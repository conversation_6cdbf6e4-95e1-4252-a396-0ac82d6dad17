package com.gametaco.app_android_fd.environment.debug

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.lifecycle.ViewModelProvider
import com.gametaco.app_android_fd.utils.log.Logger

class EnvironmentDebugActivity : ComponentActivity() {
    companion object {
        const val TAG = "EnvironmentDebugActivity"
    }
    
    private lateinit var viewModel: EnvironmentDebugViewModel
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        Logger.d(TAG, "onCreate EnvironmentDebugActivity")
        
        viewModel = ViewModelProvider(this)[EnvironmentDebugViewModel::class.java]
        
        setContent {
            val uiState by viewModel.uiState.collectAsState()
            EnvironmentDebugContent(uiState = uiState)
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        recreate()
    }
}