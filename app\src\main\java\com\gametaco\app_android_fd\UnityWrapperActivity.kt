package com.gametaco.app_android_fd

//import androidx.compose.ui.platform.AbstractComposeView

import android.content.Intent
import android.os.Bundle
import android.os.Vibrator
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.AbstractComposeView
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavHostController
import com.gametaco.app_android_fd.data.entity.APIGameScoreResult
import com.gametaco.app_android_fd.manager.ActivityManager
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.DeepLinkManager
import com.gametaco.app_android_fd.manager.HapticFeedbackType
import com.gametaco.app_android_fd.manager.HapticsManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.SystemBarState
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.utils.log.Logger
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import com.unity3d.player.UnityPlayer
import com.worldwinner.bridge.OverrideUnityActivity
import com.worldwinner.bridge.UnityAndroidBridgeAPI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import org.koin.android.ext.android.inject
import resources.R

class LauncherComposeView @JvmOverloads constructor(
    private val unityWrapperActivity: UnityWrapperActivity,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AbstractComposeView(unityWrapperActivity, attrs, defStyleAttr) {

    companion object {
        val TAG = "LauncherComposeView"
    }

    var isAttached: Boolean = false
    val onHierarchyChangeListener = object : OnHierarchyChangeListener {
            override fun onChildViewAdded(parent: View?, child: View?) {
                val name = child?.javaClass?.getName()
                if (name?.contains("unity") == true) {
                    Logger.i(TAG, "*** Unity View added: " + child)
                    // FX-3584: for reasons currently unknown, this `L` view is added when returning
                    // to the app. We don't want it, so forcibly remove it.
                    // Note: This is a hacky workaround and will stop working if Unity library
                    // changes, and the obfuscation (almost certainly) changes the class name.
                    if (name == "com.unity3d.player.L") {
                        (parent as? FrameLayout)?.removeView(child)
                        Logger.w(TAG, "Removed Unity View: " + child)
                    }
                }
            }

            override fun onChildViewRemoved(parent: View?, child: View?) {}
        }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()

        if (!isAttached) {
            val root = parent as? FrameLayout
            root?.setOnHierarchyChangeListener(onHierarchyChangeListener)
            Logger.i(TAG, "onAttachedToWindow(), root: ${root?.javaClass?.name}")
            isAttached = true
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()

        if (isAttached) {
            val root = parent as? FrameLayout
            root?.setOnHierarchyChangeListener(null)
            Logger.i(TAG, "onDetachedFromWindow(), root: ${root?.javaClass?.name}")
            isAttached = false
        }
    }

    @Composable
    override fun Content() {
        unityWrapperActivity.AppEntryPoint()
    }

//    @Composable
//    fun AppEntryPoint(){
//        val navController = rememberNavController()
//        NavManager.instance.navController = navController //this feels really bad, mmkay!?
//
//        CompositionLocalProvider(
//            LocalNavController provides navController
//        ) {
//            HandleBackPress(navController)
//            AppNavigationGraph()
//            AlertDialogPopup(
//                dialogData = AlertDialogViewModel.instance.dialogState.value,
//                onDismiss = AlertDialogViewModel.instance::dismissDialog
//            )
//        }
//    }
//
//    @Composable
//    fun HandleBackPress(navController: NavHostController) {
//        BackHandler(enabled = navController.currentBackStackEntry != null) {
//            if (!navController.navigateUp()) {
//                finishAffinity(MainUnityActivity.instance as Activity)
//            }
//        }
//    }
}


open class UnityWrapperActivity : OverrideUnityActivity(), UnityWrapperInterface {

    companion object {
        val TAG = "UnityWrapperActivity"
    }

    var navController : NavHostController? = null
    var targetObjectName : String? = ""
    var loadGameKey : String? = ""
    var startGameKey : String? = ""
    var resetKey : String? = ""
    var bridgeAPI : UnityAndroidBridgeAPI? = null

    var launcherComposeView : LauncherComposeView? = null
    var isUnityReady: Boolean = false
    val activityManager: ActivityManager by inject()

    var containerInfo: String? = null


    // Setup activity layout
    override fun onCreate(savedInstanceState: Bundle?) {
        Logger.d(TAG, "onCreate")
        super.onCreate(savedInstanceState)
        supportActionBar?.hide() // this is needed to hide the action bar
        instance = this

        setContentView(R.layout.empty_layout);

        lifecycleScope.launch(Dispatchers.Main) {
            delay(1000) //wait a second so splash screen can be seen as more than a flash

            setContentView(mUnityPlayer.frameLayout)
            mUnityPlayer.frameLayout.requestFocus()

            if(launcherComposeView == null) {
                val layout: FrameLayout = mUnityPlayer.frameLayout

                run {
                    launcherComposeView = LauncherComposeView(this@UnityWrapperActivity)
                    layout.addView(
                        launcherComposeView,
                        FrameLayout.LayoutParams.MATCH_PARENT,
                        FrameLayout.LayoutParams.MATCH_PARENT
                    )
                    hideLauncherView()
                }
            }
        }
    }


    override fun showLauncherView() {
        Logger.d(TAG, "showLauncherView")
        runOnUiThread {
            launcherComposeView?.visibility = View.VISIBLE
        }
    }

    override fun hideLauncherView() {
        Logger.d(TAG, "hideLauncherView")
        runOnUiThread {
            launcherComposeView?.visibility = View.GONE
        }
    }

    override fun onDestroy() {
        Logger.d(TAG, "onDestroy")
        super.onDestroy()
        instance = null
    }


    override fun onNewIntent(intent: Intent) {
        Logger.d(TAG, "onNewIntent")
        super.onNewIntent(intent)
        setIntent(intent)
        DeepLinkManager.instance.handleIntent(intent)
    }

    override fun onUnityPlayerUnloaded() {
        Logger.d(TAG, "onUnityPlayerUnloaded")
//        showMainActivity()
    }

    override fun returnToContainer() {
        runOnUiThread {
            Logger.d(TAG, "returnToContainer")
            onReturnToContainer()
            showLauncherView()
        }
    }


    override fun UnityNativeAPI_loadGame(loadString: String) {
        Logger.d(TAG, "UnityNativeAPI_loadGame targetObjectName: $targetObjectName loadGameKey: $loadGameKey loadString: $loadString")
        UnityPlayer.UnitySendMessage(targetObjectName, loadGameKey, loadString)
    }

    override fun UnityNativeAPI_startGameReady(startString: String) {
        Logger.d(TAG, "UnityNativeAPI_startGameReady targetObjectName: $targetObjectName startGameKey: $startGameKey startString: $startString")
        UnityPlayer.UnitySendMessage(targetObjectName, startGameKey, startString)
    }

    override fun UnityNativeAPI_reset() {
        Logger.d(TAG, "UnityNativeAPI_reset targetObjectName: $targetObjectName resetKey: $resetKey")
        UnityPlayer.UnitySendMessage(targetObjectName, resetKey, "")
    }

    override fun UnityNativeAPI_getReplayFilePath(tournamentInstanceEntryId: String) : String?
    {
        if(bridgeAPI == null)
            return null

        var path:String? = null
        try{
            path = bridgeAPI?.getReplayFilePath(tournamentInstanceEntryId)
            Logger.d(TAG,"UnityNativeAPI_getReplayFilePath: ${tournamentInstanceEntryId} path: ${path}")
        }catch (e:Exception){
            Logger.e(TAG,"UnityNativeAPI_getReplayFilePath: ${tournamentInstanceEntryId} error: ${e.message}")
        }
        return path
    }

    override fun setUnityReady(
        containerInfo: String?,
        targetObjectName: String?,
        loadGameKey: String?,
        startGameKey: String?,
        resetKey: String?,
        apiCallback: UnityAndroidBridgeAPI?
    ) {
        Logger.d(TAG, "setUnityReady: containerInfo: $containerInfo")


        instance = this
        
        this.containerInfo = containerInfo
        this.targetObjectName = targetObjectName
        this.loadGameKey = loadGameKey
        this.startGameKey = startGameKey
        this.resetKey = resetKey
        this.bridgeAPI = apiCallback
        this.isUnityReady = true


    }
    override fun UnityNativeAPI_appAction(jsonString:String){
        Logger.d(TAG,"UnityNativeAPI_appAction: $jsonString")
        if(bridgeAPI == null){
            Logger.e(TAG,"UnityNativeAPI_appAction error: bridgeAPI is null")
            return
        }

        try{
            bridgeAPI?.appAction(jsonString)
        }catch (e:Exception){
            Logger.e(TAG,"appAction error: ${e.message}")
        }
    }

    override fun UnityNativeAPI_getDailyRewardFutureTime(): String? {
        if(bridgeAPI == null){
            Logger.e(TAG,"UnityNativeAPI_getDailyRewardFutureTime error: bridgeAPI is null")
            return null
        }

        try{
            return bridgeAPI?.getDailyRewardFutureTime()
        }catch (e:Exception){
            Logger.e(TAG,"appAction error: ${e.message}")
        }
        return null
    }

    override fun startGame(startGame: String?) {
        Logger.d(TAG, "startGame")
        TournamentManager.instance.onStartTournament(startGame?:"")
    }

    override fun containerError(errorString: String?) {
        Logger.d(TAG, "ContainerError: $errorString" )
        TournamentManager.instance.onUnityContainerError(errorString)
    }

    override fun gameReady() {
        Logger.d(TAG, "gameReady")
        TournamentManager.instance.onGameReady()
    }

    override fun setLoadScreenProgress(progress: Float) {
        Logger.d(TAG, "setLoadScreenProgress: $progress")

        TournamentManager.instance.onLoadGameProgressUpdate(progress)
    }

    override fun analyticsQueueEvent(eventName: String?, eventObject: String?) {
        Logger.d(TAG, "analyticsQueueEvent: eventName: $eventName eventObject: $eventObject")
        try {
            val moshi = Moshi.Builder().add(KotlinJsonAdapterFactory()).build()
            val type = Types.newParameterizedType(Map::class.java, String::class.java, Any::class.java)
            val adapter = moshi.adapter<Map<String, Any>>(type)
            val jsonObject = adapter.fromJson(eventObject)

            jsonObject?.let {
                AnalyticsManager.instance.logEvent(
                    AnalyticsEvent(analyticsEvent = eventName?:"unknown",
                    properties = jsonObject)
                )
            }
        } catch (e: Exception) {
            Logger.d("Error parsing JSON: $e")
        }
    }

    override fun brazeEvent(eventName: String?, eventObject: String?) {
        Logger.d(TAG, "** brazeEvent: eventName: $eventName eventObject: $eventObject")
        if (eventName == null || eventObject == null) {
            Logger.d(TAG, "brazeEvent: eventName or eventObject is null")
            return
        }
        try {
            BrazeManager.instance.logEvent(eventName, JSONObject(eventObject))
        } catch (e: Exception) {
            Logger.d(TAG, "brazeEvent: Error parsing JSON: ${e.localizedMessage ?: "Error"}, eventName: $eventName")
        }
    }

    override fun areHapticsEnabled(): Boolean {

        val hapticsEnabled = PreferencesManager.instance.getIsHapticsEnabled()
        Logger.d(TAG, "areHapticsEnabled: $hapticsEnabled")
        return hapticsEnabled
    }

    override fun areHapticsSupported(): Boolean {
        try {
            val vibrator = getSystemService(VIBRATOR_SERVICE) as Vibrator
            if (vibrator.hasVibrator()) {
                Logger.d(TAG, "areHapticsSupported : true")
                return true
            }
        } catch (e: Exception) {
            Logger.d(TAG, "areHapticsSupported: ${e.localizedMessage ?: "Error"}")
            Logger.d(TAG, "areHapticsSupported : false")
            return false
        }
        Logger.d(TAG, "areHapticsSupported : false")
        return false
    }

    override fun hapticsVibrate(setting: String?) {
        Logger.d(TAG, "hapticsVibrate $setting")

        try {
            HapticsManager.instance.playEffect(HapticFeedbackType.fromType(setting?:"LightImpact"))
        } catch (e: Exception) {
            Logger.d(TAG, "hapticsVibrate: ${e.localizedMessage ?: "Error"}")
        }

    }

    override fun scoreUpdate(score: String?) {
        Logger.d(TAG, "scoreUpdate")

        val moshi = Moshi.Builder()
            .add(KotlinJsonAdapterFactory())
            .build()
        val jsonAdapter = moshi.adapter(APIGameScoreResult::class.java)
        val gameScoreResult = jsonAdapter.fromJson(score!!)!!

        if(gameScoreResult.final != null && gameScoreResult.final!!.toBoolean() && gameScoreResult.score != null)
        {
            TournamentManager.instance.onFinalScoreUpdate(gameScoreResult.score!!)
        }
    }

    override fun resetCompleted() {
        Logger.d(TAG, "resetCompleted")

        lifecycleScope.launch(Dispatchers.Main) {
            onReturnToContainer()
            showLauncherView()
        }
    }

    override fun prepareForReplay(tournamentInstanceEntryID: String?) {

        TournamentManager.instance.unityProvider?.prepareForReplay(tournamentInstanceEntryID)
    }

    override fun hideUnity(appNavigationInfo: String?, error: String?) {
        Logger.d(TAG, "hideUnity appNavigationInfo:${appNavigationInfo} error:${error}")
        TournamentManager.instance.unityProvider?.hideUnity(appNavigationInfo,error)
    }

    override fun uploadReplay(tournamentInstanceEntryID: String?, replayFilePath: String?, isUserViewable: Boolean) {
        TournamentManager.instance.unityProvider?.uploadReplay(tournamentInstanceEntryID, replayFilePath, isUserViewable)
    }

    fun onReturnToContainer() {
        Logger.d(TAG, "onReturnToContainer")
        TournamentManager.instance.unityProvider?.returnToApp()
    }

    override fun pause() {
        Logger.d(TAG, "pause")
        // Don't try pause before we have initialized - may have caused a crash early on in development.
        // https://gametacoteam.slack.com/archives/C05NK4GTX4P/p1706893958629059
        // Shant T 2-2-2024
        // I've ran into this crash a couple of times lately, and now I think it is an actual bug (as opposed to some debug issue).
        // When launching the app and quickly backgrounding it, and then returning to the app, I sometimes run into this bug.
        // I'll need to take a close look, but I think it is from the scene changing to a backgrounded state causing the app to try and paus
        // Unity when there is no game to pause (or maybe because of some threading issue?).
        // Sam V - 5-16-2025
        // We are adding daily reward as a unity feature, so we need to pause unity when not in a game.
        // Otherwise, we get an error:
        // [Execution of the command buffer was aborted due to an error during execution. Insufficient Permission (to submit GPU work from
        // background)]
        // I could not reproduce this crash - but to be safe, I have waited until unity has sent the onReady message and verified it's
        // only called from the main thread

        if(!this.isUnityReady) {
            return
        }
        mUnityPlayer.pause()
    }

    override fun resume() {
        Logger.d(TAG, "resume")
        mUnityPlayer.resume()
    }

    @Composable
    override fun AppEntryPoint() {
    }

    override fun setSystemBarState(systemBarState: SystemBarState) {

    }

//    override fun onResume() {
//        super.onResume()
//
//        //we only wait for the setUnityReady callback on the first load of the activity
//        //unityActivityLoadedCallback can be called from here for all other resumes
//        //unityActivityLoadedCallback is set to null because it's used as a mechanism to
//        //let TournamentManager know unity is in the foreground before calling loadGame
//        if(unityActivityLoadedCallback != null && waitForUnityReadyCallback == false) {
//
//            unityActivityLoadedCallback?.invoke(true)
//            unityActivityLoadedCallback = null
//        }
//
//    }

//    override fun onWindowFocusChanged(hasFocus: Boolean) {
//        super.onWindowFocusChanged(hasFocus)
//        // Additional code when the window focus changes
//        if (hasFocus) {
//
//
//            //we only wait for the setUnityReady callback on the first load of the activity
//            //unityActivityLoadedCallback can be called from here for all other resumes
//            //unityActivityLoadedCallback is set to null because it's used as a mechanism to
//            //let TournamentManager know unity is in the foreground before calling loadGame
//            if(unityActivityLoadedCallback != null && waitForUnityReadyCallback == false) {
//
//                coroutineScopes.main.launch.launch {
//
////                    delay(5000L)
//                    unityActivityLoadedCallback?.invoke(true)
//                    unityActivityLoadedCallback = null
//                }
//            }
//
//        } else {
//            // The activity has lost focus
//        }
//    }

}

