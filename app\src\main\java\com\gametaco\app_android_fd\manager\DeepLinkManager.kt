package com.gametaco.app_android_fd.manager

import android.content.Intent
import android.net.Uri
import android.os.Handler
import android.os.Looper
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.CoroutineScopes
import com.gametaco.app_android_fd.viewmodel.GamesViewModel
import com.gametaco.app_android_fd.viewmodel.LeaderboardViewModel
import com.gametaco.app_android_fd.viewmodel.PlayViewModel

class DeepLinkManager (val activityManager: ActivityManager,
                       val alertDialogManager: AlertDialogManager,
                       private val coroutineScopes: CoroutineScopes
) {


    companion object {
        const val TAG = "DeepLinkManager"
        val instance: DeepLinkManager
            get() = resolve()
    }

    var deepLinkIntent: Intent? = null

    fun tryHandleCachedDeepLinkIntent() : Boolean {

        if( deepLinkIntent == null) {
            Logger.d(TAG, "tryHandleCachedDeepLinkIntent: deepLinkIntent is null")
            return false
        }

        val action: String? = deepLinkIntent?.action
        val data: Uri? = deepLinkIntent?.data

        Logger.d(TAG, "tryHandleCachedDeepLinkIntent: action: ${action} \ndata: ${data}")

        deepLinkIntent = null
        handleUri(data)
        return true
    }

    fun handleIntent(intent : Intent) {

        val navManager = NavManager.instance

        // if we're in a state where we can't handle deep links store for later
        if(navManager.currentDestinationRoute == null ||
            navManager.currentDestinationRoute == Routes.MAINTENANCE_SCREEN ||
            navManager.currentDestinationRoute == Routes.LOGIN_SCREEN ||
            navManager.currentDestinationRoute == Routes.WELCOME_SCREEN ||
            navManager.currentDestinationRoute == Routes.GAMEPLAY_SCREEN){

            Logger.d(TAG, "handleIntent: navManager.currentDestinationRoute ${navManager.currentDestinationRoute}; caching intent");

            val action: String? = intent?.action
            val data: Uri? = intent?.data

            Logger.d(TAG, "handleIntent: action: ${action} \ndata: ${data}")

            if(action == null || data == null) {
                Logger.d(TAG, "ignoring intent as action or data are null")
                return
            }

            deepLinkIntent = intent

        } else { //else handle immediately
            val action: String? = intent?.action
            val data: Uri? = intent?.data

            Logger.d(TAG, "handleIntent: action: ${action} \ndata: ${data}")

            handleUri(data)
        }
    }

    fun handleUri(uri: Uri?) {
        Logger.d(TAG, "handleUri: uri: ${uri}")

        Handler(Looper.getMainLooper()).post {
            try {

                val queryParts: List<String>? = uri?.query?.split(",")
                val uriScheme = uri?.scheme

                when {
                    uriScheme?.lowercase() == "faceoff" -> {
                        when (uri.host?.lowercase()) {
                            "jump_to_screen" -> {

                                if (queryParts != null) {

                                    require(queryParts.size >= 1) {
                                        "queryParts size must be at least 1, but was ${queryParts.size}"
                                    }

                                    when (queryParts[0]?.lowercase()) {
                                        "gameoverviews" -> {

                                            require(queryParts.size >= 2) {
                                                "Array size must be at least 2, but was ${queryParts.size}"
                                            }

                                            if (queryParts[1] != null) {

                                                val gameId = queryParts[1]

                                                val selectedGameData = GamesViewModel.instance.getGameDataById(gameId)
                                                val selectedGameCatalogData = GamesViewModel.instance.getGCGameDataForGameId(gameId)

                                                if (selectedGameData == null || selectedGameCatalogData == null) {
                                                    alertGameIsNotAvailable()
                                                } else {
                                                    PlayViewModel.instance.setGameData(
                                                        gameData = selectedGameCatalogData,
                                                        game = selectedGameData
                                                    )
                                                    PlayViewModel.instance.setForceShowPreview(true)
                                                    NavManager.instance.navigateClearBackStack(Routes.GAMES_SCREEN)
                                                    NavManager.instance.navigate(Routes.CONTEST_SCREEN)
                                                }


                                            }
                                        }

                                        "tournamentselect" -> {

                                            require(queryParts.size >= 2) {
                                                "queryParts size must be at least 2, but was ${queryParts.size}"
                                            }

                                            if (queryParts[1] != null) {

                                                val gameId = queryParts[1]

                                                val selectedGameData = GamesViewModel.instance.getGameDataById(gameId)
                                                val selectedGameCatalogData = GamesViewModel.instance.getGCGameDataForGameId(gameId)

                                                if (selectedGameData == null || selectedGameCatalogData == null) {
                                                    alertGameIsNotAvailable()
                                                } else {
                                                    PlayViewModel.instance.setGameData(
                                                        gameData = selectedGameCatalogData,
                                                        game = selectedGameData
                                                    )
                                                    PlayViewModel.instance.setForceShowPreview(false)
                                                    NavManager.instance.navigateClearBackStack(Routes.GAMES_SCREEN)
                                                    NavManager.instance.navigate(Routes.CONTEST_SCREEN)
                                                }


                                            }
                                        }

                                        "fanduelaccountsettings" -> {

                                            when (queryParts.getOrNull(1)?.lowercase()) {

                                                "deposit" -> {
                                                    FDManager.instance.showWebviewFD(FDWebviewPage.Deposit)
                                                }
                                                "referafriend" -> {
                                                    FDManager.instance.showWebviewFD(page = null, sourceURL = "/account/referrals", title = "Earn Cash: Refer Friends")
                                                }
                                                else -> {
                                                    NavManager.instance.navigate(Routes.ACCOUNT_SCREEN)
                                                }
                                            }
                                        }

                                        "gamehistory" -> {
                                            NavManager.instance.navigate(Routes.SCORES_SCREEN)
                                        }

                                        "tournamentresult" -> {

                                            require(queryParts.size >= 2) {
                                                "queryParts size must be at least 2, but was ${queryParts.size}"
                                            }

                                            if (queryParts[1] != null) {
                                                NavManager.instance.navigate(Routes.SCORES_SCREEN)
                                                LeaderboardViewModel.instance.showLeaderboard(
                                                    queryParts[1],
                                                    false
                                                )
                                            }
                                        }

                                        "webview" -> {

                                            require(queryParts.size >= 2) {
                                                "queryParts size must be at least 2, but was ${queryParts.size}"
                                            }

                                            if (queryParts[1] != null) {

                                                val url = queryParts.getOrNull(1)
                                                val nativeFlag = queryParts.getOrNull(2)

                                                if (url != null) {

                                                    if (nativeFlag == null) {
                                                        FDManager.instance.showWebviewFD(
                                                            page = null,
                                                            sourceURL = url
                                                        )
                                                    } else if (nativeFlag?.lowercase() == "native") { //navigate outside of app
                                                        val intent = Intent(Intent.ACTION_VIEW)
                                                        intent.data = Uri.parse(url)

                                                        // Check if there is an activity available to handle this intent
                                                        if (intent.resolveActivity(activityManager.activity.packageManager) != null) {
                                                            activityManager.activity.startActivity(
                                                                intent
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        "rewards" -> {
                                            NavManager.instance.navigate(Routes.GOALS_SCREEN)
                                        }

                                        "lobby" -> {
                                            NavManager.instance.navigate(Routes.GAMES_SCREEN)
                                        }

                                        "debugconsole" -> {
                                            NavManager.instance.navigate(Routes.DEBUG_MENU_SHEET)
                                        }
                                    }
                                }
                            }

                            "goals" -> {
                                NavManager.instance.navigateClearBackStack(Routes.GOALS_SCREEN)
                            }

                            "profiles" -> {
                                NavManager.instance.navigateClearBackStack(Routes.ACCOUNT_SCREEN)
                            }

                            "games" -> {
                                NavManager.instance.navigateClearBackStack(Routes.GAMES_SCREEN)
                            }

                            "results" -> {
                                NavManager.instance.navigateClearBackStack(Routes.SCORES_SCREEN)
                            }

                            else -> {
                                Logger.w(TAG, "unsupported host ${uri.host?.lowercase()} for ${uri} ")
                            }
                        }
                    }

                    uriScheme?.lowercase() == "https" -> {
                        if(uri.host?.lowercase()?.contains("fanduel.com") == true){
                            FDManager.instance.showWebviewFD(
                                page = null,
                                sourceURL = uri.toString()
                            )
                        }
                    }

                    (uriScheme != null) -> {
                        val context = activityManager.activity
                        // Handle other URIs
                        val intent = Intent(Intent.ACTION_VIEW, uri)
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

                        if (intent.resolveActivity(context.packageManager) != null) {
                            context.startActivity(intent)
                        } else {
                            Logger.w(BrazeManager.TAG, "gotoUri: No activity found to handle URI: $uri")
                        }
                    }

                    else -> {
                        Logger.w(TAG, "unsupported uriScheme ${uri} ")
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, e.localizedMessage ?: "Unknown exception" )
            }
        }

    }

    fun alertGameIsNotAvailable(){
        alertDialogManager.showDialog("Game Not Available","The game is not available, please try other games","Confirm",{})
    }
}