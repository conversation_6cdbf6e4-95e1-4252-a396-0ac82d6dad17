package com.gametaco.app_android_fd.manager.analytics

import android.app.Application
import com.amplitude.api.Amplitude
import com.amplitude.api.AmplitudeClient
import com.amplitude.api.Identify
import com.gametaco.app_android_fd.BuildConfig
import com.gametaco.app_android_fd.MainActivity
import com.gametaco.app_android_fd.MainApplication
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.data.LoggerInitializerImpl
import com.gametaco.app_android_fd.data.api.APIMe
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.GeoComplyManager
import com.gametaco.app_android_fd.manager.OnSessionChangedEvent
import com.gametaco.app_android_fd.utils.CoroutineScopes
import com.gametaco.app_android_fd.utils.log.Logger
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import java.util.UUID


enum class AnalyticsSourceName(val value: String) {
    Play("Play"),
    Rewards("Rewards"),
    Account("Account"),
    Scores("Scores"),
    Splash("Splash"),
    None("None"),
}

class AnalyticsManager(private val coroutineScopes: CoroutineScopes) : AnalyticsManagerProtocol {
    companion object {
        val instance: AnalyticsManager get() = resolve()
        const val TAG = "AnalyticsManager"
    }


    fun initAnalytics() {
        Logger.d(MainActivity.TAG, "Initializing Amplitude")
        EventBus.getDefault().register(this);
        amplitude?.initialize(MainApplication.context, AppEnv.current.amplitude_api_key)
            ?.enableForegroundTracking(MainApplication.context.applicationContext as Application)

        systemProperties.clear()
        setSystemProperty(AnalyticsSystemProperty.AppId, BuildConfig.APPLICATION_ID)
        setSystemProperty(AnalyticsSystemProperty.Platform, "Android")
        setSystemProperty(AnalyticsSystemProperty.Language, "en")
        setSystemProperty(AnalyticsSystemProperty.AppVersion, BuildConfig.VERSION_NAME)
        setSystemProperty(AnalyticsSystemProperty.ScreenSource, AnalyticsSourceName.None)
        setSystemProperty(AnalyticsSystemProperty.SiteVersion, "native")
        setSystemProperty(AnalyticsSystemProperty.SitePlatform, "android")
        setSystemProperty(AnalyticsSystemProperty.AppName, "faceoff")
        setSystemProperty(AnalyticsSystemProperty.AndroidDistributionMethod, "self_dist")
        setSystemProperty(AnalyticsSystemProperty.Product, "Faceoff")
        setSystemProperty(AnalyticsSystemProperty.Jurisdiction, "usa")
        setSystemProperty(AnalyticsSystemProperty.NationalJurisdiction, "nj")

    }

    val amplitude: AmplitudeClient? = Amplitude.getInstance()

    private val systemProperties = mutableMapOf<String, Any>()

    override fun setUserProperties(user: APIMe) {
        val identify = Identify()
        amplitude?.setUserId(user.fanduel_id.toString())
        identify.set("User ID", user.fanduel_id)
        identify.set("Username", user.username)
        amplitude?.identify(identify)
        debugLog("setUserProperties: $user")
        EventBus.getDefault().post(OnAmplitudeUserChangedEvent())
    }

    override fun setUserProperty(property: AnalyticsUserProperty, value: Any) {
        val identify = Identify()
        identify.set(property.rawValue, value)
        amplitude?.identify(identify)
        debugLog("setUserProperty: ${property.rawValue} = $value")
    }

    override fun clearUserProperties() {
        amplitude?.setUserId(null)
        amplitude?.clearUserProperties()
        amplitude?.regenerateDeviceId()
        debugLog("clearUserProperties")
    }

    override fun setSystemProperty(property: AnalyticsSystemProperty, value: Any) {
        systemProperties[property.rawValue] = value
        debugLog("[setSystemProperty: ${property.rawValue} = $value")
    }

    override fun logEvent(event: AnalyticsEvent) {

        setJurisdictionProperties()

        if (AuthenticationManager.instance.hasPriorLogin) {
            setSystemProperty(AnalyticsSystemProperty.LoginStatus, "logged_in")
        } else {
            setSystemProperty(AnalyticsSystemProperty.LoginStatus, "logged_out")
        }

        var properties : Map<String, Any> = systemProperties
        if (event.properties != null) {
            properties = properties + event.properties
        }

        val jsonProperties = JSONObject(properties)
        amplitude?.logEvent(event.analyticsEvent, jsonProperties)

        debugLog(event.analyticsEvent + " " + jsonProperties.toString())
    }

    fun setJurisdictionProperties() {
        val countryCode = GeoComplyManager.instance.latestCountryCode
        countryCode?.let {
            val modifiedCountryCode = when (it.lowercase()) {
                "us" -> "usa"
                "ca" -> "canada"
                else -> it.lowercase()
            }
            setSystemProperty(AnalyticsSystemProperty.Jurisdiction, modifiedCountryCode)
        }

        val regionCode = GeoComplyManager.instance.latestRegionCode
        regionCode?.let {
            setSystemProperty(AnalyticsSystemProperty.NationalJurisdiction, it.lowercase())
        }
    }

    fun getDeviceId() : String? {
        return amplitude?.deviceId
    }

    fun getSessionId() : Long? {
        return amplitude?.sessionId
    }

    private fun debugLog(message: String) {
        if (LoggerInitializerImpl.loggingEnabled) {
            Logger.d(TAG, message)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSessionChangedEventHandler(event: OnSessionChangedEvent) {
        Logger.d(TAG, "onSessionChangedEventHandler")

        if(!event.loggedIn) {
            clearUserProperties()
            EventBus.getDefault().post(OnAmplitudeUserChangedEvent())
        }
    }

}


data class OnAmplitudeUserChangedEvent(val stub: Boolean = true)