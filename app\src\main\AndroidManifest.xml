<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES"/>
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" tools:ignore="QueryAllPackagesPermission"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30"/>
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"/>
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28"
        tools:replace="android:maxSdkVersion" />

    <application
        tools:replace="android:label,android:fullBackupContent,android:allowBackup"
        android:name=".MainApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/fanduel_app_name"
        android:supportsRtl="true"
        tools:targetApi="34"
        android:theme="@style/AppTheme"
        android:enableOnBackInvokedCallback="false"
        android:usesCleartextTraffic="true">

        <!-- Required: set your sentry.io project identifier (DSN) -->
        <meta-data android:name="io.sentry.dsn" android:value="https://<EMAIL>/4507133558849536" />
        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
        <meta-data android:name="io.sentry.traces.user-interaction.enable" android:value="true" />
        <!-- enable screenshot for crashes -->
        <meta-data android:name="io.sentry.attach-screenshot" android:value="true" />
        <!-- enable view hierarchy for crashes -->
        <meta-data android:name="io.sentry.attach-view-hierarchy" android:value="true" />
        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
        <meta-data android:name="io.sentry.traces.sample-rate" android:value="1.0" />
        <!-- enable profiling when starting transactions, adjust in production env -->
        <meta-data android:name="io.sentry.traces.profiling.sample-rate" android:value="0.0" />


        <activity
            android:name="com.gametaco.app_android_fd.MainActivity"
            android:screenOrientation="portrait"
            android:exported="true"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="faceoff" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" />
                <data android:host="www.fanduel.com" />
                <data android:host="fanduel.com" />
                <data android:pathPrefix="/beat-the-pros/"/>
            </intent-filter>

        </activity>
        <activity
            android:name=".webview.trustly.LightBoxActivity"
            android:exported="true" />
        <activity
            android:name=".webview.trustly.RedirectActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="sdkdemo" />
            </intent-filter>
        </activity>
        <activity
            android:name=".webview.trustly.ResultActivity"
            android:exported="true" />
        <activity
            android:name="com.fanduel.core.libs.modalpresenter.ModalActivity"
            android:theme="@style/CustomWebViewTheme"
            tools:replace="android:theme"/>
        <service android:name="com.braze.push.BrazeFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
    </application>
</manifest>