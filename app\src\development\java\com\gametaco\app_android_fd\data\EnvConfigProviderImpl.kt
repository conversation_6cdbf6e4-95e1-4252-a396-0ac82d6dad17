package com.gametaco.app_android_fd.data

import com.gametaco.app_android_fd.data.EnvConfigDevelopment.EnvConfigDev
import com.gametaco.app_android_fd.data.EnvConfigDevelopment.EnvConfigQa
import com.gametaco.app_android_fd.manager.PreferencesManager

object EnvConfigProviderImpl : EnvConfigProvider {

    private val preferencesManager: PreferencesManager
        get() = PreferencesManager.instance

    override val envConfig: EnvConfig
        get() {
            val env = preferencesManager.getAppENV()
            val environmentSpec = preferencesManager.getEnvironmentSpec()

            return when(env){
                ENV.QA.name -> {
                    when (environmentSpec) {
                        null -> EnvConfigQa(environmentSpec = EnvironmentSpec.Default)
                        else -> EnvConfigQa(environmentSpec = environmentSpec)
                    }
                }
                ENV.DEV.name -> {
                    when (environmentSpec) {
                        null -> EnvConfigDev(environmentSpec = EnvironmentSpec.Default)
                        else -> EnvConfigDev(environmentSpec = environmentSpec)
                    }
                }
                ENV.PROD.name -> EnvConfigs.PROD
                else -> {
                    EnvConfigQa(environmentSpec = EnvironmentSpec.Default)
                }
            }
        }

    override fun updateEnvironmentSpec(environmentSpec: EnvironmentSpec): EnvironmentSpec? {
        val currentEnvironmentSpec = preferencesManager.getEnvironmentSpec()
        if (environmentSpec == currentEnvironmentSpec) {
            return null
        }

        preferencesManager.setEnvironmentSpec(environmentSpec.jsonString)
        return environmentSpec
    }
}