package com.gametaco.app_android_fd.ui.components

//import io.ktor.http.quote
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.gametaco.app_android_fd.LocalClipboardManager
import com.gametaco.app_android_fd.LocalToastManager
import com.gametaco.app_android_fd.MainApplication.Companion.context
import com.gametaco.app_android_fd.data.entity.APIRefundReason
import com.gametaco.app_android_fd.data.entity.APITournamentBrand
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntry
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntryExpandedResultScoreItem
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntryResult
import com.gametaco.app_android_fd.data.entity.APITournamentUserStatus
import com.gametaco.app_android_fd.data.entity.EntryFee
import com.gametaco.app_android_fd.data.entity.Prize
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.ui.theme.AppFont
import com.gametaco.app_android_fd.ui.theme.theme_light_outline_variant2
import com.gametaco.app_android_fd.ui.theme.theme_light_primary
import com.gametaco.app_android_fd.utils.formatEST
import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.app_android_fd.utils.toDollarString
import com.gametaco.app_android_fd.utils.toDollarWithCommaString
import com.gametaco.app_android_fd.utils.toNumberWithCommaString
import com.gametaco.app_android_fd.viewmodel.LeaderboardViewModel
import com.gametaco.utilities.STR
import resources.R

@Composable
fun ExpandableContestDetails(
    padding: PaddingValues,
    isExpanded: Boolean,
    data: APITournamentInstanceEntry
)
{
    val clipboardManager = LocalClipboardManager.current
    val toastManager = LocalToastManager.current

    val enterTransition = remember {
        expandVertically(
            expandFrom = Alignment.Top,
            animationSpec = tween(1000)
        ) //+ fadeIn(initialAlpha = .3f, animationSpec = tween(1000))
    }
    val exitTransition = remember {
        shrinkVertically(
            shrinkTowards = Alignment.Top,
            animationSpec = tween(1000)
        ) + fadeOut(animationSpec = tween(1000))
    }

    AnimatedVisibility(
        visible = isExpanded,
        enter = enterTransition,
        exit = exitTransition
    ) {
        Column (
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.padding(padding)
        )
        {

            if(!data.hasBeenRefunded()){
                //Limited Entry Contests
                Box {
                    ContentBox(
                        titlePaddingValues = PaddingValues(horizontal = 16.dp, vertical = 14.dp),
                        titleComposable = {
                            ContentTitle(
                                style = TextStyle(
                                    fontFamily = AppFont.ProximaNova,
                                    fontWeight = FontWeight.W700,
                                    fontSize = 16.sp,
                                    lineHeight = 20.sp
                                ),
                                title = if(data.maximum_slots != null) STR(R.string.limited_entry_contests) else STR(R.string.unlimited_player_contests)
                            )
                        },
                        bodyComposable = {
                            val maximum_entries_per_player = data.maximum_entries_per_player ?: -1
                            Text(
                                modifier = Modifier
                                    .padding(horizontal = 4.dp),
                                style = TextStyle(
                                    fontFamily = AppFont.ProximaNova,
                                    fontWeight = FontWeight.W400,
                                    fontSize = 12.sp,
                                    lineHeight = 15.sp
                                ),
                                text =  if(data.brand == APITournamentBrand.FIFTY_FIFTY) STR(R.string.survivor_contest_desc)
                                        else if(data.maximum_slots != null) STR(R.string.these_contests_have_a_limited_number_of_entry_slots)
                                        else if(maximum_entries_per_player > 1) STR(R.string.unlimited_contests,maximum_entries_per_player)
                                        else STR(R.string.unlimited_contests_one_entry)
                            )
                        }
                    )
                }
            }

            fun createLeaderboardSummaryItemOnClick(desc: String, value: String): () -> Unit = {
                clipboardManager.copyToClipboard(value)
                toastManager.showToast("${desc} copied to clipboard")
            }
            val playersCountStr = if(data.maximum_slots == null){
                "∞"
            }else if(data.refund_reason == APIRefundReason.autoBooted.rawValue){
                "${data.maximum_slots}"
            }else{
                "${Math.max(data.current_slots_count,1)} of ${data.maximum_slots}"
            }
            val entryFee = data.entry_fee?.toDouble()

            val entryFeeString : String? = if (entryFee != null) {
                if (entryFee > 0) {
                    entryFee.toDollarWithCommaString()
                } else {
                    STR(R.string.free_caps)
                }
            } else {
                null
            }

            val leaderboardSummaryItems: List<@Composable ()->Unit> = listOf(
                {LeaderboardSummaryItemView(desc = context.getString(R.string.game), value = data.game_display_name)},
                {LeaderboardSummaryItemView(desc = context.getString(R.string.players), value =  playersCountStr)},
                {LeaderboardSummaryItemView(desc = context.getString(R.string.entry_fee), value = entryFeeString ?: "", entryWaived = data.ticket_info != null, discountedValue = data.discountedEntryFee?.toDollarWithCommaString())},
                {LeaderboardSummaryItemView(desc = context.getString(R.string.start_time), value = data.started_at?.toDate()?.formatEST("MM/dd/yyyy h:mm:ss a") ?: "")},
                {LeaderboardSummaryItemView(desc = context.getString(R.string.end_time), value = data.ended_at?.toDate()?.formatEST("MM/dd/yyyy h:mm:ss a") ?: "Pending")},
                {
                    val desc = context.getString(R.string.contest_id)
                    val value = data.tournament_instance_id
                    val leaderboardOnCopy: () -> Unit = {
                        toastManager.showToast("$desc copied to clipboard")
                        val stringData = "$desc: $value"
                        clipboardManager.copyToClipboard(stringData)
                    }

                    LeaderboardSummaryItemView(
                        desc = desc,
                        value = value,
                        onCopy = leaderboardOnCopy,
                        modifier = Modifier.clickable(
                            onClick = createLeaderboardSummaryItemOnClick(desc = desc, value = value),
                        )
                    )
                },
                {
                    val desc = context.getString(R.string.reference_id)
                    val value = data.reference_id ?: data.id
                    val leaderboardOnCopy: () -> Unit = {
                        toastManager.showToast("$desc copied to clipboard")
                        val stringData = "$desc: $value"
                        clipboardManager.copyToClipboard(stringData)
                    }

                    LeaderboardSummaryItemView(
                        desc = desc,
                        value = value,
                        onCopy = leaderboardOnCopy,
                        modifier = Modifier.clickable(
                            onClick = createLeaderboardSummaryItemOnClick(
                                desc = desc,
                                value = value
                            ),
                        )
                    )
                },
            )

//            val prizes:List<Prize> = listOf(
//                Prize(start = 1,end = 1, prize_amount = "100"),
//                Prize(start = 2,end = 2, prize_amount = "800"),
//                Prize(start = 3,end = 3, prize_amount = "600"),
//                Prize(start = 4,end = 10, prize_amount = "300"),
//                Prize(start = 11,end = 50, prize_amount = "100"),
//                Prize(start = 51,end = 100, prize_amount = "10"),
//            )
            val prizes = PreferencesManager.instance.getTournamentPrizesCache(data.tournament_id)

            if(prizes != null && data.maximum_slots != 2){
                val leaderboardPrizeItems: MutableList<@Composable ()->Unit> = mutableListOf()

                var totalPrize = 0f
                for (prize in prizes){
                    totalPrize += prize.prize_amount.toFloat() * (prize.end - prize.start + 1)
                }
                leaderboardPrizeItems.add({
                    LeaderboardPrizeItemView(prize = Prize(start = 0,end = 0, prize_amount = totalPrize.toString()))
                })
                for (prize in prizes) {
                    leaderboardPrizeItems.add({
                        LeaderboardPrizeItemView(prize = prize)
                    })
                }

                // Prizes
                Box{
                    ContentBox(
                        titlePaddingValues = PaddingValues(horizontal = 16.dp, vertical = 14.dp),
                        titleComposable = {
                            ContentTitle(
                                style = TextStyle(
                                    fontFamily = AppFont.ProximaNova,
                                    fontWeight = FontWeight.W700,
                                    fontSize = 16.sp,
                                    lineHeight = 20.sp
                                ),
                                title = STR(R.string.prizes)
                            )
                        },
                        bodyComposable = {
                            ContentListBody(
                                listElements = leaderboardPrizeItems,
                                dividerPadding = PaddingValues(),
                                elementSpacingHeight = 0.dp,
                                precedingSpacerHeight = 0.dp
                            )
                        },
                        bodyPaddingValues = PaddingValues(start = 16.dp)
                    )
                }
            }
            // Contest Summary
            Box{
                ContentBox(
                    titlePaddingValues = PaddingValues(horizontal = 16.dp, vertical = 14.dp),
                    titleComposable = {
                        ContentTitle(
                            style = TextStyle(
                                fontFamily = AppFont.ProximaNova,
                                fontWeight = FontWeight.W700,
                                fontSize = 16.sp,
                                lineHeight = 20.sp
                            ),
                            title = STR(R.string.summary)
                        )
                    },
                    bodyComposable = {
                        ContentListBody(
                            listElements = leaderboardSummaryItems,
                            dividerPadding = PaddingValues(),
                            elementSpacingHeight = 0.dp,
                            precedingSpacerHeight = 0.dp
                        )
                    },
                    bodyPaddingValues = PaddingValues(start = 16.dp)
                )
            }

        }
    }
}

@Composable
fun ExpandableScoringBreakdown(
    isExpanded: Boolean,
    subScores: MutableMap<String, Pair<APITournamentInstanceEntryExpandedResultScoreItem?, APITournamentInstanceEntryExpandedResultScoreItem?>>
)
{
    val enterTransition = remember {
        expandVertically(
            expandFrom = Alignment.Top,
            animationSpec = tween(1000)
        )
    }
    val exitTransition = remember {
        shrinkVertically(
            shrinkTowards = Alignment.Top,
            animationSpec = tween(1000)
        ) + fadeOut(animationSpec = tween(1000))
    }

    AnimatedVisibility(
        visible = isExpanded,
        enter = enterTransition,
        exit = exitTransition
    ) {
        val leaderboardSummaryItems: MutableList<@Composable ()->Unit> = mutableListOf(
            {}, //Empty at the start to add preceding divider
        )

        for (score in subScores) {
            leaderboardSummaryItems.add {
                // Falls back to min_value to handle comparing negative subScores
                // This value is not displayed
                val score1 = score.value.first?.value ?: Int.MIN_VALUE
                val score2 = score.value.second?.value ?: Int.MIN_VALUE

                val desc1 = score.value.first?.description
                val desc2 = score.value.second?.description

                ScoreBreakdownItemView(
                    scoreTitle = score.key,
                    value1 = score.value.first?.value?.toNumberWithCommaString(),
                    value1Desc = desc1,
                    showStar1 = score1 > score2,
                    value2 = score.value.second?.value?.toNumberWithCommaString(),
                    value2Desc = desc2,
                    showStar2 = score2 > score1
                )
            }
        }

        // Scoring breakdown
        ContentListBody(
            listElements = leaderboardSummaryItems,
            dividerPadding = PaddingValues(),
            elementSpacingHeight = 0.dp,
            precedingSpacerHeight = 0.dp,
            dividerColor = theme_light_outline_variant2
        )
    }
}

@Composable
fun LeaderboardSummaryItemView(
    desc: String,
    value: String,
    onCopy: (() -> Unit)? = null,
    entryWaived:Boolean = false,
    discountedValue: String? = null,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(42.dp)
            .padding(end = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    )
    {
        Text(
            text = desc,
            style = MaterialTheme.typography.bodySmall
        )
        Spacer(modifier = Modifier.width(32.dp))
        if(entryWaived == false){
            Row(
                modifier = Modifier.weight(1f),
            ) {
                Text(
                    text = value,
                    style = MaterialTheme.typography.bodySmall,
                    textAlign = TextAlign.Right,
                    maxLines = 2,
                    modifier = Modifier.weight(1f),
                )
                if (onCopy != null) {
                    Image(
                        imageVector = Icons.Default.ContentCopy,
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                onCopy()
                            }
                            .padding(vertical = 4.dp)
                            .padding(start = 4.dp)

                    )
                }
            }
        }else{
            Row(horizontalArrangement = Arrangement.End, verticalAlignment = Alignment.CenterVertically,modifier = Modifier.weight(1f)){
                val newValue = discountedValue ?: "FREE"
                Text(
                    text = newValue,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodySmall,
                    overflow = TextOverflow.Visible,
                    softWrap = false,
                    color = Color(0xff128000)
                )
                Spacer(modifier = Modifier.width(2.dp))
                Box(contentAlignment = Alignment.Center){
                    Text(
                        text = value,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodySmall,
                        overflow = TextOverflow.Visible,
                        softWrap = false,
                        color = Color(0x33131433)
                    )
                    val textMeasurer = rememberTextMeasurer()
                    val textLayoutResult = textMeasurer.measure(
                        text = AnnotatedString(value),
                        style = MaterialTheme.typography.bodySmall
                    )

                    val textWidth = with(LocalDensity.current) { textLayoutResult.size.width.toDp() }
                    val textHeight = with(LocalDensity.current) { textLayoutResult.size.height.toDp() }

                    Canvas(modifier = Modifier
                        .width(textWidth)
                        .height(textHeight)
                    ) {
                        val y = size.height / 2  // middle of the text
                        val lineHeight = size.height * 0.04f
                        drawRect(
                            color = Color.Black,
                            topLeft = Offset(0f, y - lineHeight / 2),
                            size = Size(textLayoutResult.size.width.toFloat(), lineHeight)
                        )
                    }
                }
            }
        }

    }
}
@Composable
fun LeaderboardPrizeItemView(
    prize: Prize,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(42.dp)
            .padding(end = 16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    )
    {

        val start = prize.start
        val end = prize.end
        val range = end - start

        Box(modifier = Modifier
            .height(32.dp)
            .width(if(prize.start == 0) 128.dp else if (prize.end >= 10) 64.dp else 32.dp)) {
            val medal: Int? = when(prize.start){
                1 -> R.drawable.ic_medal_gold
                2 -> R.drawable.ic_medal_silver
                3 -> R.drawable.ic_medal_bronze
                else -> null
            }

            if (medal != null){
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = painterResource(medal),
                    contentDescription = STR(R.string.prize_medal),
                )
            }

            Text(
                text = if(start == 0) "Total Prize Pool" else if (range == 0) start.toString() else "$start - $end",
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold),
                modifier = Modifier.align(Alignment.CenterStart).padding(start = if(start == 0) 0.dp else if(medal!= null && range > 0) 2.dp else 12.dp),
                overflow = TextOverflow.Visible,
                maxLines = 1
            )
        }

        Spacer(modifier = Modifier.width(32.dp))

        Text(
            text = prize.prize_amount.toDollarWithCommaString(true),
            style = if(prize.start > 0 )MaterialTheme.typography.bodySmall else MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold),
            textAlign = TextAlign.Right,
            maxLines = 2
        )
    }
}
@Composable
fun ScoreBreakdownItemView(
    scoreTitle: String,
    value1: String?,
    value1Desc: String?,
    showStar1: Boolean,
    value2: String?,
    value2Desc: String?,
    showStar2: Boolean,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(52.dp)
            .padding(horizontal = 16.dp)
    )
    {
        Column (
            horizontalAlignment = Alignment.Start,
            verticalArrangement = Arrangement.spacedBy(2.dp, alignment = Alignment.CenterVertically),
            modifier = Modifier.align(Alignment.CenterStart)
        ) {
            Text(
                text = value1 ?: "-",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.secondary
            )
            if (value1 != null && !value1Desc.isNullOrEmpty())
                Text(
                    text = value1Desc,
                    style = MaterialTheme.typography.labelMedium
                )
        }

        // * scoreTitle *
        Box (
            modifier = Modifier
                .width(96.dp)
                .padding(horizontal = 3.dp)
                .align(Alignment.Center)
        ){
            if(showStar1){
                val altPrimaryColor = Color(0xFF005FC8)
                Image(
                    painter = painterResource(R.drawable.ic_star_app),
                    contentDescription = null,
                    colorFilter = ColorFilter.tint(color = altPrimaryColor),
                    modifier = Modifier
                        .size(16.dp)
                        .align(Alignment.CenterStart)
                        .offset((-16).dp, 0.dp)
                )
            }

            Text(
                text = scoreTitle.uppercase(),
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.inverseOnSurface,
                textAlign = TextAlign.Center,
                maxLines = 2,
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.Center)
            )

            if(showStar2){
                Image(
                    painter = painterResource(R.drawable.ic_star_app),
                    contentDescription = null,
                    colorFilter = ColorFilter.tint(color = MaterialTheme.colorScheme.secondary),
                    modifier = Modifier
                        .size(16.dp)
                        .align(Alignment.CenterEnd)
                        .offset(16.dp, 0.dp)
                )
            }
        }

        Column (
            horizontalAlignment = Alignment.End,
            verticalArrangement = Arrangement.spacedBy(2.dp, alignment = Alignment.CenterVertically),
            modifier = Modifier.align(Alignment.CenterEnd)
        ) {
            Text(
                text = value2 ?: "-",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.secondary
            )
            if (value2 != null && !value2Desc.isNullOrEmpty()){
                Text(
                    text = value2Desc,
                    style = MaterialTheme.typography.labelMedium
                )
            }
        }
    }
}

@Composable
fun KeyStatsItemView(
    statTitle: String,
    value1: String?,
    value2: String?,
    modifier: Modifier = Modifier,
) {
    val isWinnings = statTitle == "WINNINGS"
    val isReplay = statTitle == "VIDEO REPLAYS"
    val winnings1 = if (isWinnings && value1 != null) value1.removePrefix("$").toFloatOrNull() else null
    val winnings2 = if (isWinnings && value2 != null) value2.removePrefix("$").toFloatOrNull() else null
    val hasWinnings1 = winnings1 != null && winnings1 > 0
    val hasWinnings2 = winnings2 != null && winnings2 > 0

    val backgroundColor = if (isWinnings) MaterialTheme.colorScheme.tertiaryContainer
        else Color(0xFFF7FBFF)

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(52.dp)
            .background(backgroundColor)
            .padding(horizontal = 16.dp)
    )
    {
        if(isReplay){
            Box(contentAlignment = Alignment.Center, modifier = Modifier.align(Alignment.CenterStart)){
                CommonButton(title = "", width = 82f, height = 34f, fillColor = Color(0xFF005FC8),textColor =  Color(0xFFFFFFFF),borderColor = Color(0xFF005FC8)) {
                    LeaderboardViewModel.instance.showReplay(value1, false)
                }
                Row(horizontalArrangement = Arrangement.SpaceEvenly){
                    Image(
                        painter = painterResource(R.drawable.ic_replays),
                        contentDescription = null,
                        modifier = Modifier
                            .size(14.dp)
                            .align(Alignment.CenterVertically),
                        colorFilter = ColorFilter.tint(Color.White)
                    )
                    Spacer(modifier = Modifier.width(8.dp))

                    Text("Watch", style = MaterialTheme.typography.bodySmall,color = Color.White, textAlign = TextAlign.Center,modifier = Modifier.offset(0.dp,1.dp))
                }
            }
        }else{
            Text(
                text = value1 ?: if (isWinnings) "$0" else "-",
                style = MaterialTheme.typography.bodyMedium,
                color = if (isWinnings && value1 != null && hasWinnings1) MaterialTheme.colorScheme.tertiary
                else MaterialTheme.colorScheme.secondary,
                modifier = Modifier.align(Alignment.CenterStart)
            )
        }

        Box (modifier = Modifier
            .width(96.dp)
            .align(Alignment.Center)
        ){
            Text(
                text = statTitle.uppercase(),
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = if (isWinnings || isReplay) FontWeight.Bold else FontWeight.Normal
                ),
                color = MaterialTheme.colorScheme.inverseOnSurface,
                textAlign = TextAlign.Center,
                maxLines = 2,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 3.dp)
                    .align(Alignment.Center)
            )
        }

        if(isReplay){
            Box(contentAlignment = Alignment.Center, modifier = Modifier.align(Alignment.CenterEnd)){
                CommonButton(title = "", width = 82f, height = 34f, fillColor = Color(0xFF005FC8),textColor =  Color(0xFFFFFFFF),borderColor = Color(0xFF005FC8)) {
                    LeaderboardViewModel.instance.showReplay(value2, true)
                }
                Row(horizontalArrangement = Arrangement.SpaceEvenly){
                    Image(
                        painter = painterResource(R.drawable.ic_replays),
                        contentDescription = null,
                        modifier = Modifier
                            .size(14.dp)
                            .align(Alignment.CenterVertically),
                        colorFilter = ColorFilter.tint(Color.White)
                    )
                    Spacer(modifier = Modifier.width(8.dp))

                    Text("Watch", style = MaterialTheme.typography.bodySmall,color = Color.White, textAlign = TextAlign.Center,modifier = Modifier.offset(0.dp,1.dp))
                }
            }
        }else{
            Text(
                text = value2 ?: if (isWinnings) "$0" else "-",
                style = MaterialTheme.typography.bodyMedium,
                color = if (isWinnings && value2 != null && hasWinnings2) MaterialTheme.colorScheme.tertiary
                else MaterialTheme.colorScheme.secondary,
                modifier = Modifier.align(Alignment.CenterEnd)
            )
        }
    }

    HorizontalDivider(
        modifier = Modifier.fillMaxWidth(),
        thickness = 1.dp,
        color = if (isWinnings) MaterialTheme.colorScheme.outline
            else MaterialTheme.colorScheme.outlineVariant
    )
}

@Composable
fun PlayerScoreMarquee(
    player1Id:String,
    player1Name: String,
    player1Score: String,
    player1Placement: Int?,
    player1Icon: String?,
    player2Id:String?,
    player2Name: String?,
    player2Score: String?,
    player2Placement: Int?,
    player2Icon: String?,
    player2Status:APITournamentUserStatus? = null,
    game_mode_id:String? = null,
    modifier: Modifier = Modifier,
) {
    val isTwoPlayers = player2Name != null && player2Score != null
    val altPrimaryColor = Color(0xFF005FC8)

    //Background row
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(52.dp)
            .background(
                MaterialTheme.colorScheme.background,
                RoundedCornerShape(
                    topStart = 4.dp,
                    topEnd = 4.dp,
                    bottomStart = if (isTwoPlayers) 0.dp else 6.dp,
                    bottomEnd = if (isTwoPlayers) 0.dp else 6.dp,
                )
            )
    ) {
        // 2-tone background
        if (isTwoPlayers) {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(0.5f)
                    .align(Alignment.CenterEnd)
                    .background(
                        MaterialTheme.colorScheme.surface,
                        RoundedCornerShape(topEnd = 6.dp)
                    )
            )
        }

        // Center graphic
        Image(
            alignment = Alignment.Center,
            painter = painterResource(R.drawable.results_banner_middle),
            contentDescription = null,
            contentScale = ContentScale.FillHeight,
            modifier = Modifier
                .fillMaxHeight()
                .align(Alignment.Center)
        )

        // Versus graphic if 2 players
        if (isTwoPlayers) {
            Image(
                alignment = Alignment.CenterEnd,
                painter = painterResource(R.drawable.ic_versus),
                contentDescription = null,
                modifier = Modifier
                    .size(32.dp)
                    .align(Alignment.Center)
            )
        }

        // Start slash
        Image(
            alignment = Alignment.Center,
            painter = painterResource(R.drawable.results_banner_corner),
            contentDescription = null,
            contentScale = ContentScale.FillHeight,
            modifier = Modifier
                .fillMaxHeight()
                .clip(
                    RoundedCornerShape(
                        topStart = 6.dp,
                        bottomStart = if (isTwoPlayers) 0.dp else 6.dp
                    )
                )
                .align(Alignment.CenterStart)
        )

        // End slash
        Image(
            alignment = Alignment.Center,
            painter = painterResource(R.drawable.results_banner_corner),
            contentDescription = null,
            contentScale = ContentScale.FillHeight,
            modifier = Modifier
                .fillMaxHeight()
                .clip(
                    RoundedCornerShape(
                        topEnd = 6.dp,
                        bottomEnd = if (isTwoPlayers) 0.dp else 6.dp
                    )
                )
                .graphicsLayer { rotationY = 180f }
                .align(Alignment.CenterEnd)
        )

        Box(
            modifier = modifier
                .fillMaxSize()
                .padding(horizontal = 3.dp),
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth(0.5f)
                    .align(Alignment.CenterStart)
                    .padding(
                        start = if (player1Placement == null) 6.dp else 0.dp,
                        end = if (isTwoPlayers) 16.dp else 10.dp
                    )
                    .offset(x = if (player1Placement != null) -(6).dp else 0.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                val medal1: Painter? = when (player1Placement) {
                    1 -> painterResource(id = R.drawable.ic_medal_gold)
                    2 -> painterResource(id = R.drawable.ic_medal_silver)
                    3 -> painterResource(id = R.drawable.ic_medal_bronze)
                    else -> null
                }
                if (player1Placement != null && medal1 != null) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .height(32.dp)
                            .offset(x = 6.dp)
                    ) {
                        Image(
                            alignment = Alignment.Center,
                            painter = medal1,
                            contentDescription = null,
                            contentScale = ContentScale.FillHeight
                        )
                        Text(
                            text = player1Placement.toString(),
                            style = MaterialTheme.typography.headlineMedium.copy(
                                color = Color(0xFF05285A),
                                fontWeight = FontWeight.Bold
                            ),
                        )
                    }
                }

                // Player 1 icon
                Box(
                    modifier = Modifier
                        .padding(1.dp)
                        .border(1.dp, MaterialTheme.colorScheme.surface, CircleShape)
                ){
                    if (player1Icon != null) {
                        AsyncImage(
                            model = player1Icon,
                            contentDescription = null,
                            error = painterResource(R.drawable.ic_default_account),
                            contentScale = ContentScale.Crop,
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape)
                                .clickable {
                                    LeaderboardViewModel.instance.showProfile(
                                        player1Id,
                                        game_mode_id
                                    )
                                }
                        )
                    } else {
                        Image(
                            alignment = Alignment.Center,
                            painter = painterResource(
                                if (AuthenticationManager.instance.isGuest)
                                    R.drawable.ic_fanduel_account else R.drawable.ic_default_account
                            ), //Players Profile Img
                            contentDescription = null,
                            modifier = Modifier.size(32.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.width(4.dp))
                Column (
                    horizontalAlignment = Alignment.Start,
                    verticalArrangement = Arrangement.spacedBy(2.dp, Alignment.CenterVertically)
                ) {
                    AutosizeText(
                        text = player1Name,
                        style = MaterialTheme.typography.labelLarge,
                        maxLines = 1,
                        color = altPrimaryColor,
                        modifier = Modifier.clickable {
                            LeaderboardViewModel.instance.showProfile(player1Id,game_mode_id)
                        }
                    )
                    AutosizeText(
                        text = player1Score,
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Black
                        ),
                        maxLines = 1,
                        color = altPrimaryColor
                    )
                }
            }

            if (isTwoPlayers) {
                CompositionLocalProvider(value = LocalLayoutDirection provides LayoutDirection.Rtl) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth(0.5f)
                            .align(Alignment.CenterEnd)
                            .padding(
                                start = if (player2Placement == null) 6.dp else 0.dp,
                                end = 16.dp
                            )
                            .offset(x = if (player2Placement != null) (-6).dp else 0.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CompositionLocalProvider(value = LocalLayoutDirection provides LayoutDirection.Ltr) {
                            val medal2: Painter? = when (player2Placement) {
                                1 -> painterResource(id = R.drawable.ic_medal_gold)
                                2 -> painterResource(id = R.drawable.ic_medal_silver)
                                3 -> painterResource(id = R.drawable.ic_medal_bronze)
                                else -> null
                            }
                            if (player2Placement != null && medal2 != null) {
                                Box(
                                    contentAlignment = Alignment.Center,
                                    modifier = Modifier
                                        .height(32.dp)
                                        .offset(x = (-6).dp)
                                ) {
                                    Image(
                                        alignment = Alignment.Center,
                                        painter = medal2,
                                        contentDescription = null,
                                        contentScale = ContentScale.FillHeight
                                    )
                                    Text(
                                        text = player2Placement.toString(),
                                        style = MaterialTheme.typography.headlineMedium.copy(
                                            color = Color(0xFF05285A),
                                            fontWeight = FontWeight.Bold
                                        ),
                                    )
                                }
                            }

                            // Player 2 icon
                            Box(
                                modifier = Modifier
                                    .padding(1.dp)
                                    .border(1.dp, MaterialTheme.colorScheme.surface, CircleShape)
                            ) {
                                if (player2Icon != null) {
                                    AsyncImage(
                                        model = player2Icon,
                                        contentDescription = null,
                                        error = painterResource(R.drawable.ic_default_account),
                                        contentScale = ContentScale.Crop,
                                        modifier = Modifier
                                            .size(32.dp)
                                            .clip(CircleShape)
                                            .clickable {
                                                if (player2Status != APITournamentUserStatus.NOT_STARTED) {
                                                    LeaderboardViewModel.instance.showProfile(
                                                        player2Id ?: "",
                                                        game_mode_id
                                                    )
                                                }
                                            }
                                    )
                                } else {
                                    Image(
                                        alignment = Alignment.Center,
                                        painter = painterResource(
                                            if (AuthenticationManager.instance.isGuest)
                                                R.drawable.ic_fanduel_account else R.drawable.ic_default_account
                                        ), //Players Profile Img
                                        contentDescription = null,
                                        modifier = Modifier.size(32.dp)
                                    )
                                }
                            }

                            Spacer(modifier = Modifier.width(4.dp))
                            Column (
                                horizontalAlignment = Alignment.End,
                                verticalArrangement = Arrangement.spacedBy(2.dp, Alignment.CenterVertically)
                            ) {
                                AutosizeText(
                                    text = player2Name ?: "",
                                    style = MaterialTheme.typography.labelLarge
                                        .copy(fontWeight = FontWeight.Normal),
                                    maxLines = 1,
                                    color = MaterialTheme.colorScheme.secondary,
                                    textAlign = TextAlign.Right,
                                    modifier = Modifier.clickable {
                                        if(player2Status != APITournamentUserStatus.NOT_STARTED){
                                            LeaderboardViewModel.instance.showProfile(player2Id ?:"",game_mode_id)
                                        }
                                    }
                                )
                                AutosizeText(
                                    text = player2Score ?: "",
                                    style = MaterialTheme.typography.titleMedium.copy(
                                        fontSize = 20.sp,
                                        fontWeight = FontWeight.Black
                                    ),
                                    color = MaterialTheme.colorScheme.secondary,
                                    textAlign = TextAlign.Right,
                                    maxLines = 1,
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    if (isTwoPlayers){
        HorizontalDivider(
            modifier = Modifier.fillMaxWidth(),
            thickness = 1.dp,
            color = MaterialTheme.colorScheme.outlineVariant
        )
    }
}

@Preview
@Composable
fun ReEntryPreview(){
        LeaderboardTournamentReEntryView(null)
}



@Composable
fun LeaderboardTournamentReEntryView(data: APITournamentInstanceEntry?) {
    var backgroundColor: Color = MaterialTheme.colorScheme.outlineVariant

    val coroutineScope = rememberCoroutineScope()

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp)
            .background(backgroundColor)
            .padding(horizontal = 12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = STR(R.string.re_enter_for_a_better_score),
                style = MaterialTheme.typography.bodyMedium,
            )
            Column (modifier = Modifier.width(62.dp), horizontalAlignment = Alignment.End) {
                Text(
                    text = data?.entry_fee?.toDollarWithCommaString() ?: "0.00".toDollarString(),
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Right
                )
                Text(
                    text = STR(R.string.entry_caps),
                    style = MaterialTheme.typography.labelSmall,
                    textAlign = TextAlign.Right
                )
            }
            CommonButton(
                title = STR(R.string.re_enter),
                onClick = {
                    val entryFee = if(data?.has_additional_ticket == true){
                        EntryFee("0.00",data.entry_fee)
                    }else{
                        data?.entryFee ?:EntryFee()
                    }
                    TournamentManager.instance.reEnterTournament(
                        tournamentInstanceEntryId = data?.id ?: "",
                        gameId = data?.game_id?:"",
                        entryFee = entryFee,
                        maxSlots = data?.maximum_slots,
                        survivorRounds = data?.survivorRounds,
                        brand = data?.brand?.value,
                        cutoffPercent = data?.fifty_fifty_cutoff_percent
                    )
                },
                height = 32f,
                width = 80f,
                fillColor = theme_light_primary
            )
        }
    }
}

@Composable
fun LeaderboardPlayerView(model: APITournamentInstanceEntryResult,game_mode_id:String? = null)
{
    val order:Int? = model.order
    var medal: Painter? = when(order)
    {
        1 -> painterResource(id = R.drawable.ic_medal_gold)
        2 -> painterResource(id = R.drawable.ic_medal_silver)
        3 -> painterResource(id = R.drawable.ic_medal_bronze)
        else -> {
            if(order == null){
                painterResource(id = R.drawable.ic_waiting_leaderboard)
            }else{
                null
            }
        }
    }
    if(model.status == APITournamentUserStatus.NOT_STARTED){
        medal = painterResource(id = R.drawable.ic_waiting_leaderboard)
    }
    val backgroundColor: Color
    val textStyle: TextStyle
    val isCurrentUser = (AuthenticationManager.instance.isGuest && order == 1) || (model.getUserName() == AuthenticationManager.instance.apiMe?.username)
    if(isCurrentUser)
    {
//        backgroundColor = Color(0xFFEAF0F6)
        backgroundColor = Color(0xFFEAF0F6)
        textStyle = MaterialTheme.typography.bodyMedium.copy(color = Color(0xFF005FC8))
    }
    else
    {
        backgroundColor = Color.White
        textStyle = MaterialTheme.typography.bodySmall
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(64.dp)
    )
    {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(backgroundColor)
        )
        {
            Row(

                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier
                    .weight(1.0f)
                    .padding(end = 6.dp)
            )
            {
                //Placement + ProfilePic
                Row(
                    modifier = Modifier
                        .padding(start = 2.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(alignment = Alignment.CenterHorizontally, space = (-11).dp)
                )
                {
                    val circlePadding = 6.dp
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .size(52.dp)
                            .padding(circlePadding)
                    )
                    {
                        if(medal != null)
                        {
                            Image(
                                alignment = Alignment.Center,
                                painter = medal,
                                contentDescription = null,
                                modifier = Modifier.fillMaxSize()
                            )
                        }
                        if(order != null && model.status != APITournamentUserStatus.NOT_STARTED){
                            Text(
                                text = order.toString(),
                                style = MaterialTheme.typography.bodyMedium.copy(color = Color(0xFF05285A)),
                            )
                        }
                    }
                    Box(
                        Modifier
                            .clip(CircleShape)
                            .shadow(10.dp, CircleShape)
                            .background(Color.White)
                            .size(38.dp)
                    )
                    {
                        //Padding on upper box expands Circle instead of applying padding
                        Box(
                            Modifier
                                .padding(4.dp)
                                .clip(CircleShape)
                                .fillMaxSize(),
                            contentAlignment = Alignment.Center
                        )
                        {
                            if(model.user?.avatar_url != null){
                                AsyncImage(
                                    model = model.user.avatar_url,
                                    contentDescription = null,
                                    error = painterResource(R.drawable.ic_default_account),
                                    contentScale = ContentScale.Crop,
                                    modifier = Modifier
                                        .size(32.dp)
                                        .clip(CircleShape)
                                        .clickable {
                                            if (model.status != APITournamentUserStatus.NOT_STARTED) {
                                                LeaderboardViewModel.instance.showProfile(
                                                    model.user.id,
                                                    game_mode_id
                                                )
                                            }
                                        }
                                )
                            }else{
                                Image(
                                    alignment = Alignment.Center,
                                    painter = painterResource(if(AuthenticationManager.instance.isGuest)
                                        R.drawable.ic_fanduel_account else  R.drawable.ic_default_account), //Players Profile Img
                                    contentDescription = null,
                                    modifier = Modifier.size(32.dp)
                                )
                            }
                        }
                    }
                }
                //Username
                model.getUserName()?.let { Text(
                    text = if(model.status == APITournamentUserStatus.NOT_STARTED) STR(R.string.waiting_for_opponent) else it,
                    style = textStyle,
                    modifier = Modifier
                        .weight(1.0f)
                        .clickable {
                            if (model.status != APITournamentUserStatus.NOT_STARTED) {
                                LeaderboardViewModel.instance.showProfile(
                                    model.user?.id ?: "",
                                    game_mode_id
                                )
                            }
                        }
                ) }

                if(model.status == APITournamentUserStatus.NOT_STARTED){
                    //Score
                    Column(
                        verticalArrangement = Arrangement.spacedBy(4.dp),
                        horizontalAlignment = Alignment.End,
                        modifier = Modifier
                            .width(80.dp)
                    )
                    {
                        Spacer(Modifier.width(40.dp))
                        Text(
                            text = STR(R.string.not_started),
                            style = textStyle
                        )
                        Text(
                            text = STR(R.string.score_caps),
                            style = MaterialTheme.typography.labelSmall,
                            color = Color(0xFF6A6F73)
                        )
                    }
                }else{
                    //Winnings
                    if(!AuthenticationManager.instance.isGuest && (model.prize_amount?:"0").toDouble() > 0){
                        Column(
                            verticalArrangement = Arrangement.spacedBy(4.dp),
                            horizontalAlignment = Alignment.End,
                        )
                        {
                            Text(
                                text = (model.prize_amount?:"0").toDollarString(),
                                style = textStyle
                            )
                            Text(
                                text = STR(R.string.won_caps),
                                style = MaterialTheme.typography.labelSmall,
                                color = Color(0xFF6A6F73)
                            )
                        }
                    }

                    //Score
                    Column(
                        verticalArrangement = Arrangement.spacedBy(4.dp),
                        horizontalAlignment = Alignment.End,
                        modifier = Modifier.width(80.dp)
                    )
                    {
                        val scoreLabel = model.score
                        Text(
                            text = scoreLabel?.toNumberWithCommaString()
                                ?: if(model.status == APITournamentUserStatus.IN_PROGRESS) STR(R.string.playing) else STR(R.string.open),
                            style = textStyle
                        )
                        if(model.status != APITournamentUserStatus.IN_PROGRESS){
                            Text(
                                text = STR(R.string.score_caps),
                                style = MaterialTheme.typography.labelSmall,
                                color = Color(0xFF6A6F73)
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.width(12.dp))
            }
            HorizontalDivider(
                modifier = Modifier
                    .fillMaxWidth(
                        if (LocalInspectionMode.current || AuthenticationManager.instance.isGuest) {
                            1.0f
                        } else {
                            0.9f
                        }
                    )
                    .align(Alignment.End),
                thickness = 1.dp,
                color = MaterialTheme.colorScheme.outline
            )
        }
    }
}

enum class LeaderboardResultType
{
    DEFAULT,
    WON,
    TIED,
    IN_PROGRESS,
    REFUNDED,
    EXPIRED,
    DECLINED,
}
