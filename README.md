# FaceOff X on Android

## Setup

1. Install Homebrew (https://brew.sh)
2. Install git-lfs "brew install git-lfs"
3. Generate (if necessary) and upload your SSH key to GitHub -- see [SSH Key](#ssh-key)
4. Clone Git repository and change to local repository directory (e.g.: `cd ~/code/app-android-fd`)
5. Install android build dependencies: `make install-deps`
6. Install Android Studio Hedgehog 2023.1.1 Patch 2
7. If not located in the USA, run a VPN when updating libraries or on first run. See [VPN Use](#vpn-use).
8. Run ./fdconfig.sh to configure environment variables for fanduel sdk (do this only once; this will write environment vars to ~/.zshrc)
9. Run ./sentryconfig.sh to configure environment for sentry sdk (do this only once; this will write environment vars to ~/.zshrc)
10. Run ./setEnvArm.sh on ARM macs or ./setEnvIntel.sh on intel macs to configure environment for building. (do this only once; this will write environment vars to ~/.zshrc)
11. Reload terminal / Android Studio to allow environment variables configured by the above scripts to be applied
12. Load project in Android Studio


### Android Studio SDK Setup (Tools/SDK Manager)

- SDK Platforms - Android 14 ("UpsideDownCake")
- SDK Tools - (Show package details)
	- Build-Tools 34.0.0
	- NDK (Side by side) 23.1.7779620
	- Android SDK Command-line Tools 8.0
	- Android SDK Platform-Tools 34.0.5

### Caveats

If you have an issue with 'brew' not being found on ARM Mac, do the following
1. Run "echo 'export PATH="/opt/homebrew/bin:$PATH"' >> ~/.zshrc"
2. Restart Terminal

## Development

### Commit Messages & Conventional Commits

Please make sure that commit messages adhere to Conventional Commits guidelines: https://www.conventionalcommits.org/en/v1.0.0/

#### Risk Levels

The following risk levels are used to categorize the impact of a change. The levels inform the QA team about the potential impact of a change and help them to prioritize their testing efforts.

- **none**: changes that will not affect the app's behavior
- **low**: changes that are unlikely to affect the app's behavior
- **medium**: changes that are likely to affect the app's behavior
- **high**: changes that are very likely to affect the app's behavior

#### Examples

- `feat: add new feature to the app PX-1234 (medium)`
- `fix: correct a bug in the app PX-6789 (high)`
- `refactor: change the implementation of a feature without changing its behavior (low)`
- `test: add or modify tests (none)`
- `docs: add or modify documentation (none)`

### Formatting + Linting + Pull Request Checks

We employ SwiftFormat and SwiftLint for consistent code formatting and linting. Before submitting a pull request, please run `make pr-check` to format and lint your code. You can also use `make format` to format your code and `make lint` for linting.

Useful commands:

-   `make format` - Format all Swift code.
-   `make lint` - Lint all Swift code.
-   `make pr-check` - Run all checks before submitting a pull request.

### After Pulling from Origin

-   `make install-deps` - Install dependencies

### Before Sending a Pull Request

-   Ensure all code is formatted and linted (see above).
-   Ensure all new and modified code is documented.
-   Ensure 100% coverage on all new and modified code.
-   Ensure all tests pass.
-   Run and test the app on a physical device, not the simulator.

### Make Commands

-   `make install-deps` - Install dependencies
-   `make check-android` - Install android dependencies
-   `make build-checks` - Perform pre-build dependency checks.
-   `make build-qa-debug`  - Build QA debug (DevelopmentGames).
-   `make build-qa-release`  - Build QA release (DevelopmentGames).
-   `make build-production-debug`  - Build production debug (ProductionDefault).
-   `make build-production-release`  - Build production release (ProductionDefault).
-   `make pr-check` - Run all checks before submitting a pull request.
-   `make clean` - Clean the project directory.
-   `make lint` - Lint code


### SSH Key

Prerequisites:

-   You must have a GitHub account.
-   You must have a public/private SSH key pair.
-   You must have uploaded your public key to your GitHub account.

#### Generate a new SSH key pair:

Follow the [GitHub Docs] (https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent) to get set up with SSH.

-   Note: You may want to have multiple SSH keys if you are on your personal device. When following the GitHub docs remember to:

    1. Enter a file name in which to save the key to distinguish between work and personal ssh keys, e.g. `gametaco_rsa`.
    1. Use the path to the newly created file to copy the public key `cat ~/.ssh/gametaco_rsa.pub | pbcopy`.
    1. The config file should have a section for each SSH identity:

        ```
        Host github.com
          AddKeysToAgent yes
          UseKeychain yes
          IdentityFile ~/.ssh/id_ed25519

        Host github.com
          AddKeysToAgent yes
          UseKeychain yes
          IdentityFile ~/.ssh/gametaco_rsa
        ```

        - NOTE: A different Host name can be used to avoid the next step, but that will lead to different next steps.

    1. Git might not use the correct key when accessing Game Taco repos (or when accessing personal repos) because the ssh agent doesn't know which key to use. One way to help ssh is to remove all keys from ssh, and add the desired key (copy the `IdentityFile` path from the config file):
        ```
        ssh-add -D
        ssh-add ~/.ssh/gametaco_rsa
        ```
        - NOTE: Confirm the identity used by ssh with: `ssh-add -l`.
        - NOTE: This step will have to be repeated when working on a repo that requires a different ssh identity.

#### Upload your public key to GitHub:

1. Go to https://github.com/settings/keys
1. Click "New SSH key"
1. Enter a title for the key (e.g.: "MacBook Pro")
1. Paste the public key into the "Key" field
1. Click "Add SSH key"


### VPN Use
If you are not located in the USA, you will likely need to configure the VPN to be able to download Fanduel's libraries and SDKs.

#### VPN Installation
1. Download NordVPN from https://nordvpn.com/download/ and install it.
2. Login details:
   - Username: <EMAIL>
   - Password: G@m3TxWw

#### VPN Configuration
1. Open NordVPN and Connect.
2. To ensure access to FanDuel's SDKs, you will likely want to select "United States - San Francisoco" as the server location.


### Build Variants

This project uses **two flavor dimensions** to create different build variants:

#### Flavor Dimensions

**Environment Dimension:**
- **`development`** - QA/testing builds with debug features, environment switching, and enhanced logging
- **`production`** - Production builds optimized for end users

**Target App Dimension:**
- **`default`** - Standard app configuration 
- **`games`** - QA-specific configuration with separate app ID, distinctive icons, and testing features

#### Build Commands

- **QA Builds**: `make build-qa-debug` or `make build-qa-release`
- **Production Builds**: `make build-production-debug` or `make build-production-release`
