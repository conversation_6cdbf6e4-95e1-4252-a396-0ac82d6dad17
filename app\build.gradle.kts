import io.sentry.android.gradle.extensions.InstrumentationFeature
import io.sentry.android.gradle.instrumentation.logcat.LogcatLevel
import org.jetbrains.kotlin.gradle.dsl.JvmTarget

allprojects {
    tasks.withType<JavaCompile> {
        options.compilerArgs.addAll(listOf("-Xlint:unchecked", "-Xlint:deprecation"))
    }
}

plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("org.jetbrains.kotlin.plugin.compose")
    id("org.jetbrains.kotlin.plugin.serialization")
    kotlin("kapt")
    id("io.sentry.android.gradle") version "4.4.1"
}

// Hack for Game apps. The custom bundle ID we're using are not registered in Firebase. Ignore the plugin for now
val containsGames = project.gradle.startParameter.taskRequests.toString().contains("Games")
if (!containsGames) {
    apply(plugin = "com.google.gms.google-services")
}

val buildVersion = project.findProperty("BuildVersion")?.toString()?.toInt() ?: error("BuildVersion not found - does `gradle.properties` exist?")

//@Suppress("DEPRECATION")
android {
    namespace = "com.gametaco.app_android_fd"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.fanduel.skillgames"
        minSdk = 26
        targetSdk = 34

        versionCode = buildVersion
        versionName = "2.24.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }

        ndk {
            abiFilters.add("armeabi-v7a")
            abiFilters.add("arm64-v8a")
        }

    }

    buildFeatures {
        buildConfig = true
        resValues = true
        compose = true
        aidl = true
        viewBinding = false
    }

    buildTypes {

        getByName("debug") {
            isMinifyEnabled = false
            versionNameSuffix = "-d"
        }

        getByName("release") {
            // Standard release settings
            isMinifyEnabled = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    flavorDimensions.add("environment")
    flavorDimensions.add("targetApp")
    productFlavors {
        create("production") {
            dimension = "environment"
        }

        create("development") {
            dimension = "environment"
        }

        create("default") {
            dimension = "targetApp"
        }

        create("games") {
            applicationIdSuffix = ".games.qa"
            dimension = "targetApp"
            resValue("string", "fanduel_app_name", "Fanduel Faceoff\nGames QA")
        }
    }

    sourceSets {
        getByName("production") {
            java.srcDirs("src/withUnity/java")
            res.srcDirs("src/withUnity/res")
        }
    }

    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }

    ndkVersion = Versions.ndkVersion
    buildToolsVersion = Versions.buildToolsVersion

    dependenciesInfo {
        includeInApk = false
        includeInBundle = false
    }
}

kotlin {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_17)
    }
}

composeCompiler {
//    enableStrongSkippingMode = true
}

afterEvaluate {
    if (gradle.startParameter.taskNames.any { it.contains("NoUnity") }) {
        rootProject.ext.set("includeUnity", false)
    }
    else
    {
        rootProject.ext.set("includeUnity", true)
    }
}

dependencies {
    configurations.all {
        resolutionStrategy {
            force("org.jetbrains.kotlin::jetified-kotlin-stdlib-${Versions.kotlin}")
            exclude(group = "com.perimeterx.sdk", module = "msdk")
        }
    }

    implementation(project(":unityLibrary"))
    implementation(fileTree(File("..\\game-android-container-fd\\framework\\unityLibrary\\libs")) { include("*.jar") })

    implementation(files("libs/GeoComplyLibrary-DexGuard-2.15.0-841.aar"))

    implementation(Dependencies.coreKtx)
    implementation(Dependencies.eventBus)
    implementation(Dependencies.lifecycleRuntimeKtx)
    implementation(Dependencies.activityCompose)
    implementation(platform(Dependencies.composeBom))
    implementation(Dependencies.navigationCompose)
    implementation(Dependencies.navigationRuntime)
    implementation(Dependencies.composeUi)
    implementation(Dependencies.composeUiGraphics)
    implementation(Dependencies.composeUiToolingPreview)
    implementation(Dependencies.composeMaterial3)
    implementation(Dependencies.kotlinxSerializationJson)

    implementation(Dependencies.amplitude)
    implementation(Dependencies.amplitude_experimentation)
    implementation(Dependencies.appsFlyer)
    implementation(Dependencies.appsFlyerLvl)
    implementation(Dependencies.trustly)
    implementation(Dependencies.koinCore)
    implementation(Dependencies.koinAndroid)
    implementation(Dependencies.processPhoenix)
    implementation(Dependencies.timber)
    implementation("androidx.browser:browser:1.8.0")
    implementation("io.reactivex.rxjava2:rxandroid:2.1.1")
    implementation("io.reactivex.rxjava2:rxjava:2.2.21")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("androidx.media3:media3-ui:1.3.1")
    implementation("androidx.legacy:legacy-support-core-ui:1.0.0")

    implementation("com.google.firebase:firebase-messaging:23.2.0")

    testImplementation(Dependencies.kotlinTest)
    androidTestImplementation(Dependencies.jUnit)
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
    androidTestImplementation(platform("androidx.compose:compose-bom:${Versions.compose}"))
    androidTestImplementation("androidx.compose.ui:ui-test-junit4")
    androidTestImplementation("androidx.test.ext:junit-ktx:1.1.5")
    androidTestImplementation("androidx.test:monitor:1.6.1")

    implementation("androidx.compose.ui:ui-tooling")
    debugImplementation("androidx.compose.ui:ui-test-manifest")

    implementation(Dependencies.moshi)
    implementation(Dependencies.okio)
    implementation(Dependencies.okhttp)
    implementation(Dependencies.okhttpLoggingInterceptor)
    implementation(Dependencies.retrofit)
    implementation(Dependencies.retrofitAdapterRxjava2)
    implementation(Dependencies.retrofitConverterGson)
    implementation(Dependencies.retrofitConverterMoshi)

    implementation(Dependencies.coroutinesCore)
    implementation(Dependencies.coroutinesAndroid)

    implementation(Dependencies.composeMaterial3Icons)

    implementation(project(Modules.utilities))
    implementation(project(Modules.resources))

    implementation(Dependencies.exoPlayer)

    implementation(Dependencies.googlePlayServicesBase)
    implementation(Dependencies.playServicesLocation)
    implementation(Dependencies.googlePlayServicesAdsIdentifier)
    implementation(Dependencies.playIntegrity)

    implementation(Dependencies.coilCompose)

    implementation(Dependencies.appcompat)
    implementation(Dependencies.brazeBase)
//    implementation(Dependencies.brazeUI)
    implementation(Dependencies.brazeCompose)
    implementation(Dependencies.sift)
    implementation(Dependencies.lottie)

//    implementation(Dependencies.perimeterX)


    implementation(Dependencies.fd_corewebview)
    implementation(Dependencies.fd_ioc)
    implementation(Dependencies.fd_coreconfig)
    implementation(Dependencies.fd_corepx)
    implementation(Dependencies.fd_commonmodules)
    implementation(Dependencies.fd_wallet)
    implementation(Dependencies.fd_account)
    implementation(Dependencies.fd_account_contract)
    implementation(Dependencies.fd_account_verification_contract)
    implementation(Dependencies.fd_account_mfa)
    implementation(Dependencies.fd_accounthub)
    implementation(Dependencies.fd_accounthub_contract)
    implementation(Dependencies.fd_amplitude_um)
    implementation(Dependencies.fd_coredeeplinks)
    implementation(Dependencies.fd_coreevents)
    implementation(Dependencies.fd_responsible_gaming)
    implementation(Dependencies.fd_coremodalpresenter)
    implementation(Dependencies.fd_coreapiidentities)
    implementation(Dependencies.fd_salesforce_library)
    implementation(Dependencies.fd_salesforce_contract)
    implementation(Dependencies.salesforce_service_chat_ui)

//    implementation(Dependencies.fd_promolinks)


}

kapt {
    correctErrorTypes = true
}

sentry {
    // Disables or enables debug log output, e.g. for for sentry-cli.
    // Default is disabled.
    debug.set(false)

    // The slug of the Sentry organization to use for uploading proguard mappings/source contexts.
    org.set("worldwinner")

    // The slug of the Sentry project to use for uploading proguard mappings/source contexts.
    projectName.set("fo-x-android")

    // The authentication token to use for uploading proguard mappings/source contexts.
    // WARNING: Do not expose this token in your build.gradle files, but rather set an environment
    // variable and read it into this property.
    authToken.set(System.getenv("SENTRY_AUTH_TOKEN"))

    // The url of your Sentry instance. If you're using SAAS (not self hosting) you do not have to
    // set this. If you are self hosting you can set your URL here
    url = null

    // Disables or enables the handling of Proguard mapping for Sentry.
    // If enabled the plugin will generate a UUID and will take care of
    // uploading the mapping to Sentry. If disabled, all the logic
    // related to proguard mapping will be excluded.
    // Default is enabled.
    includeProguardMapping.set(true)

    // Whether the plugin should attempt to auto-upload the mapping file to Sentry or not.
    // If disabled the plugin will run a dry-run and just generate a UUID.
    // The mapping file has to be uploaded manually via sentry-cli in this case.
    // Default is enabled.
    autoUploadProguardMapping.set(true)

    // Experimental flag to turn on support for GuardSquare's tools integration (Dexguard and External Proguard).
    // If enabled, the plugin will try to consume and upload the mapping file produced by Dexguard and External Proguard.
    // Default is disabled.
    dexguardEnabled.set(false)

    // Disables or enables the automatic configuration of Native Symbols
    // for Sentry. This executes sentry-cli automatically so
    // you don't need to do it manually.
    // Default is disabled.
    uploadNativeSymbols.set(false)

    // Whether the plugin should attempt to auto-upload the native debug symbols to Sentry or not.
    // If disabled the plugin will run a dry-run.
    // Default is enabled.
    autoUploadNativeSymbols.set(true)

    // Does or doesn't include the source code of native code for Sentry.
    // This executes sentry-cli with the --include-sources param. automatically so
    // you don't need to do it manually.
    // Default is disabled.
    includeNativeSources.set(false)

    // Generates a JVM (Java, Kotlin, etc.) source bundle and uploads your source code to Sentry.
    // This enables source context, allowing you to see your source
    // code as part of your stack traces in Sentry.
    includeSourceContext.set(true)

    // Configure additional directories to be included in the source bundle which is used for
    // source context. The directories should be specified relative to the Gradle module/project's
    // root. For example, if you have a custom source set alongside 'main', the parameter would be
    // 'src/custom/java'.
    additionalSourceDirsForSourceContext.set(emptySet())

    // Enable or disable the tracing instrumentation.
    // Does auto instrumentation for specified features through bytecode manipulation.
    // Default is enabled.
    tracingInstrumentation {
        enabled.set(true)

        // Specifies a set of instrumentation features that are eligible for bytecode manipulation.
        // Defaults to all available values of InstrumentationFeature enum class.
        features.set(setOf(InstrumentationFeature.DATABASE, InstrumentationFeature.FILE_IO, InstrumentationFeature.OKHTTP, InstrumentationFeature.COMPOSE))

        // The set of glob patterns to exclude from instrumentation. Classes matching any of these
        // patterns in the project's sources and dependencies JARs won't be instrumented by the Sentry
        // Gradle plugin.
        //
        // Don't include the file extension. Filtering is done on compiled classes and
        // the .class suffix isn't included in the pattern matching.
        //
        // Example usage:
        // ```
        // excludes.set(setOf("com/example/donotinstrument/**", "**/*Test"))
        // ```
        //
        // Only supported when using Android Gradle plugin (AGP) version 7.4.0 and above.
        excludes.set(emptySet())
    }

    // Enable auto-installation of Sentry components (sentry-android SDK and okhttp, timber, fragment and compose integrations).
    // Default is enabled.
    // Only available v3.1.0 and above.
    autoInstallation {
        enabled.set(true)

        // Specifies a version of the sentry-android SDK and fragment, timber and okhttp integrations.
        //
        // This is also useful, when you have the sentry-android SDK already included into a transitive dependency/module and want to
        // align integration versions with it (if it's a direct dependency, the version will be inferred).
        //
        // NOTE: if you have a higher version of the sentry-android SDK or integrations on the classpath, this setting will have no effect
        // as Gradle will resolve it to the latest version.
        //
        // Defaults to the latest published Sentry version.
        sentryVersion.set("7.8.0")
    }

    // Disables or enables dependencies metadata reporting for Sentry.
    // If enabled, the plugin will collect external dependencies and
    // upload them to Sentry as part of events. If disabled, all the logic
    // related to the dependencies metadata report will be excluded.
    //
    // Default is enabled.
    //
    includeDependenciesReport.set(true)

    // Whether the plugin should send telemetry data to Sentry.
    // If disabled the plugin won't send telemetry data.
    // This is auto disabled if running against a self hosted instance of Sentry.
    // Default is enabled.
    telemetry.set(true)
}

androidComponents {
    onVariants { variant ->
        val shouldEnableLogcatInstrumentation =
            variant.buildType == "debug" || variant.flavorName?.contains("withUnityDevelopment") == true

        sentry {
            tracingInstrumentation {
                logcat {
                    enabled.set(shouldEnableLogcatInstrumentation)
                    if (shouldEnableLogcatInstrumentation) {
                        minLevel.set(LogcatLevel.DEBUG)
                    }
                }
            }
        }

        val taskNames = gradle.startParameter.taskNames
        val variantTaskName = "assemble${variant.name.replaceFirstChar { it.uppercase() }}"
        val isBeingBuilt = taskNames.any { it.contains(variantTaskName, ignoreCase = true) }

        if (isBeingBuilt) {
            if (shouldEnableLogcatInstrumentation) {
                println("[sentry] Enabling logcat instrumentation for variant: ${variant.name}")
            } else {
                println("[sentry] Disabling logcat instrumentation for variant: ${variant.name}")
            }
        }
    }
}


