package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.FDWebviewPage
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.WalletManager
import com.gametaco.app_android_fd.ui.modifiers.clickableWithoutRipple
import com.gametaco.app_android_fd.ui.theme.AppFont
import com.gametaco.app_android_fd.ui.theme.BlueMinimalBackground
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.geometry.Offset
import com.gametaco.app_android_fd.manager.MockModeManager
import com.gametaco.app_android_fd.utils.toDollarString
import com.gametaco.app_android_fd.utils.toDollarWithCommaString
import com.gametaco.utilities.STR
import resources.R

@Composable
fun TopBar (title: String, onBack: (()->Unit)? = null){
    Box(
        modifier = Modifier
            .background(MaterialTheme.colorScheme.surface)
            .statusBarsPadding()
            .fillMaxWidth()
            .height(48.dp),
        contentAlignment = Alignment.Center
    ){
        if (onBack != null){
            BackButton(Modifier
                .align(alignment = Alignment.CenterStart), onBack = onBack)
        }
        Text(
            text = title,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 48.dp),
        )
    }
}
@Composable
fun TopBarLogo(){
    val mockModeManager = MockModeManager.instance
    val isMockMode = mockModeManager.isMockModeEnabled
    val mockGradient = getMockModeGradient()
    
    Box(
        modifier = Modifier
            .background(if (isMockMode) mockGradient else BlueMinimalBackground)
            .statusBarsPadding()
            .fillMaxWidth()
            .height(72.dp)
            .padding(horizontal = 16.dp)
            .padding(top = 12.dp, bottom = 8.dp)
    ) {
        Image(
            modifier = Modifier
                .size(96.dp, 28.dp)
                .align(Alignment.CenterStart),
            painter = painterResource(R.drawable.ic_fanduel_top),
            contentDescription = null,
            contentScale = ContentScale.Fit
        )
        
        if (isMockMode) {
            MockModeIndicator(
                username = mockModeManager.mockUsername ?: "Mock User",
                modifier = Modifier.align(Alignment.CenterEnd)
            )
        }
    }
}
@Composable
fun TopBarNewLogo(showBonusTooltip:MutableState<Boolean>){
    val wallet by WalletManager.instance.wallet.collectAsState()
    val mockModeManager = MockModeManager.instance
    val isMockMode = mockModeManager.isMockModeEnabled
    val mockGradient = getMockModeGradient()

    Box(
        modifier = Modifier
            .background(if (isMockMode) mockGradient else BlueMinimalBackground)
            .statusBarsPadding()
            .fillMaxWidth()
            .height(72.dp)
            .padding(horizontal = 16.dp)
            .padding(top = 12.dp, bottom = 8.dp)
    ){
        Image(
            modifier = Modifier
                .size(96.dp, 28.dp)
                .align(Alignment.CenterStart),
            painter = painterResource(R.drawable.ic_fanduel_top),
            contentDescription = null,
            contentScale = ContentScale.Fit,
            alignment = Alignment.TopEnd
        )
        Row(horizontalArrangement = Arrangement.End,
            modifier = Modifier.align(Alignment.CenterEnd))
        {
            if(wallet.balance.toFloat() > 0){
                Box(modifier = Modifier
                    .clip(RoundedCornerShape(16.dp))
                    .clickable {
                        showBonusTooltip.value = !showBonusTooltip.value
                    }
                    .background(Color.White.copy(0.2f), shape = RoundedCornerShape(16.dp))
                    .height(32.dp)
                    .padding(top = 2.dp, start = 8.dp, end = 2.dp, bottom = 2.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Row(horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically) {
                        Column(horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center) {
                            AutosizeText(
                                text = wallet.balance.toDollarWithCommaString(),
//                        text = STR(R.string.bonus_cash_available_auto_applied, value.toDollarString()),
                                style = MaterialTheme.typography.bodySmall.copy(fontSize = 10.sp),
                                color = Color.White,
                                maxLines = 2,
                            )
                            Spacer(modifier = Modifier.height(3.dp))
                            AutosizeText(
                                text = "BONUS",
//                        text = STR(R.string.bonus_cash_available_auto_applied, value.toDollarString()),
                                style = MaterialTheme.typography.bodySmall.copy(fontSize = 8.sp, fontFamily = AppFont.ProximaNovaCond),
                                color = Color.White,
                                maxLines = 2,
                            )
                        }
                        Spacer(modifier = Modifier.width(4.dp))

                        Image(modifier = Modifier
                            .size(26.dp),
                            painter = painterResource(R.drawable.ic_cash_single),
                            contentDescription = null)
                    }
                }
                Spacer(modifier = Modifier.width(8.dp))
            }

            Box(modifier = Modifier
                .clip(RoundedCornerShape(16.dp))
                .clickable {
                    FDManager.instance.showWebviewFD(FDWebviewPage.Deposit)
                }
                .background(Color.White.copy(0.2f), shape = RoundedCornerShape(16.dp))
                .height(32.dp)
                .padding(8.dp),
                contentAlignment = Alignment.Center
            ) {
                Row(horizontalArrangement = Arrangement.Center, verticalAlignment = Alignment.CenterVertically) {
                    Image(modifier = Modifier
                        .size(17.dp),
                        painter = painterResource(R.drawable.ic_add),
                        contentDescription = null)

                    Spacer(modifier = Modifier.width(4.dp))

                    AutosizeText(
                        text = wallet.bonus_balance.toDollarWithCommaString(),
//                        text = STR(R.string.bonus_cash_available_auto_applied, value.toDollarString()),
                        style = MaterialTheme.typography.bodySmall.copy(fontSize = 12.sp),
                        color = Color.White,
                        maxLines = 1,
                    )
                }
            }
            Spacer(modifier = Modifier.width(8.dp))
            Box(modifier = Modifier
                .clip(RoundedCornerShape(100))
                .clickable {
                    NavManager.instance.navigate(Routes.ACCOUNT_SCREEN)
                }
                .background(Color.White.copy(0.2f), shape = RoundedCornerShape(100))
                .size(32.dp),
                contentAlignment = Alignment.Center
            ) {
                Image(modifier = Modifier
                    .size(17.dp),
                    painter = painterResource(R.drawable.ic_acc_settings),
                    contentDescription = null)
            }
        }
        
        if (isMockMode) {
            MockModeIndicator(
                username = mockModeManager.mockUsername ?: "Mock User",
                modifier = Modifier.align(Alignment.CenterEnd)
            )
        }
    }
}

@Composable
private fun getMockModeGradient(): Brush {
    return Brush.linearGradient(
        colors = listOf(
            Color(0xFFFFEB3B), // Bright yellow
            Color(0xFFFFC107)  // Amber
        ),
        start = Offset(-0.5f * 1080f, 0f),
        end = Offset(0.5f * 1080f, 0f)
    )
}

@Composable
private fun MockModeIndicator(
    username: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                color = Color.Black.copy(alpha = 0.7f),
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(
            text = "MOCK: $username",
            style = MaterialTheme.typography.labelSmall,
            color = Color.Yellow
        )
    }
}