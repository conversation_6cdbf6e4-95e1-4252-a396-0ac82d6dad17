package com.gametaco.app_android_fd.ui.components

import IcB<PERSON>
import androidx.compose.animation.core.spring
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.times
import coil.compose.AsyncImage
import com.gametaco.app_android_fd.LocalNavManager
import com.gametaco.app_android_fd.data.entity.APICatalog
import com.gametaco.app_android_fd.data.entity.APICatalogBannerData
import com.gametaco.app_android_fd.data.entity.APICatalogCtaLinkEnum
import com.gametaco.app_android_fd.data.entity.APICatalogGameItem
import com.gametaco.app_android_fd.data.entity.APICatalogSection
import com.gametaco.app_android_fd.data.entity.APICatalogSectionTypeEnum
import com.gametaco.app_android_fd.data.entity.APICatalogSpotlightItem
import com.gametaco.app_android_fd.data.entity.APICatalogSwimlaneGamesLayoutEnum
import com.gametaco.app_android_fd.data.entity.APICatalogTournamentItem
import com.gametaco.app_android_fd.data.entity.APITournament
import com.gametaco.app_android_fd.data.entity.APIWalletResponse
import com.gametaco.app_android_fd.data.entity.BannerEnum
import com.gametaco.app_android_fd.data.entity.GCGameData
import com.gametaco.app_android_fd.data.entity.GCRow
import com.gametaco.app_android_fd.data.entity.GCRowItem
import com.gametaco.app_android_fd.data.entity.GameCatalogResponse
import com.gametaco.app_android_fd.data.entity.RowStyles
import com.gametaco.app_android_fd.data.entity.RowTypes
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.FDWebviewPage
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.WalletManager
import com.gametaco.app_android_fd.ui.modifiers.clickableWithoutRipple
import com.gametaco.app_android_fd.ui.modifiers.fadingEdge
import com.gametaco.app_android_fd.ui.theme.AppFont
import com.gametaco.app_android_fd.ui.theme.ShadowFade
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.utils.toDollarString
import com.gametaco.app_android_fd.utils.toDollarWithCommaString
import com.gametaco.app_android_fd.viewmodel.GamesViewModel
import com.gametaco.app_android_fd.viewmodel.LobbyViewModel
import com.gametaco.app_android_fd.viewmodel.LoginViewModel
import com.gametaco.app_android_fd.viewmodel.PlayViewModel
import com.gametaco.utilities.STR
import resources.R

@Composable
fun GameTile(
    modifier: Modifier = Modifier,
    iconUrl: String,
    iconDescription: String? = null,
    clipShape: Shape = RoundedCornerShape(8.dp),
    showShadow: Boolean = true,
    onClick: () -> Unit
){
    Button(
        onClick = onClick,
        contentPadding = PaddingValues(0.dp),
        shape = clipShape,
        colors = ButtonDefaults.buttonColors(
            containerColor = Color.Transparent,
        ),
        modifier = Modifier
            .then(modifier)
            .wrapContentHeight()
            .let {
                if (showShadow) {
                    it.shadow(
                        elevation = 8.dp,
                        ambientColor = Color.White,
                        spotColor = Color.Black,
                        shape = clipShape
                    )
                } else it
            }
    ) {
        AsyncImage(
            model = iconUrl,
            contentDescription = iconDescription,
            modifier = Modifier
                .fillMaxSize(),
            contentScale = ContentScale.FillBounds,
        )
    }
}

@Composable
fun TaggedTile(
    modifier: Modifier = Modifier,
    message: String? = null,
    bannerData: APICatalogBannerData? = null,
    tagOffsetX: Dp? = null,
    tagOffsetY: Dp? = null,
    content: @Composable BoxScope.()->Unit
){
    Box(
        modifier = modifier
    ) {
        content()

        if (message != null){
            val backgroundBlue = Color(0xFF0070EB)
            val borderBlue = Color(0xFF64AEFF)
            val backgroundOrange = Color(0xFFF87A1E)
            val borderOrange = Color(0xFFFFB77E)
            val backgroundRed = Color(0xFFC7002B)
            val borderRed = Color(0xFFE96775)
            val backgroundPurple = Color(0xFF7401B6)
            val borderPurple = Color(0xFFB165FD)

            var messageText = message
            var icon: Int? = null
            var backgroundColor = backgroundBlue
            var borderColor = borderBlue

            if(bannerData != null){
                messageText = bannerData.text
                backgroundColor = Color(android.graphics.Color.parseColor(bannerData.color))
                borderColor = Color(android.graphics.Color.parseColor(bannerData.border_color))
            }else{
                when (message){
                    // Blue
                    BannerEnum.REWARD -> { messageText = STR(R.string.reward_caps) }
                    BannerEnum.BEAT_THE_SCORE -> { messageText = STR(R.string.beat_the_score_caps) }
                    BannerEnum.WINNERS_CIRCLE -> { messageText = STR(R.string.winners_circle_caps) }
                    BannerEnum.PROMOTION -> { messageText = STR(R.string.promotion_caps) }
                    BannerEnum.SPECIAL_OFFER -> { messageText = STR(R.string.special_offer_caps) }
                    BannerEnum.DAILY_SWING -> { messageText = STR(R.string.daily_swing_caps) }
                    BannerEnum.DAILY_KICK -> { messageText = STR(R.string.daily_kick_caps) }
                    BannerEnum.LIMITED_TIME -> { messageText = STR(R.string.limited_time_caps) }
                    BannerEnum.LIMITED_TIME_REWARD -> { messageText = STR(R.string.limited_time_reward_caps) }
                    BannerEnum.DAILY_PUTT -> { messageText = STR(R.string.daily_putt_caps) }
                    BannerEnum.HAPPY_HOUR -> { messageText = STR(R.string.happy_hour_caps) }
                    BannerEnum.FREE_CASH -> { messageText = STR(R.string.free_cash_caps) }
                    BannerEnum.DAILY_SHOT -> { messageText = STR(R.string.daily_shot_caps) }
                    BannerEnum.DAILY_POP -> { messageText = STR(R.string.daily_pop_caps) }

                    // Orange
                    BannerEnum.BIGGER_PRIZES -> {
                        messageText = STR(R.string.bigger_prizes_caps)
                        backgroundColor = backgroundOrange
                        borderColor = borderOrange
                    }
                    BannerEnum.TOURNAMENT -> {
                        messageText = STR(R.string.tournament_caps)
                        backgroundColor = backgroundOrange
                        borderColor = borderOrange
                    }
                    BannerEnum.MULTIPLAYER -> {
                        messageText = STR(R.string.daily_shot_caps)
//                    backgroundColor = backgroundOrange
//                    borderColor = borderOrange
                    }
                    BannerEnum.NEW_GAME -> {
                        messageText = STR(R.string.new_game_caps)
                        backgroundColor = backgroundOrange
                        borderColor = borderOrange
                    }
                    BannerEnum.NEW_GAME_MODE -> {
                        messageText = STR(R.string.new_game_mode_caps)
                        backgroundColor = backgroundOrange
                        borderColor = borderOrange
                    }

                    // Red
                    BannerEnum.HOT_STREAK -> {
                        messageText = STR(R.string.hot_streak_caps)
                        icon = R.drawable.ic_fire_emoji
                        backgroundColor = backgroundRed
                        borderColor = borderRed
                    }
                }
            }

            TagComponent(
                offsetX = tagOffsetX,
                offsetY = tagOffsetY,
                backgroundColor = backgroundColor,
                borderColor = borderColor,
                height = 20.dp,
                contentPadding = PaddingValues(start = if(icon == null) {10.dp} else {7.dp} ,end = 10.dp,top = 0.dp, bottom = 6.dp)
            ){
                TextCentered(
                    text = messageText.uppercase(),
                    icon = icon,
                    fontFamily = AppFont.Shentox,
                    fontSize = 9.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimary,
                    modifier = Modifier
                        .padding(top = 2.dp)
                )
            }
        }
    }
}

@Composable
fun BonusCashBanner(value: String){
    Row (
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
    ) {
        Image(
            modifier = Modifier.size(32.dp),
            painter = painterResource(R.drawable.ic_cash_single),
            contentDescription = STR(R.string.bonus_cash_icon)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            AutosizeText(
                text = STR(R.string.bonus_cash_available_auto_applied, value.toDollarString()),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.secondary,
                maxLines = 1,
            )
            AutosizeText(
                text = STR(R.string.we_automatically_use_this_first_when_you_join_a_contest),
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = 1,
            )
        }
    }
}
@Composable
fun ReferFriendsBanner(){
    Row (
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
    ) {
        Image(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(4.dp))
                .shadow(4.dp, RoundedCornerShape(4.dp))
                .clickable {
                    FDManager.instance.showWebviewFD(
                        page = null,
                        sourceURL = "/account/referrals",
                        title = "Earn Cash: Refer Friends"
                    )
                },
            contentScale = ContentScale.FillWidth,
            painter = painterResource(R.drawable.img_raf_banner),
            contentDescription = null
        )
    }
}
@Composable
fun FeaturedContest(
    contestType: String,
    gameName: String,
    tournament: APITournament,
    wallet:APIWalletResponse,
    gameBackground: String?,
    gameIcon: String?,
    onInfoClick:() ->Unit,
    gameId:String?
){
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 8.dp, bottom = 4.dp)
            .graphicsLayer { clip = true }
            .clip(RoundedCornerShape(4.dp))
    ){
        AsyncImage(model = gameBackground,
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            modifier = Modifier.fillMaxSize()

        )
        Column (
            modifier = Modifier
                .fillMaxSize()
                .padding(6.dp, 6.dp, 6.dp, 6.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier
                    .padding(12.dp, 0.dp, 0.dp, 0.dp)
                    .fillMaxWidth()
                    .height(44.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                //Title
                Column (
                    modifier = Modifier.padding(0.dp, 8.dp, 0.dp, 0.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .height(16.dp)
                    ) {
                        Text(
                            text = contestType,
                            style = MaterialTheme.typography.titleSmall.copy(fontSize = 16.sp),
                            color = MaterialTheme.colorScheme.onPrimary,
                            modifier = Modifier
                                .align(Alignment.Center)
                                .offset(y = -4.dp)
                        )
                    }
                    Spacer(modifier = Modifier.height(2.dp))
                    Box(modifier = Modifier
                        .height(16.dp)
                        .background(
                            MaterialTheme.colorScheme.surface,
                            RoundedCornerShape(2.dp)
                        )
                    ){
                        Text(
                            text = gameName.uppercase(),
                            style = MaterialTheme.typography.headlineSmall,
                            modifier = Modifier
                                .padding(4.dp, 0.dp)
                                .align(Alignment.CenterStart),
                            letterSpacing = 1.sp
                        )
                    }
                }
                AsyncImage(model = gameIcon,
                    contentDescription = null,
                    modifier = Modifier
                        .size(40.dp)
                        .border(1.dp, Color.Gray, RoundedCornerShape(4.dp))
                        .clip(RoundedCornerShape(4.dp))
                )
            }
            //Details Box
            Spacer(modifier = Modifier.height(4.dp))
            Box(
                modifier = Modifier.background(
                    MaterialTheme.colorScheme.surface,
                    RoundedCornerShape(4.dp)
                )
            ) {
                ContestListItem(
                    tournament = tournament,
                    wallet = wallet,
                    onInfoButton = onInfoClick,
                    titlesOnTop = false,
                    gameId = gameId,
                    lobbySelection = "Featured Tournament"
                )
            }
        }
    }
}

@Composable
fun LobbyContestInfo(
    title: String,
    value: String
){
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = title,
            color = MaterialTheme.colorScheme.outline,
            style = MaterialTheme.typography.labelMedium
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyLarge
        )
    }
}

@Composable
fun GameTileRowSpan(
    title: String,
    gamesList: List<GCGameData>,
    onClickTile: (GCGameData) -> Unit
){
    TileGroupTitle(title = title)
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp),
        horizontalArrangement = Arrangement.spacedBy(
            12.dp,
            Alignment.Start
        )
    ) {
        for (gameData in gamesList){
            GameTile(
                modifier = Modifier
                    .weight(1f)
                    .aspectRatio(1f)
                    .shadow(8.dp),
                iconUrl = gameData.tile_image_url
            ) {
                onClickTile(gameData)
            }
        }
    }
}

@Composable
fun GameTileRowScrollable(
    title: String,
    gamesList: List<Pair<GCGameData, GCRowItem>>,
    tileSize: Dp = 72.dp,
    horizontalPadding: Dp = 16.dp,
    onClickTile: (GCGameData) -> Unit
){
//    Spacer(Modifier.height(16.dp))
    TileGroupTitle(title = title)
    LazyRow(
        modifier = Modifier
            .height(tileSize),
        contentPadding = PaddingValues(start = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(
            horizontalPadding,
            Alignment.Start
        )
    ) {
        for (gameData in gamesList) {
            item {
                TaggedTile(
                    modifier = Modifier
                        .size(tileSize),
                    message = gameData.second.banner
                ) {
                    GameTile(
                        iconUrl = gameData.first.tile_image_url,
                        showShadow = false
                    ) {
                        onClickTile(gameData.first)
                    }
                }
            }
        }

        if (gamesList.isNotEmpty()){
            item{
                Spacer(modifier = Modifier.width(12.dp))
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun GameTileGrid(
    title: String,
    subtitle: String? = null,
    maxColumns: Int,
    gamesList: List<Pair<GCGameData, GCRowItem>>,
    onClickTile: (GCGameData) -> Unit
){
    TileGroupTitle(
        title = title,
        bottomPadding = if (subtitle != null) 0.dp else 6.dp
    )
    if (subtitle != null){
        AutosizeText(
            text = subtitle,
            style = MaterialTheme.typography.headlineLarge,
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.Normal,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(top = 2.dp, bottom = 12.dp),
            maxLines = 1,
        )
    }
    BoxWithConstraints(
        modifier = Modifier
            .fillMaxWidth()
    ){
        val screenWidth = this.maxWidth
        val columnSpacing = 16.dp

        // Calculate the item width (1/2 of the screen width minus left and right padding)
        val itemSize = ((screenWidth - columnSpacing)) / maxColumns.toFloat()
        val imageSize = itemSize - columnSpacing

        FlowRow(
            maxItemsInEachRow = maxColumns,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = (columnSpacing / 2) - .5.dp)
                .align(Alignment.Center)
        ) {
            for (gameData in gamesList){
                Box(
                    modifier = Modifier
                        .size(itemSize),
                ) {
                    GameTile(
                        iconUrl = gameData.first.tile_image_url,
                        modifier = Modifier
                            .size(imageSize)
                            .align(Alignment.Center),
                    ) {
                        onClickTile(gameData.first)
                    }

                    TaggedTile(
                        modifier = Modifier
                            .align(Alignment.TopStart),
                        message = gameData.second.banner
                    ) {
                    }
                }
            }

            //Fill tile row if odd number
            val rem = gamesList.size % maxColumns
            if(rem != 0)
            {
                for(i in 0 ..< maxColumns - rem)
                {
                    Box(
                        modifier = Modifier
                            .width(itemSize)
                            .aspectRatio(1.08333f)
                    )
                }
            }
        }
    }
}

@Composable
fun TileGroupTitle(
    title: String,
    topPadding: Dp = 0.dp,
    bottomPadding: Dp = 12.dp
){
    Text(
        text = title,
        style = MaterialTheme.typography.titleSmall,
        color = MaterialTheme.colorScheme.secondary,
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .padding(top = topPadding, bottom = bottomPadding)
    )
}

@Composable
fun FeaturedTournamentRow(
    tournaments:List<Pair<APITournament, GCRowItem>>,
    gamesList: Map<String,GCGameData>,
    wallet:APIWalletResponse,
    tileHeight: Dp = 136.dp,
    tileWidth: Dp = 330.dp,
    onInfoClick: (tournament:APITournament, gameData: GCGameData?) -> Unit,
){
    LazyRow(
        modifier = Modifier
            .height(tileHeight)
            .padding(bottom = 10.dp),
        contentPadding = PaddingValues(start = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(
            16.dp,
            Alignment.Start
        )
    ) {
        for (tournament in tournaments) {
            item {
                val gameData = gamesList[tournament.first.game_id]

                TaggedTile(
                    message = tournament.second.banner,
                    tagOffsetX = (-8).dp,
                    tagOffsetY = (-2).dp,
                    modifier = Modifier
                        .fillMaxHeight()
                        .width(tileWidth)
                ) {
                    FeaturedContest(
                        contestType = tournament.first.game_mode?.short_name ?: tournament.first.game_mode?.name ?: STR(R.string.standard),
                        gameName = tournament.first.game_display_name ?: STR(R.string.tournament),
                        tournament = tournament.first,
                        wallet = wallet,
                        gameBackground = gameData?.tournament_background_image_url,
                        gameIcon = gameData?.tile_image_url,
                        onInfoClick = {
                            onInfoClick(tournament.first, gameData)
                        },
                        gameId = gameData?.game_id
                    )
                }
            }
        }

        if (tournaments.isNotEmpty()){ // Add trailing padding to the end of the row
            item {
                Spacer(modifier = Modifier.width(12.dp))
            }
        }
    }
}

@Composable
fun FtueBanner(isGuest: Boolean = false){
    Box (
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.primary)
    ) {
        Image(
            painter = painterResource(R.drawable.img_ftue_banner),
            alignment = Alignment.TopCenter,
            contentScale = ContentScale.FillWidth,
            contentDescription = "FTUE Banner",
            alpha = 0.25f,
            modifier = Modifier
                .fillMaxSize()
                .fadingEdge()
        )
        Column (
            modifier = Modifier
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterVertically)
        ) {
            val textStyle = TextStyle(
                fontWeight = FontWeight.W700,
                fontStyle = FontStyle.Italic,
                fontSize = 36.sp,
                lineHeight = 39.6.sp,
                letterSpacing = 2.sp,
                shadow = Shadow(
                    color =  Color(0x40000000),
                    offset = Offset(0.0f, 8.0f),
                    blurRadius = 8.0f
                )
            )
            Spacer(modifier = Modifier.weight(1f))
            if (isGuest) {
                Text(
                    text = STR(R.string.play_a_free_guest_game_caps),
                    style = textStyle,
                    color = MaterialTheme.colorScheme.onPrimary,
                    textAlign = TextAlign.Center,
                )
            } else {
                Text(
                    text = STR(R.string.play_a_free_practice_game_caps),
                    style = textStyle,
                    color = MaterialTheme.colorScheme.onPrimary,
                    textAlign = TextAlign.Center
                )
                Text(
                    text = STR(R.string.and_learn_the_ropes),
                    style = MaterialTheme.typography.titleMedium.copy(fontSize = 18.sp),
                    color = MaterialTheme.colorScheme.onPrimary,
                    textAlign = TextAlign.Center,
                )
            }
            Spacer(modifier = Modifier.weight(1f))
        }
    }
}

@Composable
fun ErrorElement(errorMessage: String, onRefresh: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxHeight()
            .padding(horizontal = 48.dp),
        verticalArrangement = Arrangement.spacedBy(
            4.dp,
            Alignment.CenterVertically
        ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = STR(R.string.lobby_error),
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = errorMessage,
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center
        )
        Spacer(Modifier.height(16.dp))
        Text(
            text = STR(R.string.lobby_refresh_try_again),
            color = MaterialTheme.colorScheme.secondary,
            style = MaterialTheme.typography.bodyMedium
        )
        Spacer(Modifier.height(16.dp))
        CommonButton(title = STR(R.string.refresh)) {
            //gamesViewModel.loadData()
            onRefresh()
        }
    }
}
@Composable
fun BonusTooltip(enabled:Boolean = true,){
    if(!enabled){
        return
    }
    val wallet by WalletManager.instance.wallet.collectAsState()

    TooltipWithBorder(
        borderWidth = 2.dp,
        gapWidth = 20.dp,
        arrowPositionXNormalized = 0.55f,
        arrowMode = ArrowMode.Top,
        padding = 16.dp,
        cornerRadius = 8.dp,
        modifier = Modifier.clickableWithoutRipple {  }
    ) {
        Column(horizontalAlignment = Alignment.Start) {
            Row(horizontalArrangement = Arrangement.Start, verticalAlignment = Alignment.CenterVertically) {
                Image(modifier = Modifier
                    .size(18.dp),
                    painter = painterResource(R.drawable.ic_cash_single),
                    contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text(text = "You have ${wallet.bonus_balance.toDollarWithCommaString()} in Bonus Cash",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary,)
            }
            Spacer(modifier = Modifier.width(10.dp))
            Text(text = "Bonus Cash is automatically used first to\npay for your entry fees.",
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onSurface)
            Spacer(modifier = Modifier.height(10.dp))
            Text(text = "Look out for rewards to earn more!",
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onSurface)
        }
    }
}

@Composable
fun LobbySection(title: String,
                 icon:Int? = null,
                 subtitle: String? = null,
                 expandable: Boolean = true,
                 showDivider:Boolean = true,
                 contentComposable: @Composable () -> Unit
) {
    val expand = remember { mutableStateOf(true) }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .border(1.dp, color = Color(0xFFCED4DB))
    ) {

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .let {
                    if (expandable) {
                        it.clickable {
                            expand.value = !expand.value
                        }
                    } else {
                        it
                    }
                }
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp),
//                    .padding(top = 12.dp, bottom = 8.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    if (icon != null) {
                        Image(
                            painter = painterResource(id = icon),
                            contentDescription = null,
                            modifier = Modifier
                                .size(18.dp)
                                .padding(end = 2.dp)
                        )
                    }

                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.secondary,
                    )
                }

                if (expandable) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_arrow_down_24dp),
                        contentDescription = null,
                        colorFilter = ColorFilter.tint(Color(0xFF005FC8)),
                        modifier = Modifier
                            .rotate(
                                if (expand.value) {
                                    180.0f
                                } else {
                                    0.0f
                                }
                            )
                            .size(18.dp)
                    )
                }
            }
            if (subtitle != null) {
                Text(
                    text = subtitle,
                    fontWeight = FontWeight.Normal,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier
                        .background(Color.Transparent)
                        .padding(start = 16.dp)
                )
            }
            if(showDivider){
                HorizontalDivider(
                    thickness = 1.dp,
                    color = MaterialTheme.colorScheme.outline,
                )
            }
        }

        if (expand.value) {
            Box(
                Modifier
                    .fillMaxWidth()
            ) {
                contentComposable()
            }
        }
    }
}
@Composable
fun LobbyTopHighlight(
    isNewCarousel: Boolean,
    isFtue: Boolean,
    isGuest: Boolean,
) {
    val height = if (isFtue) {
        if (isGuest) {
            144.dp
        } else {
            144.dp
        }
    } else if (isGuest) {
        222.dp
    } else {
        if (isNewCarousel) {
            181.dp
        } else {
            240.dp
        }
    }
    Box(
        modifier = Modifier
            .height(height)
    ) {
        if (isFtue) {
            FtueBanner(isGuest)
        } else if (isGuest) {
            Image(
                painter = painterResource(R.drawable.img_banner_guest),
                alignment = Alignment.TopCenter,
                contentScale = ContentScale.FillHeight,
                contentDescription = STR(R.string.content_description_check),
                modifier = Modifier
                    .fillMaxSize()
                    .clickable { LoginViewModel.instance.fanduelSignupFromGuestMode() }
            )
        } else {
            if (isNewCarousel) {
                BrazeNewCarousel()
            } else {
                BrazeCarousel()
            }
        }
    }
}


@Composable
fun LobbyGuestSignup() {
    Box(
        modifier = Modifier
            .padding(horizontal = 16.dp, vertical = 12.dp)
            .clickable {
                LoginViewModel.instance.fanduelSignupFromGuestMode()
            }
    )
    {
        ContentBox(
            modifier = Modifier.fillMaxWidth(),
            bodyPaddingValues = PaddingValues(),
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(50.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .graphicsLayer { clip = true },
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_cash),
                    contentDescription = null,
                    contentScale = ContentScale.FillHeight,
                    modifier = Modifier
                        .wrapContentHeight(unbounded = true)
                        .height(92.dp)
                        .offset(x = (-28).dp)
                )

                Column(
                    modifier = Modifier
                        .fillMaxHeight()
                        .padding(horizontal = 12.dp),
                    verticalArrangement = Arrangement.spacedBy(
                        4.dp,
                        Alignment.CenterVertically
                    ),
                    horizontalAlignment = Alignment.Start
                ) {
                    Text(
                        text = STR(R.string.rewards_are_waiting_for_you),
                        style = MaterialTheme.typography.headlineMedium
                    )
                    Text(
                        text = STR(R.string.join_now_to_claim_bonus_cash),
                        color = MaterialTheme.colorScheme.secondary,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                Spacer(modifier = Modifier)
            }
        }
    }
}

val RECENTLY_PLAYED = "Recently Played"//todo: currently this section is hardcoded (followed iOS)

@Composable
fun buildGamesList(
    gamesViewModel: GamesViewModel,
    row: GCRow,
    gamesDict: Map<String, GCGameData>,
): List<Pair<GCGameData, GCRowItem>> {
    val gamesList: MutableList<Pair<GCGameData, GCRowItem>> =
        mutableListOf()
    val recentlyPlayedGames by gamesViewModel.recentlyPlayedGames.collectAsState()
    for (rowItem in row.items) {
        if (gamesDict.containsKey(rowItem.game_meta_datum_id)) {
            gamesDict[rowItem.game_meta_datum_id]?.let {
                if (gamesViewModel.getGameDataById(it.game_id) != null) {
                    if (row.title == RECENTLY_PLAYED) {
                        if (recentlyPlayedGames.contains(
                                it.game_id
                            )
                        ) {
                            gamesList.add(
                                Pair(
                                    it,
                                    rowItem
                                )
                            )
                        }
                    } else {
                        gamesList.add(Pair(it, rowItem))
                    }
                }
            }
        }
    }

    if (row.title == RECENTLY_PLAYED) {
        gamesList.sortBy {
            recentlyPlayedGames.indexOf(it.first.game_id)
        }
    }

    return gamesList
}

@Composable
fun buildTournamentList(
    gamesViewModel: GamesViewModel,
    row: GCRow,
    catalogResponse: GameCatalogResponse,
    isTurboActive: Boolean,
): Pair<List<Pair<APITournament, GCRowItem>>, Map<String, GCGameData>> {
    val featuredTournaments by gamesViewModel.featuredTournaments.collectAsState()

    val availableTournaments: MutableList<Pair<APITournament, GCRowItem>> =
        mutableListOf()
    val availableGames: MutableMap<String, GCGameData> =
        mutableMapOf()

    row.items.forEach {
        val tournament =
            featuredTournaments[it.tournament_id]

        if (tournament != null) {
            var available = tournament.is_turbo != true || (tournament.is_turbo == true && isTurboActive)

            //temp fix for https://worldwinner.atlassian.net/browse/FX-2818, as is_turbo is not added to the featured tournaments
            if(tournament.is_turbo == null && tournament.name?.lowercase()?.contains("turbo") == true && !isTurboActive){
                available = false
            }

            if(available){
                availableTournaments.add(
                    Pair(
                        tournament,
                        it
                    )
                )

                for (pair in catalogResponse.data.games) {
                    if (pair.value.game_id == tournament.game_id) {
                        availableGames[tournament.game_id] =
                            pair.value
                    }
                }
            }
        }
    }

    return availableTournaments to availableGames
}

@Composable
fun LobbyGameContent(
    gamesViewModel: GamesViewModel,
    rows: List<GCRow>,
    gamesDict: Map<String, GCGameData>,
    wallet: APIWalletResponse,
    catalogResponse: GameCatalogResponse,
    isFtue: Boolean,
    isGuest: Boolean,
    onGameTileClicked: (NavManager, GCGameData, String?) -> Unit,
    onInfoClick: (tournament:APITournament, gameData: GCGameData?) -> Unit,
) {
    var isTurboActive = true

    val ftueGamesTitle = if(isGuest) STR(R.string.choose_a_game) else STR(R.string.browse_games)
    val ftueGamesSubtitle = if (isGuest) STR(R.string.try_a_free_practice_game_guest) else
        STR(R.string.try_a_free_practice_game_before_facing_another_player)

    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    // 4 tiles, 16dp padding on left, 16dp between items, 16 dp padding on right
    val gameTileItemWidth = (screenWidth - 16.dp - (3 * 16.dp) - 16.dp) / 4
    val featuredBannerWidth = screenWidth - 2 * 16.dp

    for ((index, row) in rows.withIndex()) {
        // Skip non-game-grid rows if in Ftue or is guest
        if ((
                    (isFtue || isGuest) &&
                            (row.row_type != RowTypes.GAME ||
                                    row.style != RowStyles.GRID) ||
                            (row.for_guest_mode != isGuest) //guest mode filter
                    )
        ) {
            continue
        }

        // Show before non-river elements or after the first river
        if (
            index == 0 && (row.style != RowStyles.RIVER || row.row_type != RowTypes.GAME) ||
            index == 1 && rows[0].style == RowStyles.RIVER && rows[0].row_type == RowTypes.GAME
        ) {
            if (!isFtue && !isGuest && wallet.bonus_balance.toDouble() > 0) {
                Spacer(Modifier.height(5.dp))
                Box(modifier = Modifier.padding(horizontal = 16.dp)) {
                    ContentBox(
                        titleComposable = {
                            BonusCashBanner(value = wallet.bonus_balance)
                        }
                    )
                }
            }
        }

        when (row.row_type) {
            RowTypes.GAME -> {
                RowTypesGame(
                    gamesViewModel = gamesViewModel,
                    row = row,
                    gamesDict = gamesDict,
                    gameTileItemWidth = gameTileItemWidth,
                    isFtue = isFtue,
                    ftueGamesTitle = ftueGamesTitle,
                    ftueGamesSubtitle = ftueGamesSubtitle,
                    onGameTileClicked = onGameTileClicked,
                )
            }

            RowTypes.TOURNAMENT -> {
                RowTypesTournament(
                    gamesViewModel = gamesViewModel,
                    row = row,
                    catalogResponse = catalogResponse,
                    wallet = wallet,
                    featuredBannerWidth = featuredBannerWidth,
                    isTurboActive = isTurboActive,
                    onInfoClick = onInfoClick,
                )
            }
        }
    }
}

@Composable
private fun RowTypesGame(
    gamesViewModel: GamesViewModel,
    row: GCRow,
    gamesDict: Map<String, GCGameData>,
    gameTileItemWidth: Dp,
    isFtue: Boolean,
    ftueGamesTitle: String,
    ftueGamesSubtitle: String,
    onGameTileClicked: (NavManager, GCGameData, String?) -> Unit,
) {
    val navManager = LocalNavManager.current

    val gamesList = buildGamesList(
        gamesViewModel = gamesViewModel,
        row = row,
        gamesDict = gamesDict,
    )

    if (gamesList.isEmpty()) {
        return
    }

    when (row.style) {
        RowStyles.RIVER -> {
            GameTileRowScrollable(
                title = row.title,
                gamesList = gamesList.subList(
                    0,
                    minOf(
                        gamesList.size,
                        (row.max_items_to_show
                            ?: gamesList.size)
                    )
                ),
                horizontalPadding = if (row.title == RECENTLY_PLAYED || (row.items.count() <= 4)) 16.dp else 12.dp,
                tileSize = gameTileItemWidth,
                onClickTile = { gameData: GCGameData ->
                    onGameTileClicked.invoke(navManager, gameData, row.title)
                }
            )
        }

        RowStyles.GRID -> {
            GameTileGrid(
                title = if (isFtue) ftueGamesTitle else row.title,
                subtitle = if (isFtue) ftueGamesSubtitle else null,
                maxColumns = 3,
                gamesList = gamesList,
                onClickTile = { gameData: GCGameData ->
                    onGameTileClicked(navManager, gameData, row.title)
                }
            )
        }

        else -> {

        }
    }
}

@Composable
private fun RowTypesTournament(
    gamesViewModel: GamesViewModel,
    row: GCRow,
    catalogResponse: GameCatalogResponse,
    wallet: APIWalletResponse,
    featuredBannerWidth: Dp,
    isTurboActive: Boolean,
    onInfoClick: (APITournament, GCGameData?) -> Unit,
) {
    val (availableTournaments, availableGames) = buildTournamentList(
        gamesViewModel = gamesViewModel,
        row = row,
        catalogResponse = catalogResponse,
        isTurboActive = isTurboActive,
    )

    if (availableTournaments.size > 1) {
        if (row.show_title == true) {
            Spacer(Modifier.height(10.dp))
            TileGroupTitle(
                title = row.title,
                topPadding = 1.dp,
                bottomPadding = 1.dp,
            )
        }
        Spacer(Modifier.height(4.dp))
        FeaturedTournamentRow(
            tournaments = availableTournaments,
            gamesList = availableGames,
            tileWidth = featuredBannerWidth * .9f,
            wallet = wallet,
            onInfoClick = onInfoClick,
        )
    } else if (availableTournaments.isNotEmpty()) {
        val tournament = availableTournaments[0]
        val gameData =
            availableGames[tournament.first.game_id]
        if (row.show_title == true) {
            Spacer(Modifier.height(16.dp))
            TileGroupTitle(
                title = row.title,
                topPadding = 1.dp,
                bottomPadding = 1.dp,
            )
        }

        TaggedTile(
            message = tournament.second.banner,
            tagOffsetX = (-8).dp,
            modifier = Modifier
                .height(116.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            FeaturedContest(
                contestType = tournament.first.game_mode?.short_name
                    ?: tournament.first.game_mode?.name
                    ?: STR(R.string.standard),
                gameName = tournament.first.game_display_name
                    ?: STR(R.string.tournament),
                tournament = tournament.first,
                wallet = wallet,
                gameBackground = gameData?.tournament_background_image_url,
                gameIcon = gameData?.tile_image_url,
                onInfoClick = {
                    onInfoClick(
                        tournament.first,
                        gameData
                    )
                },
                gameId = gameData?.game_id
            )
        }
    }
}
@Composable
fun NewLobbyGameContent(
    lobbyViewModel: LobbyViewModel,
    catalog: APICatalog,
    isFtue: Boolean,
    isGuest: Boolean,
    onGameTileClicked: (NavManager, GCGameData, String?) -> Unit,
    onInfoClick: (tournament:APITournament, gameData: GCGameData?) -> Unit,
) {
    val navManager = LocalNavManager.current
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    // 4 tiles, 16dp padding on left, 16dp between items, 16 dp padding on right
    val gameTileItemWidth = (screenWidth - 16.dp - (3 * 16.dp) - 16.dp) / 4
    val featuredBannerWidth = screenWidth - 2 * 16.dp

    val recentlyPlayedSection = lobbyViewModel.generateRecentlyPlayedSection(catalog)

    NewRecentlyPlayedSection(recentlyPlayedSection,catalog,gameTileItemWidth,onGameTileClicked)

    catalog.sections.forEach { section ->
        if(section.section_type != APICatalogSectionTypeEnum.RECENTLY_PLAYED){
            NormalTournamentSection(lobbyViewModel,section,catalog,gameTileItemWidth,onGameTileClicked,onInfoClick)
        }
    }
}
@Composable
fun NewRecentlyPlayedSection(
    section:APICatalogSection,
    catalog: APICatalog,
    gameTileItemWidth:Dp,
    onGameTileClicked: (NavManager, GCGameData, String?) -> Unit,
){
    val navManager = LocalNavManager.current

    if(section.swimlanes.isNotEmpty()){
        val games = section.swimlanes[0].game_items
        if(games.isNotEmpty()){
            LobbySection(title = section.title, expandable = section.is_collapsible) {
                NewGameTileRowScrollable(
                    games = games,
                    catalog = catalog,
                    horizontalPadding = if ((games.size <= 4)) 16.dp else 12.dp,
                    tileSize = gameTileItemWidth,
                    onClickTile = { gameData: GCGameData ->
                        onGameTileClicked.invoke(navManager, gameData, section.title)
                    }
                )
            }
        }
    }
}
@Composable
fun NormalTournamentSection(
    lobbyViewModel: LobbyViewModel,
    section:APICatalogSection,
    catalog: APICatalog,
    gameTileItemWidth:Dp,
    onGameTileClicked: (NavManager, GCGameData, String?) -> Unit,
    onInfoClick: (tournament:APITournament, gameData: GCGameData?) -> Unit,
){
    val navManager = LocalNavManager.current
    val showNavTitle = section.swimlanes.size > 1
    LobbySection(title = section.title, showDivider = !showNavTitle, expandable = section.is_collapsible){
        if(section.swimlanes.isNotEmpty()){
            val selectedNav = remember {mutableStateOf<Int?>(0)}

            Column(modifier = Modifier.fillMaxWidth(), verticalArrangement = Arrangement.spacedBy(8.dp)) {
                if(showNavTitle){
                    SectionFilterNav(section){
                        selectedNav.value = it
                    }
                }

                section.swimlanes.forEachIndexed { index, swimlane ->
                    if(index == selectedNav.value || selectedNav.value == null){
                        //title
                        if(selectedNav.value == null){
                            Row(modifier = Modifier.padding(horizontal = 16.dp),verticalAlignment = Alignment.CenterVertically){
                                if(swimlane.is_new){
                                    NewTag()
                                }
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(text = swimlane.title, color = Color(0xFF005FC8),
                                    style = MaterialTheme.typography.labelMedium.copy(
                                        fontWeight = FontWeight.W400,
                                        fontSize = 14.sp,
                                    ),)
                            }
                        }
                        //spotlight
                        if(swimlane.spotlight_item != null){
                            SpotlightItem(spotlightItem = swimlane.spotlight_item, catalog = catalog)
                        }
                        //games
                        if(swimlane.game_items.isNotEmpty()){
                            if(swimlane.games_layout == APICatalogSwimlaneGamesLayoutEnum.HORIZONTAL){
                                NewGameTileRowScrollable(
                                    games = swimlane.game_items,
                                    catalog = catalog,
                                    horizontalPadding = if ((swimlane.game_items.size <= 4)) 16.dp else 12.dp,
                                    tileSize = gameTileItemWidth,
                                ){ gameData: GCGameData ->
                                    onGameTileClicked.invoke(navManager, gameData, section.title)
                                }
                            }else{
                                NewGameTileGrid(maxColumns = 3, games = swimlane.game_items, catalog = catalog){ gameData: GCGameData ->
                                    onGameTileClicked.invoke(navManager, gameData, section.title)
                                }
                            }
                        }
                        //tournaments
                        if(swimlane.tournament_items.isNotEmpty()){
                            NewFeaturedTournamentRow(lobbyViewModel,catalog,swimlane.tournament_items){tournament, gameData ->
                                onInfoClick(tournament,gameData)
                            }
                        }
                    }
                }
            }
        }
    }
}
@Composable
fun SectionFilterNav(
    section:APICatalogSection,
    onClick: (index:Int?) -> Unit
){
    val nav = section.swimlanes.mapNotNull { it.filter_pill_title }
    if(nav.isNotEmpty()){
        val navList: MutableList<Pair<Int,@Composable ()->Unit>> = mutableListOf()
        val selectedNav = remember {mutableStateOf<Int?>(0)}

        Column(modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 8.dp)
        ) {
            Row(modifier = Modifier
                .height(56.dp)
                .padding(horizontal = 16.dp)
                .offset(-8.dp, -4.dp)
                , verticalAlignment = Alignment.CenterVertically) {
                if(nav.size > 4){
                    val selected = null == selectedNav.value
                    FilterNavItem("View All",null,selected,false){
                        selectedNav.value = null
                        onClick(null)
                    }

                    VerticalDivider(thickness = 1.dp,
                        color = MaterialTheme.colorScheme.outline,
                        modifier = Modifier
                            .height(56.dp)
                            .padding(horizontal = 12.dp)
                            .offset(8.dp, 4.dp),
                    )
                }
                section.swimlanes.forEachIndexed { index, it ->
                    if(it.filter_pill_title != null){
                        val selected = index == selectedNav.value
                        navList.add(Pair(index) {
                            FilterNavItem(it.filter_pill_title, index, selected, it.is_new) {
                                selectedNav.value = index
                                onClick(index)
                            }
                        })
                    }
                }
                LazyRow(modifier = Modifier
                    .fillMaxWidth()
                    .height(44.dp),
                    horizontalArrangement = Arrangement.spacedBy(
                        12.dp,
                        Alignment.Start
                    ),
                    verticalAlignment = Alignment.CenterVertically) {
                    for(tab in navList){
                        item(tab.first) {
                            tab.second()
                        }
                    }
                    item{
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                }
            }

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(2.dp)
//                    .background(ShadowFade)
                    .shadow(2.dp)
            )
        }

    }
}
@Composable
fun SpotlightItem(spotlightItem: APICatalogSpotlightItem,catalog: APICatalog? = null){
    Row(modifier = Modifier
        .padding(horizontal = 16.dp)
        .height(100.dp)) {
        AsyncImage(
            model = spotlightItem.image,
            contentDescription = null,
            modifier = Modifier
                .size(100.dp)
                .align(Alignment.CenterVertically)
                .clip(RoundedCornerShape(6.dp)),
            contentScale = ContentScale.FillBounds,
        )
        Spacer(modifier = Modifier.width(16.dp))
        Box(modifier = Modifier.fillMaxWidth()){
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                TaggedTile(
                    modifier = Modifier,
                    message = spotlightItem.banner ?: BannerEnum.NEW_GAME,
                    bannerData = catalog?.getBannerData(spotlightItem.banner),
                ) {
                }
                Text(text = spotlightItem.title, style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.W700,
                    fontSize = 18.sp,
                    color = Color(0xFF05285A)
                ))
                Text(
                    text = spotlightItem.description, maxLines = 3, style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.W400,
                        fontSize = 12.sp,
                        color = Color(0xFF05285A)
                    ), lineHeight = 15.sp, overflow = TextOverflow.Ellipsis)
//                AutosizeText(
//                    text = spotlightItem.description, maxLines = 3, style = MaterialTheme.typography.bodyMedium.copy(
//                    fontWeight = FontWeight.W400,
//                    fontSize = 12.sp,
//                    color = Color(0xFF05285A)
//                ))
            }
            Row(modifier = Modifier
                .align(Alignment.TopEnd)
                .clickable {
                    if (spotlightItem.cta_link == APICatalogCtaLinkEnum.GAME_LOBBY) {
                        val gcGameData = catalog?.data?.games?.get(spotlightItem.game_meta_datum_id)
                        if (gcGameData != null) {
                            PlayViewModel.instance.jumpToPlayView(gcGameData.game_id)
                        }
                    } else if (spotlightItem.cta_link == APICatalogCtaLinkEnum.ADD_FUNDS) {
                        FDManager.instance.showWebviewFD(FDWebviewPage.Deposit)
                    } else {
                        Logger.d("unknown cta_link: ${spotlightItem.cta_link}")
                    }
                },
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically){
                Text(text = spotlightItem.cta_label ?:"All Contests", color = Color(0xFF005FC8),
                    style = MaterialTheme.typography.labelMedium.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 12.sp,
                    ),)
                Icon(
                    imageVector = IcBack,
                    tint = Color(0xFF005FC8),
                    contentDescription = null,
                    modifier = Modifier
                        .size(10.dp)
                        .rotate(degrees = 180f)
                        .offset(0.dp, 1.dp),
                )
            }
        }
    }
}
@Composable
fun NewGameTileRowScrollable(
    games: List<APICatalogGameItem>,
    catalog: APICatalog,
    tileSize: Dp = 72.dp,
    horizontalPadding: Dp = 16.dp,
    onClickTile: (GCGameData) -> Unit
){
    LazyRow(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .padding(vertical = 12.dp)
            .height(tileSize),
        contentPadding = PaddingValues(start = 0.dp),
        horizontalArrangement = Arrangement.spacedBy(
            horizontalPadding,
            Alignment.Start
        )
    ) {
        val gamesList: MutableList<Pair<GCGameData, APICatalogGameItem>> = mutableListOf()
        games.forEach {
            val gcGameData = catalog.data.games.get(it.game_meta_datum_id)
            if(gcGameData!=null){
                gamesList.add(Pair(gcGameData,it))
            }
        }

        for (gameData in gamesList) {
            item {
//                TaggedTile(
//                    modifier = Modifier
//                        .size(tileSize),
//                    message = gameData.second.banner,
//                    bannerData = catalog.getBannerData(gameData.second.banner),
//                ) {
                    GameTile(
                        iconUrl = gameData.first.tile_image_url,
                    modifier = Modifier
                        .size(tileSize),
                        showShadow = false
                    ) {
                        onClickTile(gameData.first)
                    }
//                }
            }
        }

        if (gamesList.isNotEmpty()){
            item{
                Spacer(modifier = Modifier.width(12.dp))
            }
        }
    }
}
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun NewGameTileGrid(
    maxColumns: Int,
    games: List<APICatalogGameItem>,
    catalog: APICatalog,
    onClickTile: (GCGameData) -> Unit
){
    BoxWithConstraints(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 16.dp)
//            .padding(horizontal = 16.dp)
    ){
        val screenWidth = this.maxWidth
        val columnSpacing = 16.dp

        // Calculate the item width (1/2 of the screen width minus left and right padding)
        val itemSize = ((screenWidth - columnSpacing)) / maxColumns.toFloat()
        val imageSize = itemSize - columnSpacing

        FlowRow(
            maxItemsInEachRow = maxColumns,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = (columnSpacing / 2) - .5.dp)
                .align(Alignment.Center)
        ) {
            val gamesList: MutableList<Pair<GCGameData, APICatalogGameItem>> = mutableListOf()
            games.forEach {
                val gcGameData = catalog.data.games.get(it.game_meta_datum_id)
                if(gcGameData!=null){
                    gamesList.add(Pair(gcGameData,it))
                }
            }

            for (gameData in gamesList){
                Box(
                    modifier = Modifier
                        .size(itemSize),
                ) {
                    GameTile(
                        iconUrl = gameData.first.tile_image_url,
                        modifier = Modifier
                            .size(imageSize)
                            .align(Alignment.Center),
                    ) {
                        onClickTile(gameData.first)
                    }

                    TaggedTile(
                        modifier = Modifier
                            .align(Alignment.TopStart),
                        message = gameData.second.banner,
                        bannerData = catalog.getBannerData(gameData.second.banner)
                    ) {
                    }
                }
            }

            //Fill tile row if odd number
            val rem = gamesList.size % maxColumns
            if(rem != 0)
            {
                for(i in 0 ..< maxColumns - rem)
                {
                    Box(
                        modifier = Modifier
                            .width(itemSize)
                            .aspectRatio(1.08333f)
                    )
                }
            }
        }
    }
}
@Composable
fun NewFeaturedTournamentRow(
    lobbyViewModel: LobbyViewModel,
    catalog: APICatalog,
    tournaments:List<APICatalogTournamentItem>,
    tileHeight: Dp = 136.dp,
    onInfoClick: (tournament:APITournament, gameData: GCGameData?) -> Unit,
){
    val featuredTournaments by lobbyViewModel.featuredTournaments.collectAsState()
    val wallet: APIWalletResponse by WalletManager.instance.wallet.collectAsState()
    val screenWidth = LocalConfiguration.current.screenWidthDp.dp
    // 16dp padding on left, 16 dp padding on right
    val tileWidth: Dp = if(tournaments.size > 1) 330.dp else screenWidth - 32.dp

    LazyRow(
        modifier = Modifier
            .fillMaxWidth()
            .height(tileHeight)
            .padding(horizontal = 16.dp),
//            .padding(bottom = 10.dp),
        contentPadding = PaddingValues(start = 0.dp),
        horizontalArrangement = Arrangement.spacedBy(
            16.dp,
            Alignment.Start
        ),
        userScrollEnabled = tournaments.size > 1
    ) {
        for (tournament in tournaments) {
            val gameData = catalog.data.games.values.firstOrNull { it.game_id == tournament.game_id  }
            val tournamentData = featuredTournaments.get(tournament.tournament_id)

            if(gameData != null && tournamentData != null){
                item {
                    TaggedTile(
                        message = tournament.banner,
                        bannerData = catalog.getBannerData(tournament.banner),
                        tagOffsetX = (-8).dp,
                        tagOffsetY = (-2).dp,
                        modifier = Modifier
                            .fillMaxHeight()
                            .width(tileWidth)
                    ) {
                        FeaturedContest(
                            contestType = tournamentData.game_mode?.short_name ?: tournamentData.game_mode?.name ?: STR(R.string.standard),
                            gameName = tournamentData.game_display_name ?: STR(R.string.tournament),
                            tournament = tournamentData,
                            wallet = wallet,
                            gameBackground = gameData.tournament_background_image_url,
                            gameIcon = gameData.tile_image_url,
                            onInfoClick = {
                                onInfoClick(tournamentData, gameData)
                            },
                            gameId = gameData.game_id
                        )
                    }
                }
            }else{
                if(gameData == null){
                    println("not found game data for ${tournament.game_id}")
                }else{
                    println("not found tournament data for ${tournament.tournament_id}")
                }
            }
        }

        if (tournaments.isNotEmpty()){ // Add trailing padding to the end of the row
            item {
                Spacer(modifier = Modifier.width(12.dp))
            }
        }
    }
}
@Composable
fun NewTag(){
    Box(modifier = Modifier
        .size(33.dp, 15.dp)
        .background(Color(0xFFF87A1E), RoundedCornerShape(2.dp)),
        contentAlignment = Alignment.Center) {
        Text(text = "NEW", color = Color.White, style = MaterialTheme.typography.displayMedium.copy(fontWeight = FontWeight.W400, fontSize = 12.sp, fontStyle = FontStyle.Normal))
    }
}
@Composable
fun FilterNavItem(title:String,index:Int?,selected:Boolean,isNew:Boolean,onClick: (index:Int?) -> Unit){
    Box(modifier = Modifier){
        Button(
            modifier = Modifier.offset(8.dp,4.dp),
            shape = RoundedCornerShape(16.dp),
            contentPadding = PaddingValues(12.dp),
            colors = ButtonDefaults.buttonColors(containerColor = if(selected)Color(0xFF005FC8) else Color(0xFFEAF0F6), contentColor = if(selected)Color.Black else Color.White),
            onClick = {
                onClick(index)
            }){
            Text(title,
                textAlign = TextAlign.Center,
                color = if(selected)Color.White else Color(0xFF011638),
                style = if(selected)MaterialTheme.typography.bodyMedium else MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.W400))
        }
        if(isNew){
            NewTag()
        }
    }
}
@Preview
@Composable
fun Preview(){

    Column(modifier = Modifier
        .background(Color.White)
        .fillMaxSize()){
        val item = APICatalogSpotlightItem(
            banner = "NEW_GAME",
            title = "5050 Tournament",
            description = "New tournament type for Fairway Frenzy. Top 50% advance each round until two remain for a final head-to-head.",
            image = "https://qa1-s3-assets-cdn.wwqa-fd.com/spotlight_catalog_items/b80f38c4-9c9d-46fe-b9b0-e1232d4bf2a5.png",
            cta_link = APICatalogCtaLinkEnum.GAME_LOBBY,
            cta_label = "All Contests",
            game_meta_datum_id = null,//018fa7ff-8877-78cc-93a0-1bdfc3a271a2
        )
        SpotlightItem(item)
        FilterNavItem("Sports",1,true,true,){}
        FilterNavItem("Sports",1,false,false){}

    }
}