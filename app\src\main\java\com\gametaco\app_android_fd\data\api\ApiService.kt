package com.gametaco.app_android_fd.data.api

import com.gametaco.app_android_fd.data.entity.APIAuthToken
import com.gametaco.app_android_fd.data.entity.APICatalog
import com.gametaco.app_android_fd.data.entity.APICatalogVersion
import com.gametaco.app_android_fd.data.entity.APIDailyDoorCollectedRewards
import com.gametaco.app_android_fd.data.entity.APIDailyDoorStatus
import com.gametaco.app_android_fd.data.entity.APIDailyDoorStatusRequest
import com.gametaco.app_android_fd.data.entity.APIGameTournamentsResponse
import com.gametaco.app_android_fd.data.entity.APIGameTournamentsResponseV2
import com.gametaco.app_android_fd.data.entity.APIGamesResponse
import com.gametaco.app_android_fd.data.entity.APIGeocomplyLicenseResponse
import com.gametaco.app_android_fd.data.entity.APIGeocomplyTokenExchangeRequest
import com.gametaco.app_android_fd.data.entity.APIGeocomplyTokenExchangeResponse
import com.gametaco.app_android_fd.data.entity.APIGoalsCollectableRequest
import com.gametaco.app_android_fd.data.entity.APIGoalsCollectableResponse
import com.gametaco.app_android_fd.data.entity.APIGoalsCollectedResponse
import com.gametaco.app_android_fd.data.entity.APIGoalsCompletableResponse
import com.gametaco.app_android_fd.data.entity.APIGoalsStatusResponse
import com.gametaco.app_android_fd.data.entity.APIGuestSignupResponse
import com.gametaco.app_android_fd.data.entity.APILoginRequest
import com.gametaco.app_android_fd.data.entity.APIMockLoginRequest
import com.gametaco.app_android_fd.data.entity.APIMaintenanceModeStatus
import com.gametaco.app_android_fd.data.entity.APIPlayedGameIDResponse
import com.gametaco.app_android_fd.data.entity.APIPlayedGamesResponse
import com.gametaco.app_android_fd.data.entity.APIPostGoalsCollectableResponse
import com.gametaco.app_android_fd.data.entity.APIPostScoreRequest
import com.gametaco.app_android_fd.data.entity.APIPostScoreRequestGuest
import com.gametaco.app_android_fd.data.entity.APIPostScoreResponseGuest
import com.gametaco.app_android_fd.data.entity.APIRecentlyPlayedGamesResponse
import com.gametaco.app_android_fd.data.entity.APITournamentEntryStartRequest
import com.gametaco.app_android_fd.data.entity.APITournamentEntryStartRequestGuest
import com.gametaco.app_android_fd.data.entity.APITournamentEntryStartResponse
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntry
import com.gametaco.app_android_fd.data.entity.APITournamentInstanceEntryList
import com.gametaco.app_android_fd.data.entity.APITournamentPlayerProfile
import com.gametaco.app_android_fd.data.entity.APITournamentReEntry
import com.gametaco.app_android_fd.data.entity.APITournamentRegisterReplayURLRequest
import com.gametaco.app_android_fd.data.entity.APITournamentReplayURLResponse
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinRequest
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinRequestGuest
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinResponse
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinResponseGuest
import com.gametaco.app_android_fd.data.entity.APITournamentsReEntryRequest
import com.gametaco.app_android_fd.data.entity.APITournamentsResponse
import com.gametaco.app_android_fd.data.entity.APIUserGameStats
import com.gametaco.app_android_fd.data.entity.APIWalletResponse
import com.gametaco.app_android_fd.data.entity.GameCatalogResponse
import com.gametaco.app_android_fd.data.entity.GameCatalogVersion
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query
import retrofit2.http.Url

interface ApiService {

    @WithAuthentication("")
    @GET("/api/public/app-session")
    suspend fun getAppSession(

    ) : Response<Void>

    @POST("/api/public/login")
    suspend fun login(@Body request: APILoginRequest

    ) : Response<APIAuthToken>
    
    @POST("/api/public/mock-login")
    suspend fun mockLogin(@Body request: APIMockLoginRequest
    
    ) : Response<APIAuthToken>


    @WithAuthentication("")
    @DELETE("/api/public/logout")
    suspend fun logout(

    ) : Response<Void>



    @WithAuthentication("")
    @GET("/api/public/catalog/document")
    suspend fun getGameCatalog(

    ) : Response<GameCatalogResponse>

    @WithAuthentication("")
    @GET("/api/public/catalog/version")
    suspend fun getGameCatalogVersion(

    ) : Response<GameCatalogVersion>

    @WithAuthentication("")
    @GET("/api/public/home-catalog/document")
    suspend fun getHomeCatalog(

    ) : Response<APICatalog>

    @WithAuthentication("")
    @GET("/api/public/home-catalog/version")
    suspend fun getHomeCatalogVersion(

    ) : Response<APICatalogVersion>

    @WithAuthentication("")
    @GET("/api/public/entries")
    suspend fun getTournamentEntries(@Query("cursor", encoded = true) cursor: String?

    ) : Response<APITournamentInstanceEntryList>

    @WithAuthentication("")
    @GET("/api/public/entries/{id}")
    suspend fun getTournamentEntry(@Path("id") id: String

    ) : Response<APITournamentInstanceEntry>

    @WithAuthentication("")
    @POST("/api/public/entries/{id}/re-entries")
    suspend fun postTournamentReEntries(@Path("id") id: String, @Body request: APITournamentsReEntryRequest

    ) : Response<APITournamentReEntry>


    @WithAuthentication("")
    @POST("/api/public/entry/score")
    suspend fun postTournamentEntryScore(@Body request: APIPostScoreRequest

    ) : Response<APIUserGameStats>


    @WithAuthentication("")
    @POST("/api/public/entry/start")
    suspend fun postTournamentEntryStart(@Body request: APITournamentEntryStartRequest

    ) : Response<APITournamentEntryStartResponse>


    @WithAuthentication("")
    @GET("/api/public/games")
    suspend fun getGames(

    ) : Response<APIGamesResponse>


    @WithAuthentication("")
    @GET("/api/public/games/{id}/tournaments")
    suspend fun getTournamentsByGameIdSorted(@Path("id") id: String

    ) : Response<APIGameTournamentsResponse>

    @WithAuthentication("")
    @GET("/api/public/games/{id}/tournaments_v2")
    suspend fun getTournamentsByGameIdSortedV2(@Path("id") id: String

    ) : Response<APIGameTournamentsResponseV2>

    @WithAuthentication("")
    @GET("/api/public/geocomply/license")
    suspend fun getGeocomplyLicense(

    ) : Response<APIGeocomplyLicenseResponse>


    @WithAuthentication("")
    @POST("/api/public/geocomply/token-exchange")
    suspend fun postGeocomplyTokenExchange(@Body request: APIGeocomplyTokenExchangeRequest

    ) : Response<APIGeocomplyTokenExchangeResponse>


    @WithAuthentication("")
    @GET("/api/public/goals/collectable")
    suspend fun getGoalsCollectable(

    ) : Response<APIGoalsCollectableResponse>

    @WithAuthentication("")
    @POST("/api/public/goals/collectable")
    suspend fun postGoalsCollectable(@Body request: APIGoalsCollectableRequest

    ) : Response<APIPostGoalsCollectableResponse>


    @WithAuthentication("")
    @GET("/api/public/goals/collected")
    suspend fun getGoalsCollected(

    ) : Response<APIGoalsCollectedResponse>


    @WithAuthentication("")
    @GET("/api/public/goals/completable")
    suspend fun getGoalsCompletable(

    ) : Response<APIGoalsCompletableResponse>

    @WithAuthentication("")
    @GET("/api/public/daily-door/status")
    suspend fun getDailyDoorStatus(@Query("current_date: String", encoded = true) current_date:String?,
                                   @Query("is_digest") is_digest: Boolean

    ) : Response<APIDailyDoorStatus>

    @WithAuthentication("")
    @GET("/api/public/daily-door/collected-rewards")
    suspend fun getDailyDoorCollectedRewards(

    ) : Response<APIDailyDoorCollectedRewards>

    @WithAuthentication("")
    @GET("/api/public/me")
    suspend fun getMe(

    ) : Response<APIMe>


    @WithAuthentication("")
    @GET("/api/public/played-games")
    suspend fun getPlayedGames(

    ) : Response<APIPlayedGamesResponse>


    @WithAuthentication("")
    @GET("/api/public/played-games/{id}")
    suspend fun getPlayedGameByID(@Path("id") id: String

    ) : Response<APIPlayedGameIDResponse>

    @WithAuthentication("")
    @GET("/api/public/recently-played-games")
    suspend fun getRecentlyPlayedGames(

    ) : Response<APIRecentlyPlayedGamesResponse>

    @WithAuthentication("")
    @POST("/api/public/potential-deposit/start")
    suspend fun postPotentialDepositStart(

    ) : Response<Void>

    @WithAuthentication("")
    @POST("/api/public/potential-deposit/end")
    suspend fun postPotentialDepositEnd(

    ) : Response<Void>


    @WithAuthentication("")
    @GET("/api/public/tournaments")
    suspend fun getTournamentsByGameID(@Query("game_id", encoded = true) game_id: String

    ) : Response<APITournamentsResponse>

    @WithAuthentication("")
    @GET("/api/public/tournaments")
    suspend fun getTournamentsByTournamentIDs(@Query("tournament_ids", encoded = true) tournament_ids: String

    ) : Response<APITournamentsResponse>

    @WithAuthentication("")
    @POST("/api/public/tournaments/join")
    suspend fun postJoinTournament(@Body request: APITournamentsJoinRequest

    ) : Response<APITournamentsJoinResponse>

    @WithAuthentication("")
    @GET("/api/public/wallet")
    suspend fun getWallet(

    ) : Response<APIWalletResponse>

    @WithAuthentication("")
    @POST("/api/public/potential-deposit")
    suspend fun postPotentialDeposit() : Response<Void>


    @WithAuthentication("")
    @GET("/api/public/entries/{id}/goals-status")
    suspend fun getGoalsStatus(@Path("id") id: String

    ) : Response<APIGoalsStatusResponse>


    @WithAuthentication("")
    @GET
    suspend fun getMaintenanceModeStatus(@Url url: String

    ) : Response<APIMaintenanceModeStatus>

    @WithAuthentication("")
    @GET("/api/public/users/{id}")
    suspend fun getTournamentPlayerProfile(@Path("id") id: String, @Query("game_mode_id", encoded = true) game_mode_id: String?

    ): Response<APITournamentPlayerProfile>

    /////////////////GUEST//////////////////////////

    @WithAuthentication("")
    @GET("/api/public/guest/me")
    suspend fun getGuestMe(

    ) : Response<APIGuestMe>


    @WithAuthentication("")
    @POST("/api/public/guest/tournaments/join")
    suspend fun postGuestJoinTournament(@Body request: APITournamentsJoinRequestGuest

    ) : Response<APITournamentsJoinResponseGuest>


    @WithAuthentication("")
    @GET("/api/public/guest/games")
    suspend fun getGuestGames(

    ) : Response<APIGamesResponse>

//    @WithAuthentication("")
    @POST("/api/public/guest/signup")
    suspend fun postGuestSignup(

    ) : Response<APIGuestSignupResponse>


    @WithAuthentication("")
    @GET("/api/public/guest/tournaments")
    suspend fun getGuestTournamentsByGameID(@Query("game_id", encoded = true) game_id: String

    ) : Response<APITournamentsResponse>

    @WithAuthentication("")
    @GET("/api/public/guest/tournaments")
    suspend fun getGuestTournamentsByTournamentIDs(@Query("tournament_ids", encoded = true) tournament_ids: String

    ) : Response<APITournamentsResponse>


    @WithAuthentication("")
    @POST("/api/public/guest/entry/start")
    suspend fun postGuestTournamentEntryStart(@Body request: APITournamentEntryStartRequestGuest

    ) : Response<APITournamentEntryStartResponse>

    @WithAuthentication("")
    @POST("/api/public/guest/entry/score")
    suspend fun postGuestTournamentEntryScore(@Body request: APIPostScoreRequestGuest

    ) : Response<APIPostScoreResponseGuest>


    @WithAuthentication("")
    @GET("/api/public/entry/replay-upload-url")
    suspend fun getReplayUploadURL(
        @Query("tournament_instance_entry_id", encoded = true) tournament_instance_entry_id: String,
        @Query("is_user_viewable") is_user_viewable: Boolean

    ) : Response<APITournamentReplayURLResponse>


    @WithAuthentication("")
    @POST("/api/public/entry/replay-upload-url")
    suspend fun postRegisterReplayUploadURL(@Body request: APITournamentRegisterReplayURLRequest

    ) : Response<Void>


}




