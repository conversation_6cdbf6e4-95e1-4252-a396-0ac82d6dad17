package com.gametaco.app_android_fd.data

import com.gametaco.app_android_fd.data.AppConstants.API_BASE_URL_QA
import com.gametaco.app_android_fd.data.AppConstants.API_MAINTENANCE_URL_QA
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json

//@OptIn(markerClass = kotlinx.serialization.InternalSerializationApi.class)
@Serializable
data class EnvironmentSpec(
    val envHeader: String,
    val apiEndpointUrl: String = API_BASE_URL_QA,
    val maintenanceUrl: String = API_MAINTENANCE_URL_QA,
) {
    val environmentName: String
        get() = "${envHeader}staging"

    val jsonString: String
        get() = Json.encodeToString(this)

    companion object {
        fun fromJsonString(jsonString: String): EnvironmentSpec {
            return Json.decodeFromString(jsonString)
        }

        val WW4 = EnvironmentSpec(
            envHeader = "ww4",
        )
        val Default = WW4
    }
}
