package com.gametaco.app_android_fd.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.gametaco.app_android_fd.data.entity.TournamentStartInformation
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.viewmodel.LeaderboardViewModel
import com.gametaco.app_android_fd.viewmodel.ScoresViewModel
import resources.R


@Composable
fun ScoresListElement(
    tournamentInstanceEntryId:String,
    tournament_start_information: TournamentStartInformation? = null,
    gameIcon: String,
    gameIconDescription: String? = null,
    title: String,
    subtitle: String,
    gameMode: String,
    entryWaived:Boolean = false,
    isResult: Boolean = false,
    isError: Boolean = false,
    hasBadge: Boolean = false,
    value: String? = null,
    discountedValue: String? = null,
    statusMessage: String? = null,
    statusSubMessage: String? = null,
    statusIcon: Int? = null,
    statusIconDescription: String? = null,
    gameId: String? = null,
    isLegacy:Boolean = false,
    hasReplay:Boolean = false,
) {
    Box (
        modifier = Modifier
            .clickable {
                if(tournament_start_information == null){
                    LeaderboardViewModel.instance.showLeaderboard(tournamentInstanceEntryId,false,isLegacy)
                }else{
                    ScoresViewModel.instance.joinTournament(tournamentInstanceEntryId, tournament_start_information, gameId)
                }
            }
//            .wrapContentSize()
    ) {
        if (hasBadge){
            Box(
                modifier = Modifier
                    .size(15.dp)
                    .align(Alignment.TopEnd)
                    .offset(-6.dp, -4.dp)
                    .background(
                        color = Color(0xFF005FC8),
                        CircleShape
                    )
            )
        }
        Column {
            Row (
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp, 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .weight(1f, fill = false)
                ){
                    AsyncImage(
                        model = gameIcon,
                        contentDescription = gameIconDescription,
                        modifier = Modifier
                            .size(40.dp)
                            .clip(RoundedCornerShape(4.dp)),
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Column (
//                    modifier = Modifier
//                        .fillMaxHeight()
                    )
                    {
                        Text(
                            text = title,
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color(0xFF005FC8),
                        )
                        Spacer(Modifier.height(4.dp))
                        if(entryWaived == false){
                            Text(
                                text = "${subtitle} Entry | ${gameMode}",
                                style = MaterialTheme.typography.headlineMedium,
                                softWrap = false,
                                overflow = TextOverflow.Ellipsis,
                            )
                        }else{
                            Row(horizontalArrangement = Arrangement.Start, verticalAlignment = Alignment.CenterVertically){
                                val newValue = discountedValue ?: "FREE"

                                Text(
                                    text = newValue,
                                    textAlign = TextAlign.Center,
                                    style = MaterialTheme.typography.headlineMedium,
                                    overflow = TextOverflow.Ellipsis,
                                    softWrap = false,
                                    color = Color(0xff128000)
                                )
                                Spacer(modifier = Modifier.width(2.dp))
                                Box(contentAlignment = Alignment.Center){
                                    Text(
                                        text = subtitle,
                                        textAlign = TextAlign.Center,
                                        style = MaterialTheme.typography.headlineMedium,
                                        overflow = TextOverflow.Ellipsis,
                                        softWrap = false,
                                        color = Color(0x33131433)
                                    )
                                    val textMeasurer = rememberTextMeasurer()
                                    val textLayoutResult = textMeasurer.measure(
                                        text = AnnotatedString(subtitle),
                                        style = MaterialTheme.typography.headlineMedium
                                    )

                                    val textWidth = with(LocalDensity.current) { textLayoutResult.size.width.toDp() }
                                    val textHeight = with(LocalDensity.current) { textLayoutResult.size.height.toDp() }

                                    Canvas(modifier = Modifier
                                        .width(textWidth)
                                        .height(textHeight)
                                    ) {
                                        val y = size.height / 2  // middle of the text
                                        val lineHeight = size.height * 0.04f
                                        drawRect(
                                            color = Color.Black,
                                            topLeft = Offset(0f, y - lineHeight / 2),
                                            size = Size(textLayoutResult.size.width.toFloat(), lineHeight)
                                        )
                                    }
                                }
                                Text(
                                    text = " Entry | ${gameMode}",
                                    textAlign = TextAlign.Center,
                                    style = MaterialTheme.typography.headlineMedium,
                                    softWrap = false,
                                    overflow = TextOverflow.Ellipsis,
                                )
                            }

                        }
                    }
                }
                Spacer(modifier = Modifier.width(4.dp))
                if (statusMessage != null){
                    Row (
                        modifier = Modifier
                            .wrapContentWidth(align = Alignment.End)
                            .align(Alignment.CenterVertically),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.End
                    ) {
                        Column (
                            horizontalAlignment = Alignment.End,
                            verticalArrangement = Arrangement.Center,
                            //modifier = Modifier.width(if (isResult) 80.dp else 96.dp)
                        ) {
                            if (isResult && value != null)
                            {
                                Text(
                                    text = value,
                                    style = MaterialTheme.typography.titleSmall,
                                    color = MaterialTheme.colorScheme.tertiary,
                                )
                            }

                            val (statusTextStyle, color) = if (isResult) {
                                MaterialTheme.typography.labelSmall to Color(0xFF6A6F73)
                            } else {
                                MaterialTheme.typography.labelSmall
                                    .copy(fontSize = 12.sp) to Color.Unspecified
                            }

                            Text(
                                text = statusMessage,
                                style = statusTextStyle,
                                color = color,
                            )
                            if(statusSubMessage != null){
                                Spacer(Modifier.height(2.dp))
                                Text(
                                    text = statusSubMessage,
                                    style = MaterialTheme.typography.labelSmall,
                                    color = Color(0xFF6A6F73),
                                )
                            }
                        }
                        Spacer(modifier = Modifier.width(2.dp))
                        val statusIconSize = if(isResult) 22.dp else 18.dp
                        if (statusIcon != null){
                            Box(
                                modifier = Modifier
                                    .width(statusIconSize)
                                    .wrapContentHeight(),
                                contentAlignment = Alignment.Center
                            )
                            {
                                val statusImageSize = statusIconSize
                                Image(
                                    modifier = Modifier.size(statusImageSize),
                                    painter = painterResource(statusIcon),
                                    contentDescription = statusIconDescription,
                                    colorFilter = if (isError) {
                                        ColorFilter.tint(MaterialTheme.colorScheme.onSurface)
                                    } else if (isResult) {
                                        null
                                    } else {
                                        ColorFilter.tint(MaterialTheme.colorScheme.onSurface)
                                    }
                                )
                            }
                        }
                    }
                }
            }
            if(hasReplay && ExperimentManager.instance.videoReplaysUI?.isAvailable() == true && ExperimentManager.instance.videoReplaysUI?.isActive() == true){
                Row(modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp, 0.dp, 12.dp, 4.dp)
                    .offset(x = 0.dp,y = -2.dp),
                    horizontalArrangement = Arrangement.Start,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(painter = painterResource(R.drawable.ic_replays), contentDescription = null)
                    Spacer(modifier = Modifier.width(2.dp))
                    Text(text = "VIDEO REPLAY AVAILABLE", color = Color(0xFF6A6F73), style = MaterialTheme.typography.labelSmall)
                }
            }
        }

    }
}
