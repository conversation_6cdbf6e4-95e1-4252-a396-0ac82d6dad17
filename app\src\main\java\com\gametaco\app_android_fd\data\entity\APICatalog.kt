package com.gametaco.app_android_fd.data.entity

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

data class APICatalog(
    val data:APICatalogData,
    var sections:List<APICatalogSection>,
){
    fun getBannerData(banner:String?):APICatalogBannerData?{
        if(banner == null){
            return null
        }
        return data.banners.get(banner)
    }
}

data class APICatalogData(
    val games:Map<String, GCGameData>,
    val banners: Map<String, APICatalogBannerData>,
)

data class APICatalogSection(
    val title:String,
    val section_type:APICatalogSectionTypeEnum,
    val is_collapsible:Boolean,
    val display_order:Int,
    val swimlanes:List<APICatalogSwimlaneData>,
    val protip_items:List<APICatalogProtipItem>
)

data class APICatalogGameData(
    val id : String,
    val game_id : String,
    val loading_background_image_url : String,
    val tournament_background_image_url : String,
    val hero_image_url : String,
    val tile_image_url : String,
    val gradient_top_color : String,
    val gradient_bottom_color : String,
    val panel_color : String?
)

data class APICatalogBannerData(
    val text:String,
    val color:String,
    val border_color:String
)

enum class APICatalogSectionTypeEnum(value:String){
    NORMAL("NORMAL"),
    RECENTLY_PLAYED("RECENTLY_PLAYED"),
    PRO_TIPS("PRO_TIPS")
}
data class APICatalogSwimlaneData(
    val title:String,
    val show_title:Boolean,
    val display_order: Int,
    val filter_pill_title:String?,
    val games_layout:APICatalogSwimlaneGamesLayoutEnum,
    val is_new:Boolean,
    val game_items:List<APICatalogGameItem>,
    val tournament_items:List<APICatalogTournamentItem>,
    val spotlight_item:APICatalogSpotlightItem?,
)

enum class APICatalogSwimlaneGamesLayoutEnum(value: String){
    HORIZONTAL("HORIZONTAL"),
    VERTICAL("VERTICAL")
}

data class APICatalogGameItem(
    val game_meta_datum_id:String,
    val display_order: Int,
    val banner:String?
)

data class APICatalogTournamentItem(
    val tournament_id:String,
    val game_id: String,
    val display_order: Int,
    val banner: String?
)

data class APICatalogSpotlightItem(
    val banner: String?,
    val title: String,
    val description:String,
    val image:String,
    val cta_label:String?,
    val cta_link:APICatalogCtaLinkEnum?,
    val game_meta_datum_id: String?,
)
enum class APICatalogCtaLinkEnum(value: String){
    GAME_LOBBY("GAME_LOBBY"),
    ADD_FUNDS("ADD_FUNDS")
}

data class APICatalogProtipItem(
    val title:String,
    val tutorial_title:String,
    val description: String,
    val image:String?,
    val video:String?,
    val game_id:String?,
    val display_order: Int
)

@Serializable
data class APICatalogVersion (
    val id: String,
    val version_number: Int,
    val is_eligible_for_card_games:Boolean
) {
    val jsonString: String
        get() = Json.encodeToString(kotlinx.serialization.serializer(), this)

    companion object {
        fun fromJsonString(jsonString: String): APICatalogVersion {
            return Json.decodeFromString(kotlinx.serialization.serializer(), jsonString)
        }
    }
}