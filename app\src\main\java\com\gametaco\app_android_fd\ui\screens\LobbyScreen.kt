package com.gametaco.app_android_fd.ui.screens

import PullRefreshIndicator
import TurboPopup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.LocalNavManager
import com.gametaco.app_android_fd.LocalUIManager
import com.gametaco.app_android_fd.data.AppConstants
import com.gametaco.app_android_fd.data.entity.APIWalletResponse
import com.gametaco.app_android_fd.data.entity.GCGameData
import com.gametaco.app_android_fd.data.entity.GCRow
import com.gametaco.app_android_fd.data.navigation.NavigationData
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.BrazeEventName
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.SystemBarState
import com.gametaco.app_android_fd.manager.WalletManager
import com.gametaco.app_android_fd.models.LobbyUiState
import com.gametaco.app_android_fd.ui.components.ContestDetail
import com.gametaco.app_android_fd.ui.components.ErrorElement
import com.gametaco.app_android_fd.ui.components.GamePreview
import com.gametaco.app_android_fd.ui.components.LobbyGameContent
import com.gametaco.app_android_fd.ui.components.LobbyGuestSignup
import com.gametaco.app_android_fd.ui.components.LobbyTopHighlight
import com.gametaco.app_android_fd.ui.components.NavBar
import com.gametaco.app_android_fd.ui.components.TopBarLogo
import com.gametaco.app_android_fd.ui.theme.theme_light_background
import com.gametaco.app_android_fd.viewmodel.GamesViewModel
import com.gametaco.app_android_fd.viewmodel.PlayViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import pullRefresh
import rememberPullRefreshState

const val TAG_LOBBY_SCREEN = "LobbyScreen"

@Composable
fun LobbyScreen(
    gamesViewModel: GamesViewModel,
    playViewModel: PlayViewModel
) {
    val TAG = TAG_LOBBY_SCREEN
    val uiManager = LocalUIManager.current
    val coroutineScope = rememberCoroutineScope()
    val listState = rememberLazyListState()

    LaunchedEffect(Unit) {
        uiManager.setSystemBarState(SystemBarState.TransparentLightStatusBarIcons)
        gamesViewModel.resetLobbyUiState()
        gamesViewModel.loadData()
        BrazeManager.instance.logEvent(BrazeEventName.Returned_To_Lobby.value)

        gamesViewModel.uiEvent.collect { event ->
            when (event) {
                is GamesViewModel.UiEvent.ScrollToTop -> {
                    coroutineScope.launch {
                        listState.scrollToItem(0)
                    }
                }
            }
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            //set loading on ResourceStates so that the previous state of the lobby isn't seen for a frame when navigating to it
//            gamesViewModel.setLoading()
        }
    }

    val navManager = LocalNavManager.current

    val lobbyUiState by gamesViewModel.lobbyUiState.collectAsState()

    val successState = lobbyUiState as? LobbyUiState.Success

    val defaultFlowFalse = remember { MutableStateFlow(false) }
    val infoModalExpanded = successState?.infoModalExpanded ?: defaultFlowFalse
    val gameModalExpanded = successState?.gameModalExpanded ?: defaultFlowFalse
    val currentTournamentData = successState?.currentTournamentData?.collectAsState()?.value
    val selectedGameData = successState?.selectedGameData?.collectAsState()?.value

//    val gameModalExpanded = remember { mutableStateOf(false) }
    val turboViewed = remember { mutableStateOf(PreferencesManager.instance.getIsTurboViewed()) }
    val experimentsReady by ExperimentManager.instance.experimentsReady.collectAsState()

    //off by default for everyone
    var isTurboPopupActive = false

//    var currentTournamentData : APITournament? by remember { mutableStateOf(null) }
    val wallet: APIWalletResponse by WalletManager.instance.wallet.collectAsState()
    val isGuest = AuthenticationManager.instance.isGuest
    val isFtue = AuthenticationManager.instance.showLobbyFtue

    val refreshScope = rememberCoroutineScope()
    var refreshing by remember { mutableStateOf(false) }

    fun refresh() = refreshScope.launch {
        refreshing = true
        gamesViewModel.loadData()
        refreshing = false
    }
    val state = rememberPullRefreshState(refreshing, ::refresh)

    Scaffold (
        topBar = { TopBarLogo() },
        bottomBar = {
            if (!isFtue || lobbyUiState is LobbyUiState.Error) {
                NavBar(NavigationData.instance, navManager)
            }
        },
    ){ paddingValues ->
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .pullRefresh(state = state)
                .background(color = theme_light_background)
                .padding(paddingValues)
        ){
            Box(
                modifier = Modifier
                    .pullRefresh(state = state)
                    .fillMaxSize()
                    .background(color = theme_light_background),
            ) {
                when (val uiState = lobbyUiState) {
                    is LobbyUiState.Loading -> {
                        LoadingScreen()
                    }

                    is LobbyUiState.Error -> {
                        ErrorElement(uiState.errorMessage) {
                            gamesViewModel.loadData()
                        }
                    }

                    is LobbyUiState.Success -> {
                        val catalogResponse = uiState.catalog

                        val gamesDict: Map<String, GCGameData> = catalogResponse.data.games
                        val rows: List<GCRow> = catalogResponse.rows.sortedBy { row -> row.display_order }

                        BoxWithConstraints {
                            var tabletPadding = PaddingValues()
                            val boxWithConstraintsScope = this
                            val maxWidth = boxWithConstraintsScope.maxWidth
                            if (maxWidth > AppConstants.VIEW_MAX_WIDTH.dp) {
                                tabletPadding =
                                    PaddingValues(
                                        horizontal = (maxWidth - AppConstants.VIEW_MAX_WIDTH.dp) / 2f
                                    )
                            }

                            LazyColumn(
                                state = listState,
                                modifier = Modifier
                                    .padding(tabletPadding)
                                    .background(color = theme_light_background),
                                userScrollEnabled = true,
                                verticalArrangement = Arrangement.spacedBy(
                                    10.dp,
                                    Alignment.Top
                                ),
                            ) {
                                item {
                                    LobbyTopHighlight(
                                        isNewCarousel = false,
                                        isFtue = isFtue,
                                        isGuest = isGuest,
                                    )
                                }

                                if (!isFtue && isGuest) {
                                    item {
                                        LobbyGuestSignup()
                                    }
                                }

                                item {
                                    LobbyGameContent(
                                        gamesViewModel = gamesViewModel,
                                        rows = rows,
                                        gamesDict = gamesDict,
                                        wallet = wallet,
                                        catalogResponse = catalogResponse,
                                        isFtue = isFtue,
                                        isGuest = isGuest,
                                        onGameTileClicked = uiState.onGameTileClicked,
                                        onInfoClick = uiState.onInfoClick,
                                    )
                                }

                                item {
                                    Spacer(modifier = Modifier)
                                }
                            }
                        }
                    }
                }
                PullRefreshIndicator(
                    modifier = Modifier.align(alignment = Alignment.TopCenter),
                    refreshing = refreshing,
                    state = state,
                )
            }
        }
    }

    ContestDetail(infoModalExpanded, currentTournamentData, wallet, isGuest, gameId = selectedGameData?.id)

    GamePreview(gameModalExpanded, selectedGameData, isFtue, isGuest)

    if(isTurboPopupActive && !turboViewed.value){
        TurboPopup(onClose = {
            PreferencesManager.instance.setIsTurboViewed()
            ExperimentManager.instance.turbosMode?.trackExposure()
            turboViewed.value = true
        }) {
            navManager.navigate(Routes.GAMES_SCREEN)
        }
    }
}

@Preview
@Composable
fun LobbyScreenPreview(){
    PreviewRoot {
        LobbyScreen(gamesViewModel = GamesViewModel.instance, playViewModel = PlayViewModel.instance)
    }
}
