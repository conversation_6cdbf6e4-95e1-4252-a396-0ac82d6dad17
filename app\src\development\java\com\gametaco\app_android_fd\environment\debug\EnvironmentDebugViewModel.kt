package com.gametaco.app_android_fd.environment.debug

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gametaco.app_android_fd.data.AppConstants.API_BASE_URL_QA
import com.gametaco.app_android_fd.data.AppConstants.API_MAINTENANCE_URL_QA
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.data.EnvConfigProviderImpl
import com.gametaco.app_android_fd.data.EnvironmentSpec
import com.gametaco.app_android_fd.manager.ActivityManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.ToastManager
import com.gametaco.app_android_fd.utils.log.Logger
import com.jakewharton.processphoenix.ProcessPhoenix
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

class EnvironmentDebugViewModel : ViewModel(), KoinComponent {
    companion object {
        const val TAG = "EnvironmentDebugViewModel"
    }

    private val currentEnvHostName
        get() = AppEnv.Companion.current.envHost?.envHeader ?: "<unknown>"
    private val currentEnvHost = MutableStateFlow<String>(currentEnvHostName)
    
    val endpointUrl = MutableStateFlow<String>(
        AppEnv.current.api_endpoint
    )
    
    val maintenanceUrl = MutableStateFlow<String>(
        AppEnv.current.maintenanceURL
    )

    fun onApplyHostName(newEnvName: String) {
        Logger.d(TAG, "onApplyHostName: $newEnvName")
        currentEnvHost.value = newEnvName
        applyAndRestart()
    }
    
    fun onApplyEndpointUrl(newEndpointUrl: String) {
        Logger.d(TAG, "onApplyEndpointUrl: $newEndpointUrl")
        endpointUrl.value = newEndpointUrl
        applyAndRestart()
    }
    
    fun onApplyMaintenanceUrl(newMaintenanceUrl: String) {
        Logger.d(TAG, "onApplyMaintenanceUrl: $newMaintenanceUrl")
        maintenanceUrl.value = newMaintenanceUrl
        applyAndRestart()
    }
    
    fun onResetToDefault() {
        Logger.d(TAG, "onResetHostnames: resetting to defaults")
        currentEnvHost.value = EnvironmentSpec.Default.envHeader
        endpointUrl.value = API_BASE_URL_QA
        maintenanceUrl.value = API_MAINTENANCE_URL_QA
        applyAndRestart()
    }

    private val toastManager: ToastManager by inject()
    private val activityManager: ActivityManager by inject()

    private fun buildEnvironmentSpec(): EnvironmentSpec {
        return EnvironmentSpec(
            envHeader = currentEnvHost.value,
            apiEndpointUrl = endpointUrl.value,
            maintenanceUrl = maintenanceUrl.value,
        )
    }

    private fun applyAndRestart() {
        val activity = activityManager.currentActivity.value ?: return
        toastManager.showToast("Applying change - resetting login & restarting app…")

        val newEnvSpec = buildEnvironmentSpec()
        EnvConfigProviderImpl.updateEnvironmentSpec(newEnvSpec)

        viewModelScope.launch {
            delay(10)
            FDManager.instance.resetLogin()
            delay(200)
            ProcessPhoenix.triggerRebirth(activity)
        }
    }

    private fun mapUiState(
        envName: String,
        endpointUrl: String,
        maintenanceUrl: String,
    ): EnvironmentDebugUiState {
        return EnvironmentDebugUiState(
            envName = envName,
            title = "Debug Menu",
            environmentTitle = "ENVIRONMENT",
            textFieldLabel = "Fanduel QA Environment",
            textFieldPlaceholder = "Environment host name",
            currentLabel = "Current: $envName",
            applyLabel = "Apply",
            onApplyHostName = ::onApplyHostName,
            hostnamesTitle = "HOSTNAMES",
            endpointUrl = endpointUrl,
            endpointUrlLabel = "Endpoint URL",
            endpointUrlPlaceholder = "API endpoint URL",
            maintenanceUrl = maintenanceUrl,
            maintenanceUrlLabel = "Maintenance URL",
            maintenanceUrlPlaceholder = "Maintenance page URL",
            resetButtonLabel = "Reset all to Default",
            onApplyEndpointUrl = ::onApplyEndpointUrl,
            onApplyMaintenanceUrl = ::onApplyMaintenanceUrl,
            onResetHostnames = ::onResetToDefault,
        )
    }

    val uiState = combine(
        currentEnvHost,
        endpointUrl,
        maintenanceUrl
    ) { envName, endpoint, maintenance ->
        mapUiState(envName, endpoint, maintenance)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000L),
        initialValue = mapUiState(
            envName = currentEnvHostName,
            endpointUrl = AppEnv.current.api_endpoint,
            maintenanceUrl = AppEnv.current.maintenanceURL,
        ),
    )

    init {
        Logger.d(TAG, "Init EnvironmentDebugViewModel")
    }
}