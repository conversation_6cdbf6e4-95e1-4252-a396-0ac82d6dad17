package com.gametaco.app_android_fd.manager

import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APIPostScoreRequest
import com.gametaco.app_android_fd.data.entity.APIPostScoreRequestGuest
import com.gametaco.app_android_fd.data.entity.APITournamentEntryStartRequest
import com.gametaco.app_android_fd.data.entity.APITournamentEntryStartRequestGuest
import com.gametaco.app_android_fd.data.entity.APITournamentEntryStartResponse
import com.gametaco.app_android_fd.data.entity.APITournamentReEntry
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinRequest
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinRequestGuest
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinResponse
import com.gametaco.app_android_fd.data.entity.APITournamentsJoinResponseGuest
import com.gametaco.app_android_fd.data.entity.APITournamentsReEntryRequest
import com.gametaco.app_android_fd.data.entity.APIUserGameStats
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.utilities.ResourceState
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

// TournamentLifeCycleProviding.kt

interface TournamentLifeCycleProviding {

    suspend fun reEnterTournament(tournamentInstanceEntryID: String): ResourceState<APITournamentReEntry>
    suspend fun joinTournament(tournamentId: String): ResourceState<APITournamentsJoinResponse>
    suspend fun joinTournamentGuest(tournamentId: String): ResourceState<APITournamentsJoinResponseGuest>
    suspend fun startTournament(tournamentInstanceEntryID: String, o: String): ResourceState<APITournamentEntryStartResponse>
    suspend fun submitScore(
        tournamentInstanceEntryID: String,
        o: String,
        maxAttempts: Int,
        retryDelay: Double
    ): ResourceState<APIUserGameStats>

    fun setTournamentId(id: String?)
    fun setGameId(id: String?)
    suspend fun getGeoComplyToken() : String?
}

// TournamentLifeCycleProvider.kt

class TournamentLifeCycleProvider(
    private val worldWinnerAPI: WorldWinnerAPI,
    private val authenticationManager: AuthenticationManager,
    private val geoComplyManager: GeoComplyManager,
    private val tournamentManager: TournamentManager,
) : TournamentLifeCycleProviding {

    companion object {
        val TAG = "TournamentLifeCycleProviding"
    }
    private val _providerTournamentIdFlow = MutableStateFlow<String?>(null)
    val providerTournamentIdFlow: StateFlow<String?> = _providerTournamentIdFlow.asStateFlow()
    val providerTournamentId: String?
        get() = providerTournamentIdFlow.value



    private val _providerGameIdFlow = MutableStateFlow<String?>(null)
    val providerGameIdFlow: StateFlow<String?> = _providerGameIdFlow.asStateFlow()
    val providerGameId: String?
        get() = providerGameIdFlow.value


    override suspend fun reEnterTournament(tournamentInstanceEntryID: String): ResourceState<APITournamentReEntry> {
        val jwt = getGeoComplyJwt()
        return worldWinnerAPI.postTournamentReEntries(
            tournamentInstanceEntryID,
            APITournamentsReEntryRequest(jwt),
        )
    }

    override suspend fun joinTournament(tournamentId: String): ResourceState<APITournamentsJoinResponse> {
        val jwt = getGeoComplyJwt()
        return worldWinnerAPI.postJoinTournament(APITournamentsJoinRequest(tournamentId, jwt))
    }

    private fun getGeoComplyJwt() = if (MockModeManager.instance.isMockModeEnabled) {
        "FAKE_TOKEN"
    } else {
        geoComplyManager.decodedJWT ?: "no_token_available"
    }

    override suspend fun joinTournamentGuest(tournamentId: String): ResourceState<APITournamentsJoinResponseGuest> {
        return worldWinnerAPI.postGuestJoinTournament(APITournamentsJoinRequestGuest(tournamentId))
    }

    override suspend fun startTournament(tournamentInstanceEntryID: String, o: String): ResourceState<APITournamentEntryStartResponse> {
        return if (authenticationManager.isGuest) {
            worldWinnerAPI.postGuestTournamentEntryStart(APITournamentEntryStartRequestGuest(tournamentInstanceEntryID, o)
            )
        } else {
            worldWinnerAPI.postTournamentEntryStart(APITournamentEntryStartRequest(tournamentInstanceEntryID, o))
        }
    }

    override suspend fun submitScore(
        tournamentInstanceEntryID: String,
        o: String,
        maxAttempts: Int,
        retryDelay: Double
    ): ResourceState<APIUserGameStats> {

        var attempts = 0
        while (attempts < maxAttempts) {

            val result = if (authenticationManager.isGuest) {
                when (val apiResult = worldWinnerAPI.postGuestTournamentEntryScore(
                    APIPostScoreRequestGuest(
                        tournamentInstanceEntryID,
                        o
                    )
                )) {
                    is ResourceState.Success -> {
                        tournamentManager.guestScore = apiResult.data.score
                        ResourceState.Success(APIUserGameStats(
                            total_games_played = 0,
                            total_cash_games_played = 0
                        ))
                    }

                    is ResourceState.Error -> ResourceState.Error(
                        apiResult.error,
                        apiResult.code
                    )

                    is ResourceState.Loading -> ResourceState.Loading()
                }
            } else {
                when (val apiResult = worldWinnerAPI.postTournamentEntryScore(
                    APIPostScoreRequest(
                        tournamentInstanceEntryID,
                        o
                    )
                )) {
                    is ResourceState.Success -> { apiResult } // Returning Unit here
                    is ResourceState.Error -> { ResourceState.Error(
                        apiResult.error,
                        apiResult.code
                    )}

                    is ResourceState.Loading -> { ResourceState.Loading()}
                }
            }

            if(result.isSuccess()) {
                return result
            } else {
                if(++attempts >= maxAttempts) {
                    return result
                }
                delay((retryDelay * 1000.0).toLong())
            }
        }
        return ResourceState.Error("Failed to submit score", 0)
    }

    override fun setTournamentId(id: String?) {
        _providerTournamentIdFlow.value = id
        Logger.i("GameData", "*** setTournamentId: $id")
    }

    override fun setGameId(id: String?) {
        _providerGameIdFlow.value = id
        Logger.i("GameData", "*** setGameId: $id")
    }

    override suspend fun getGeoComplyToken() : String? {
        GeoComplyManager.instance.refreshGeoComplyToken()
        val maxAttempts = 15 // max of 7.5 seconds
        var attempts = 0

        // Poll until the geocomply manager is done refreshing the geocomply token
        while(GeoComplyManager.instance.isRefreshingToken || attempts < maxAttempts) {
            delay(500L)
            attempts += 1
        }
        Logger.d(TAG, "Polling for refreshed Geocomply token: FINISHED")

        return GeoComplyManager.instance.decodedJWT
    }
}
