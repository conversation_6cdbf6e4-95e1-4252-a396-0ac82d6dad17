package com.gametaco.app_android_fd.manager

interface UnityProviding {
    val containerInfo: String?


    /**
     * Load a game within the Unity engine using an encoded string.
     * The delegate is used for callback purposes to handle game-related events.
     *
     * @param encodedString Encoded string containing game data.
     * @param delegate Tournament manager delegate to handle game events.
     */
    fun loadGame(encodedString: String)

    /**
     * Start a game with a specified option.
     *
     * @param o Option string to configure or start the game.
     */
    fun startGame(o: String)

    /**
     * Pause or resume the game based on the isPaused parameter.
     *
     * @param isPaused Boolean value indicating whether the game should be paused.
     */
    fun pauseGame(isPaused: Boolean)

    /**
     * Notify Unity to return control back to the main application.
     */
    fun returnToApp()

    /**
     * Reset the Unity environment so that another game can be loaded.
     * This method should be used when exiting Unity before a game finishes.
     * If not used, Unity will continue to run, which might not be desired.
     * Using UnityFramework's quit or unload methods incorrectly might crash the app.
     */
    fun resetUnity()

    fun getReplayFilePath(tournamentInstanceEntryId : String) : String?

    fun prepareForReplay(tournamentInstanceEntryID: String?)

    fun uploadReplay(tournamentInstanceEntryID: String?, replayFilePath: String?, isUserViewable: Boolean)
    fun showUnityFromAction(actionType:String)
    fun getDailyRewardFutureTime() :String?
    fun setTournamentManagerDelegate(delegate:TournamentManagerDelegate)
    fun hideUnity(appNavigationInfo: String?, error: String?)
    fun isShowingUnity():Boolean
}
