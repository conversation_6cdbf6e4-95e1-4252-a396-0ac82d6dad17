package com.gametaco.app_android_fd.data.entity

import com.gametaco.app_android_fd.utils.toNumberWithCommaString


data class APITournamentInstanceEntry(
    val id: String,
    val tournament_instance_id: String,
    val started_at: String?,
    val ended_at: String?,
    val entry_fee: String,
    val maximum_slots: Int?,
    val is_winner: <PERSON><PERSON>an,
    val is_tie: Boolean,
    val brand:APITournamentBrand?,
    val tournament_id: String?,
    val tournament_instance_status: String,
    val tournament_instance_closed_at: String?,
    val score: Int?,
    val current_slots_count: Int,
    val game_icon: String?,
    val game_id: String,
    val results: List<APITournamentInstanceEntryResult>,
    val is_joinable: Boolean,
    val can_reenter: Boolean,
    val prize_amount: String?,
    val game_display_name: String,
    val game_mode_id:String?,
    val game_mode_name: String?,
    val game_mode_description: String?,
    val game_mode_icon: String?,
    val refund_reason: String?,
    val reference_id:String?,
    val expanded_results:Map<String,APITournamentInstanceEntryExpandedResult>?,
    val gradient_top_color:String?,
    val gradient_bottom_color:String?,
    val maximum_entries_per_player:Int?,
    val has_additional_ticket: Boolean = false,
    val is_for_ticket_holders_only: Boolean = false,
    val ticket_info:APITournamentEntryResultTicketInfo? = null,
    val fifty_fifty_cutoff_percent:Int?
){
    val isSurvivor:Boolean
        get() {
            return brand == APITournamentBrand.FIFTY_FIFTY
        }
    val survivorRounds:Int?
        get() {
            if(isSurvivor && fifty_fifty_cutoff_percent != null && maximum_slots!= null){
                val percent = fifty_fifty_cutoff_percent / 100f
                var total = maximum_slots.toFloat()
                var rounds = 0
                while(total > 1){
                    rounds++
                    total = total * percent
                }
                return rounds
            }
            return null
        }
    val entryFee: EntryFee by lazy { EntryFee(entry_fee) }
    val isEntryFeeFree:Boolean
        get() = entryFee.isFree
    val isFreeEntry: Boolean
        get() = isEntryFeeFree || has_additional_ticket

    val discountedEntryFee: String?
        get() {
            //Only proceed if there's a ticket and it's a discount ticket
            if(ticket_info == null || !ticket_info.isDiscountedTicket) return null

            val originalFee = entry_fee.toDoubleOrNull() ?: return null
            val discountPercent = ticket_info.discount_percent ?: return null
            val discountAmount = originalFee * (discountPercent / 100.0)
            val discountedFee = originalFee - discountAmount
            return discountedFee.toNumberWithCommaString()
        }


    fun hasBeenRefunded():Boolean{
        return tournament_instance_status !=  APITournamentInstanceStatus.Open.rawValue
                && (tournament_instance_status == APITournamentInstanceStatus.Refunded.rawValue
                || refund_reason != null)
    }
}

val APITournamentInstanceEntry?.entryFeeLabel: String
    get() {
        if (this == null) return entryFeeLabel(isFree = true, value = "0.00")

        return when {
            ticket_info != null && ticket_info.isFreeTicket -> entryFeeLabel(isFree = true, value = "0.00")
            ticket_info != null && ticket_info.isDiscountedTicket -> {
                val discountedFee = discountedEntryFee ?: entryFee.string
                entryFeeLabel(isFree = false, value = discountedFee)
            }
            isFreeEntry -> entryFeeLabel(isFree = true, value = "0.00")
            else -> entryFeeLabel(isFree = false, value = entryFee.string)
        }
    }

data class APITournamentInstanceEntryExpandedResult(
    val keyStats:List<APITournamentInstanceEntryExpandedResultStatItem>,
    val subScores:List<APITournamentInstanceEntryExpandedResultScoreItem>,
)

data class APITournamentInstanceEntryExpandedResultStatItem(
    val name:String,
    val value:String
)
data class APITournamentInstanceEntryExpandedResultScoreItem(
    val name:String,
    val value:Int,
    val description:String?
)

data class APITournamentInstanceEntryResult(
    val user: APITournamentUser?,
    val score: Int?,
    val prize_amount: String?,
    val order:Int?,
    val status:APITournamentUserStatus,
    val replay_url:String?
){
    fun getUserName():String?{
        return user?.username
    }
}

data class APITournamentUser(
    val id:String,
    val username:String,
    val avatar_url:String?
)
enum class APITournamentUserStatus(value:String){
    NOT_STARTED("NOT_STARTED"),
    COMPLETED("COMPLETED"),
    IN_PROGRESS("IN_PROGRESS"),
    OPEN("OPEN")
}

enum class APIRefundReason(val rawValue:String){
    onlyPlayer("ONLY_PLAYER"),
    autoBooted("AUTO_BOOTED"),
    playerServices("PLAYER_SERVICES"),
}

data class APITournamentPlayerProfile(
    val username:String,
    val avatar_url:String,
    val member_since:String,
    val game_mode_stats:APITournamentPlayerProfileStat?
)

data class APITournamentPlayerProfileStat(
    val average_score:Double,
    val total_games_played:Int,
    val highest_score:Int,
    val highest_score_achieved_at:String?
)

enum class APITournamentBrand(val value:String){
    HEAD_TO_HEAD("HEAD_TO_HEAD"),
    MULTIPLAYER("MULTIPLAYER"),
    SINGLE_PLAYER_PRACTICE("SINGLE_PLAYER_PRACTICE"),
    UNLIMITED_PLAYERS("UNLIMITED_PLAYERS"),
    FIFTY_FIFTY("FIFTY_FIFTY"),
}