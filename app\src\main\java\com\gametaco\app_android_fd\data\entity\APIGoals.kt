package com.gametaco.app_android_fd.data.entity

import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.app_android_fd.utils.toDollarString
import kotlinx.coroutines.flow.MutableStateFlow
import java.util.Date

enum class APIActiveGoalClaimState{
    Not_Claim,
    In_Progress,
    Complete,
}

abstract class APIActiveGoal {
    abstract val _id:String
    abstract val goalType:APIGoalType?
    abstract val rewardType:APIGoalRewardType?
    abstract val cashString:String
    abstract val titleString:String
    abstract val subTitleString:String
    abstract val descString:String
    abstract val currentProgress:Int
    abstract val requiredProgress:Int
    abstract val currentStep:Int
    abstract val requiredStep:Int
    abstract val isComplete:Boolean
    abstract val claimState: MutableStateFlow<APIActiveGoalClaimState>
    abstract var eligibleToClaim:Boolean
    abstract val sortedTasks:List<APIGoalsTask>
    abstract val goalUserInstanceTaskId:String?
    abstract val rewardValue:Float
    abstract val displayOrder:Int
    abstract val icon:String?
    abstract val linkString:String
    abstract val ctaString:String
    abstract val gameId:String?
    abstract val is_deposit_goal:Boolean?
    abstract val cluster_title : String?
    abstract val cluster_icon : String?
    abstract val task_title:String?
    abstract val starts_at:String?
    abstract val ends_at:String?
    abstract val has_ticket_reward:Boolean?
    abstract val has_tag_reward:Boolean?
    abstract fun containsTask(id: String):Boolean
    abstract val isDailyReward: Boolean
}

data class APIGoalsCollectableResponse(
    val results: List<APIGoalsCollectableGoal>,
    val count: Int
)

data class APIGoalDetail(
    val title: String,
    val subtitle : String,
    val description : String?,
    val type : String,
    val repeat_limit : Int?,
    val icon : String,
    val reward_type : String,
    val cluster_title : String?,
    val cluster_icon : String?,
) {
    val goalType: APIGoalType
        get() = APIGoalType.fromRawValue(type)!!
    val rewardType: APIGoalRewardType
        get() = APIGoalRewardType.fromRawValue(reward_type)!!
}

data class APIGoalsCollectableGoal(
    val goal_task_id : String,
    val goal_user_instance_task_id : String,
    val goal : APIGoalDetail,
    val reward : String,
    val required_progress : String,
    val completed_at : String,
    val can_be_collected_until : String?,
    val order : Int,
    override val is_deposit_goal: Boolean?, override val task_title: String?,
    override val starts_at: String?,
    override val ends_at: String?,
    override val has_ticket_reward: Boolean?,
    override val has_tag_reward: Boolean?
):APIActiveGoal(){
    override val _id: String
        get() = goal_task_id
    override val goalType: APIGoalType
        get() = goal.goalType
    override val rewardType: APIGoalRewardType
        get() = goal.rewardType
    override val sortedTasks: List<APIGoalsTask>
        get() = listOf()
    override val cashString: String
        get(){
            return reward.toDollarString()
        }
    override val isDailyReward: Boolean
        get() = false

    override val titleString: String
        get(){
            return goal.title
        }

    override val subTitleString: String
        get() = goal.subtitle

    override val descString: String
        get() = goal.description?:""
    override val currentProgress: Int
        get() = required_progress.toDouble().toInt()
    override val requiredProgress: Int
        get() = required_progress.toDouble().toInt()
    override val currentStep: Int
        get() = 1
    override val requiredStep: Int
        get() = 1
    override val isComplete: Boolean
        get() = true
    private val _claimState = MutableStateFlow(APIActiveGoalClaimState.Not_Claim)
    override val claimState: MutableStateFlow<APIActiveGoalClaimState>
        get() = _claimState
    override var eligibleToClaim: Boolean = true
    override fun containsTask(id:String): Boolean {
        return id == this.goal_user_instance_task_id
    }
    override val goalUserInstanceTaskId: String
        get() = this.goal_user_instance_task_id
    override val rewardValue: Float
        get() = reward.toFloat()
    override val displayOrder: Int
        get() = 1
    override val icon: String
        get() = goal.icon
    override val ctaString: String
        get() = ""
    override val linkString: String
        get() = ""
    override val cluster_title: String?
        get() = goal.cluster_title
    override val cluster_icon: String?
        get() = goal.cluster_icon
    override val gameId: String?
        get() = null
}

data class APIGoalsTask(
    val id: String?,
    var goal_user_instance_task_id:String?,
    val order: Int?,
    val reward: String?,
    val current_progress:String?,
    val required_progress: String,
    val completed_count:Int?,
    val title: String?,
    val at_repeat_limit:Boolean?,
    val is_completable:Boolean?,
    val link:String,
    val cta:String,
    val game_id:String?,
    val is_active:Boolean?,
    val starts_at:String?,
    val ends_at:String?,
    val has_ticket_reward: Boolean?,
    val has_tag_reward: Boolean?
){
    val progressRatio:Double
        get() {
            val progress = current_progress?.toDouble() ?: 0.0
            val required = required_progress.toDouble()
            if(required > 0){
                return progress / required
            }
            return 0.0
        }
    val isComplete:Boolean
        get(){
            val progress = current_progress?.toDouble() ?: 0.0
            val required = required_progress.toDouble()
            return progress >= required
        }
    val isCompletable:Boolean
        get() = is_completable == true
    val isLocked:Boolean
        get(){
            if(starts_at != null){
                return starts_at.toDate()?.after(Date() ) == true
            }
            return false
        }
    val isExpired:Boolean
        get(){
            if(ends_at != null){
                return ends_at.toDate()?.before(Date() ) == true
            }
            return false
        }
}


data class APIGoalsCollectableRequest(
    val goal_user_instance_task_id: String,
)

data class APIPostGoalsCollectableResponse(
    val was_eligible_to_claim: Boolean,
    val reward:APIPostGoalsCollectableReward?
)

data class APIPostGoalsCollectableReward(
    val cash:APIPostGoalsCollectableRewardCash?,
    val tournament_ticket:APIPostGoalsCollectableRewardTournamentTicket?,
    val tag:APIPostGoalsCollectableTag?
)

data class APIPostGoalsCollectableRewardCash(
    val reward_type: String,//APIGoalRewardType
    val amount:String
)
data class APIPostGoalsCollectableRewardTournamentTicket(
    val id: String,
    val game_id:String,
    val tournament_id:String,
)
data class APIPostGoalsCollectableTag(
    val id: String,
    val name:String
)
data class APIGoalsCollectedResponse(
    val results: List<APICollectedGoal>,
    val lifetime_rewards_claimed:String,
    val count: Int
)

data class APICollectedGoal(
    val id: String,
    val completed_at: String,
    val collected_at: String,
    val goal_title: String?,
    val reward: String,
    val was_eligible_to_claim:Boolean,
    val reward_type:DailyRewardInfoType = DailyRewardInfoType.CASH,
    val tournament:APIDailyRewardTournament? = null,
    override val is_deposit_goal: Boolean?, override val task_title: String?,
    override val starts_at: String?,
    override val ends_at: String?,
    override val has_ticket_reward: Boolean?,
    override val has_tag_reward: Boolean?
):APIActiveGoal(){
    override val _id: String
        get() = id
    override val goalType: APIGoalType
        get() = APIGoalType.Standard
    override val isDailyReward: Boolean
        get() = tournament != null
    override val rewardType: APIGoalRewardType?
        get() = null
    override val sortedTasks: List<APIGoalsTask>
        get() = listOf()
    override val cashString: String
        get(){
            return reward.toDollarString() ?: "0.00".toDollarString()
        }

    override val titleString: String
        get(){
            return goal_title ?: ""
        }
    override val subTitleString: String
        get() = ""
    override val descString: String
        get() = task_title ?: ""
    override val currentProgress: Int
        get() = 1
    override val requiredProgress: Int
        get() = 1
    override val currentStep: Int
        get() = 1
    override val requiredStep: Int
        get() = 1
    override val isComplete: Boolean
        get() = true

    private val _claimState = MutableStateFlow(APIActiveGoalClaimState.Complete)
    override val claimState: MutableStateFlow<APIActiveGoalClaimState>
        get() = _claimState
    override var eligibleToClaim: Boolean = was_eligible_to_claim
    override fun containsTask(id:String): Boolean {
        return false
    }
    override val goalUserInstanceTaskId: String?
        get() = null
    override val rewardValue: Float
        get() = reward.toFloat()
    override val displayOrder: Int
        get() = 3
    override val icon: String?
        get() = null
    override val ctaString: String
        get() = ""
    override val linkString: String
        get() = ""
    override val gameId: String?
        get() = null
    override val cluster_title: String?
        get() = null
    override val cluster_icon: String?
        get() = null
}


data class APIGoalsCompletableResponse(
    val results: List<APICompletableGoal>,
    val count: Int
)

data class APICompletableGoal(
    val id: String,
    val goal : APIGoalDetail,
    val tasks: List<APIGoalsTask>,
    override val is_deposit_goal: Boolean, override val task_title: String?,
    override val starts_at: String?,
    override val ends_at: String?,
):APIActiveGoal(){
    override val _id: String
        get() = id
    override val goalType: APIGoalType
        get() = goal.goalType
    override val isDailyReward: Boolean
        get() = false
    override val rewardType: APIGoalRewardType
        get() = goal.rewardType
    override val sortedTasks: List<APIGoalsTask>
        get() = tasks.sortedBy { it.order }
    override val cashString: String
        get(){
            val task = tasks.find { it.isCompletable}
            return (task?.reward ?: "0.00").toDollarString()
        }

    override val titleString: String
        get() = goal.title
    override val subTitleString: String
        get() = goal.subtitle
    override val descString: String
        get() = goal.description ?: ""
    override val currentProgress: Int
        get() = sortedTasks.find { !it.isComplete }?.current_progress?.toDouble()?.toInt() ?: 0
    override val requiredProgress: Int
        get() = sortedTasks.find { !it.isComplete }?.required_progress?.toDouble()?.toInt() ?: 1
    override val currentStep: Int
        get() = if(goalType == APIGoalType.Cluster) 1 else tasks.filter { it.isComplete }.size + 1
    override val requiredStep: Int
        get() = if(goalType == APIGoalType.Cluster) 1 else tasks.size
    override val isComplete: Boolean
        get()  = false
    private val _claimState = MutableStateFlow(APIActiveGoalClaimState.Not_Claim)
    override val claimState: MutableStateFlow<APIActiveGoalClaimState>
        get() = _claimState
    override var eligibleToClaim: Boolean = true
    override fun containsTask(id:String): Boolean {
        return tasks.find { it.goal_user_instance_task_id == id } != null
    }
    override val goalUserInstanceTaskId: String?
        get() = null
    private var reward = 0f
    override val rewardValue: Float
        get() {
            if (reward == 0f){
                sortedTasks.forEachIndexed() tasks@ {indexTasks, task ->
                    if(!task.isComplete || task == sortedTasks.last()){
                        reward = sortedTasks[indexTasks].reward?.toFloat()?: 0f
                        return@tasks
                    }
                }
            }
            return reward
        }
    override val displayOrder: Int
        get() {
            if(goalType == APIGoalType.Cluster){
                sortedTasks.forEach {
                    if(it.isComplete && it.goal_user_instance_task_id != null){
                        return 1
                    }
                }
            }
            return 2
        }
    override val icon: String
        get() = goal.icon
    override val ctaString: String
        // The -1 is to get the index of the step from the display value
        get() = tasks[currentStep-1].cta
    override val linkString: String
        get() = tasks[currentStep-1].link
    override val gameId: String?
        get() = tasks[currentStep-1].game_id
    override val cluster_title: String?
        get() = goal.cluster_title
    override val cluster_icon: String?
        get() = goal.cluster_icon
    override val has_ticket_reward: Boolean?
        get() = sortedTasks.firstOrNull { !it.isComplete }?.has_ticket_reward
    override val has_tag_reward: Boolean?
        get() = sortedTasks.firstOrNull { !it.isComplete }?.has_tag_reward
}

enum class APIGoalType(private val rawValue:String, val eventPropertyPrefix: String) {
    /**
     * Note: previous code sent "Active Rewards" as [eventPropertyPrefix] for this type,
     * so continue to do so.
     */
    Chain("CHAIN", "Active Rewards"),

    Standard("STANDARD" , "Active Rewards"),

    /**
     * Note: the Cluster name is appended to [eventPropertyPrefix].
     */
    Cluster("CLUSTER", "Cluster: "),

    ;

    companion object {
        fun fromRawValue(rawValue: String): APIGoalType? {
            return entries.find { it.rawValue == rawValue }
        }
    }
}

enum class APIGoalRewardType(private val rawValue:String, val eventProperty: String){
    faceoff("FACEOFF", "faceoff_bonus"),
    sportsbook("SPORTSBOOK", "sbk_bonus_bets"),
    casino("CASINO", "casino_bonus"),
    racing( "RACING", "racing_bonus"),
    dfs("DFS", "dfs_bonus"),
    freebetbonus("FREE_BET_BONUS", "free_bet_bonus"),
    ;
    companion object {
        fun fromRawValue(rawValue: String): APIGoalRewardType? {
            return entries.find { it.rawValue == rawValue }
        }
    }
}

data class APIGoalsStatusResponse(
    val processed_at:String?,
    val ids:List<String>
)

data class ClusterRewardsData(
    val title: String?,
    val icon: String?,
    val totalRewards: Float,
    val subtitle: String,
    val description: String
)
