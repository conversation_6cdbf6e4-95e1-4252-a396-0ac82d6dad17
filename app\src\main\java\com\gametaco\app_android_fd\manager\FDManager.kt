package com.gametaco.app_android_fd.manager

import AppsFlyerManager
import android.app.Activity
import com.fanduel.core.libs.account.Account
import com.fanduel.core.libs.account.contract.ISession
import com.fanduel.core.libs.account.contract.SessionHint
import com.fanduel.core.libs.accountmfa.AccountMFA
import com.fanduel.core.libs.accountverification.contract.IAccountVerification
import com.fanduel.core.libs.commonmodules.CommonModules
import com.fanduel.core.libs.wallet.Wallet
import com.fanduel.coremodules.config.CoreConfig
import com.fanduel.coremodules.config.contract.Config
import com.fanduel.coremodules.deeplinks.CoreDeepLinks
import com.fanduel.coremodules.events.CoreEvents
import com.fanduel.coremodules.events.contract.Event
import com.fanduel.coremodules.ioc.CoreIoC
import com.fanduel.coremodules.px.CorePx
import com.fanduel.coremodules.webview.AuthMode
import com.fanduel.coremodules.webview.plugins.CoreWebViewPluginRegistry
import com.fanduel.coremodules.webview.plugins.ICoreWebViewPlugin
import com.fanduel.libs.accounthub.AccountHub
import com.fanduel.libs.accounthub.contract.AccountHubAction
import com.fanduel.libs.responsiblegaming.ResponsibleGaming
import com.fanduel.libs.salesforcelibrary.SalesforceChat
import com.fanduel.libs.salesforcelibrary.domain.SalesforceConfig
import com.gametaco.app_android_fd.MainApplication
import com.gametaco.app_android_fd.OnReturnToMainActivityEvent
import com.gametaco.app_android_fd.data.AppConstants
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.data.EnvConfigProviderImpl
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.isNonProd
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.manager.analytics.AppsFlyerEventFactory
import com.gametaco.app_android_fd.ui.popups.CoreWebViewFactory
import com.gametaco.app_android_fd.ui.popups.FDWebviewPresenter
import com.gametaco.app_android_fd.ui.popups.IFDWebviewPresenter
import com.gametaco.app_android_fd.utils.CoroutineScopes
import com.gametaco.app_android_fd.utils.log.Logger
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

enum class FDWebviewPage(val value: Int){
    None(0),
    Home(1),
    Deposit(2),
    Withdrawal(3),
    Transactions(4),
    Login(5),
    Join(6),
    ResponsiblePlay(7),
    TaxInfo(8),
    UserLimits(9),
    SetUpMfa(10),
    Verification(11),
    AcceptTerms(12),
    Activity(13),
    Terms(14),
    ProfitBoosts(15),
    RealityCheck(16),
    ProvePrefill(17),
    TopUpFunds(18),
    Settings(19),
    Support(20);


}

data class GAPageLoadEventData(
    val event: String?,
    val registrationStatus: String?,
    val loginStatus: String?,
    val product: String?,
    val siteVersion: String?,
    val fdAccountId: String?,
    val balance: String?,
    val pagePath: String?,
    val pageTitle: String?,
    val jurisdiction: String?,
    val referrerUrl: String?,
    val userFdpBalance: String?,
    val customerStatus: String?,
)

class FDManager(
    private val authenticationManager: AuthenticationManager,
    private val worldWinnerAPI: WorldWinnerAPI,
    private val brazeManager: BrazeManager,
    private val walletManager: WalletManager,
    private val activityManager: ActivityManager,
    private val preferencesManager: PreferencesManager,
    private val toastManager: ToastManager,
    private val coroutineScopes: CoroutineScopes,
) {

    companion object {
        const val TAG = "FDManager"
        val instance: FDManager
            get() = resolve()
    }

    private lateinit var fdWebviewModalPresenter: IFDWebviewPresenter
    private lateinit var coreWebViewFactory: CoreWebViewFactory

    var coreConfig : Config? = null

    var signingUp: Boolean = false
    var currentPage : String? = null
    var webviewVisible : Boolean = false
    var potentialDeposit : Boolean = false
    var visitedAvatarPage : Boolean = false
    val activity: Activity
        get() = activityManager.activity

    var initialized : Boolean = false
    var accountHubBackRoute : String = Routes.GAMES_SCREEN

    /**
     * A reference to the delayed job that will show the error UI.
     * We hold this reference so we can cancel it if needed.
     */
    private var pendingShowAccountErrorJob: Job? = null

    /**
     * When using the unified module, and the user signs out, it is the expected flow that the
     * current account session will be set to null, and shortly after that,
     * [handleAccountHubAction] will be called with [AccountHubAction.AccountHubEnded].
     *
     * In such cases, we do not want to show the error UI immediately. See FX-3362.
     */
    private val pendingShowAccountErrorDelay = 1000L

    /**
     * Used to so the new Salesforce UI in the UM.
     */
    private val salesForceNewUIPlugin = object: ICoreWebViewPlugin {
        override val id = "fanduel/salesforce/new-ui"
        override val cookies = mapOf(Pair("fd-app-new-support-ui", "true"))
    }

    init {
        initialized = false
        try {
            EventBus.getDefault().register(this);

            coreConfig = Config(
                appDomain = AppEnv.current.fanduel_appdomain,
                environment = AppEnv.current.fanduel_environment,
                clientAuthToken = "0eacf24c834dbda2e547cf9ee208debd" //this is the offical working one
            )

            CoreConfig.instance.setContext(MainApplication.context)
            CoreConfig.instance.setConfig(coreConfig!!)
            CorePx.instance.initialize()
            CommonModules.instance.initialize()
            //AccountMFA.instance.initialize()
            Account.instance.initialize()
            CoreEvents.instance.initialize()

            //listen for events
            coroutineScopes.main.launch {
                CoreEvents.instance.observableEvents.collect { event ->
                    handleCoreEvent(event)
                }
            }

            //listen for session changes
            coroutineScopes.main.launch {
                Account.instance.getObservableSession()?.collect { session ->
                    Logger.d(TAG, "Account.instance.getObservableSession $session")

                    if (session == null) {
                        Account.instance.clearSession(AppEnv.current.fanduel_appdomain)
                        pendingShowAccountErrorJob?.cancel()
                        pendingShowAccountErrorJob = coroutineScopes.main.launch {
                            delay(pendingShowAccountErrorDelay)

                            if (!authenticationManager.isGuest && hasSessionData) {
                                Logger.d(TAG, "FDManager: listening for session changes, session is null, called Account.clearSession")
                                showAccountError()
                            }
                            pendingShowAccountErrorJob = null
                        }
                    } else {
                        // If the session is not null, we can cancel the pending job
                        pendingShowAccountErrorJob?.also { job ->
                            Logger.d(TAG, "FDManager: listening for session changes, session is not null, cancelling pendingShowAccountErrorJob")
                            job.cancel()
                            pendingShowAccountErrorJob = null
                        }
                    }
                }
            }
            Wallet.instance.initialize()

            CoreDeepLinks.instance.initialize()
            CoreDeepLinks.instance.setHost(AppConstants.Trustly.URL_BASE)
            ResponsibleGaming.instance.initialize()

            val salesForceConfig = SalesforceConfig(
                AppEnv.current.fanduel_salesforce_orgId,
                AppEnv.current.fanduel_salesforce_deploymentId,
                AppEnv.current.fanduel_salesforce_buttonId,
                AppEnv.current.fanduel_salesforce_liveAgentPod,
                "sportsbook-action://open_chat")

            SalesforceChat.instance.initialize(salesForceConfig)

            AccountHub.instance.initialize()
            AccountHub.instance.registerDeepLinks()

            CoreWebViewPluginRegistry.instance.add(salesForceNewUIPlugin)

            //listen for actions
            coroutineScopes.main.launch {
                AccountHub.instance.observeActions().collect { accountHubAction ->
                    handleAccountHubAction(accountHubAction)
                }
            }

            coreWebViewFactory = CoreWebViewFactory()

            fdWebviewModalPresenter = FDWebviewPresenter(CoreIoC.instance, coreWebViewFactory, coroutineScopes.main)
                .also {
                    coroutineScopes.main.launch {
                        it.modalActivity.collect { modalActivity ->
                            if (modalActivity != null) {
                                SalesforceChat.instance.setActivity(modalActivity)
                            }
                        }
                    }
            }
        } catch (e: Exception) {
            Logger.d(TAG, "Caught exception: ${e.message}")
            initialized = true
        }

        initialized = true
    }

    fun handleAccountHubAction(action : AccountHubAction) {
        Logger.d(TAG, "handleAccountHubAction: ${action}")

        when (action) {
            is AccountHubAction.AccountHubEnded -> {
                NavManager.instance.navigateClearBackStack(accountHubBackRoute)

                // If this function is called within 300ms of the job's launch,
                // the job will be cancelled before it can call showAccountError().
                // If it's called after 300ms, the job will have already completed,
                // and calling cancel() here will have no effect.
                val job = pendingShowAccountErrorJob
                if (job != null) {
                    Logger.d(TAG, "Cancelling showAccountErrorJob")
                    job.cancel()
                    // Send the event to notify that the session has changed
                    EventBus.getDefault().post(OnSessionChangedEvent(false, "AccountHubAction.AccountHubEnded"))
                }
            }
        }

    }

    fun showAccountHub(backRoute : String) {
        accountHubBackRoute = backRoute
        AccountHub.instance.presentAccountHubAsync(AppEnv.current.fanduel_appdomain)
    }

    fun showAccountError() {
        AlertDialogManager.instance.showDialog(
            "An Error Has Occurred",
            "To continue playing, please re-log in to your account",
            "Got it",
            {
                EventBus.getDefault().post(OnSessionChangedEvent(false, "FDManager.showAccountError"))
            })
    }

    fun handleCoreEvent(event : Event){

        Logger.d(TAG, "handleCoreEvent: ${event}")

        when(event.name) {
//            Wallet.BALANCE_MAY_HAVE_CHANGED_NAME -> {
            //hardcode it for now, might need an update when the new wall lib is available
            "CWVBalanceMayHaveChanged" -> {
                coroutineScopes.main.launch {
                    worldWinnerAPI.postPotentialDeposit()
                    authenticationManager.refreshLoginForRestrictedStatus()
                    walletManager.refreshWallet(true)
                }
            }
            "ga_pageload" -> {
                if(event.data != null) {

                    val moshi = Moshi.Builder()
                        .add(KotlinJsonAdapterFactory())
                        .build()
                    val jsonAdapter = moshi.adapter(GAPageLoadEventData::class.java)
                    val logData = jsonAdapter.fromJson(event.data)!!

                    if(logData != null) {

                        //check page path
                        when {
                            logData.pagePath?.contains("/join?p=") == true -> {
                                signingUp = true
                            }

                            logData.pagePath == "/login" -> {
                                signingUp = false
                            }
                        }

                        //check referrer url
                        when(logData.referrerUrl) {
                            "account.settings.avatar" -> {
                                visitedAvatarPage = true
                            }
                        }
                    }
                }
            }
            "Help and Support CTA Clicked" -> {
                val activity = ActivityManager.instance.currentActivity.value
                SalesforceChat.instance.setActivity(activity ?: this.activity)
            }
        }
    }

    var sessionDataSessionID: String?
        get() = preferencesManager.getFanduelSessionDataSessionID()
        set(value) {
            preferencesManager.setFanduelSessionDataSessionID(value)
        }

    var sessionDataToken: String?
        get() = preferencesManager.getFanduelSessionDataToken()
        set(value) {
            preferencesManager.setFanduelSessionDataToken(value)
        }

    var sessionDataLoginToken: String?
        get() = preferencesManager.getFanduelSessionDataLoginToken()
        set(value) {
            preferencesManager.setFanduelSessionDataLoginToken(value)
        }

    val sessionDataExpiryFlow = MutableStateFlow<String>("none")

    fun refreshSessionExpiryValueForCurrentSession() {
        coroutineScopes.main.launch {
            _refreshSessionExpiryValueForCurrentSession()
        }
    }

    private suspend fun _refreshSessionExpiryValueForCurrentSession() {

        sessionDataExpiryFlow.value = "loading..."

        val session = Account.instance.getSessionAsync(SessionHint.Silent).await()

        if(session != null) {
            sessionDataExpiryFlow.value = session.expires.toString()
        } else {
            sessionDataExpiryFlow.value = "null session"
        }
    }



    var sessionDataDateCreated: Date?
        get()
        {
            val dateString : String? = preferencesManager.getFanduelSessionDataDateCreated()

            if (dateString == null)
                return null

            // Pattern matching the format of your date string
            val dateFormat = SimpleDateFormat("EEE MMM dd HH:mm:ss 'GMT'Z yyyy", Locale.ENGLISH)

            try {
                return dateFormat.parse(dateString)
            } catch (e: ParseException) {
                // Handle the case where the date cannot be parsed
                e.printStackTrace()
                return null
            }
        }
        set(value) {
            if(value != null) {
                // Formatting the date back to the original string format before saving
                val dateFormat = SimpleDateFormat("EEE MMM dd HH:mm:ss 'GMT'Z yyyy", Locale.ENGLISH)
                preferencesManager.setFanduelSessionDataDateCreated(dateFormat.format(value))
            } else {
                preferencesManager.setFanduelSessionDataDateCreated(null)
            }
        }

    fun buildFanduelURL(path : String?) : String{

        var url = AppEnv.current.fanduel_account_url

        if(!path.isNullOrEmpty())
            url += path

        return url
    }

    fun showWebviewFD(page : FDWebviewPage? = FDWebviewPage.Login, sourceURL : String? = null, title:String? = null) {
        if(webviewVisible){
            //prevent brutal clicks and multiple opens with same page
            if(page == null || page == FDWebviewPage.None){
                if(currentPage == sourceURL) {
                    return
                }
            }else if(currentPage == page.name){
                return
            }
        }

        if (page != null) {
            AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Open_Fanduel_Webview.value,
                properties = mapOf("page" to page.toString())))
        } else sourceURL?.let {
            AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Open_Webview.value,
                properties = mapOf("url" to sourceURL)))
        }


        if(page == null || page == FDWebviewPage.None) {
            //little nasty here, we use recursive call for support, have to convert it to FDWebviewPage
            if(sourceURL == "/support"){
                currentPage = FDWebviewPage.Support.name
            }else{
                currentPage = sourceURL
            }

            val webViewURL = sourceURL?.takeIf { it.contains("http", ignoreCase = true) }
                ?: sourceURL?.let { buildFanduelURL(it) }

            webViewURL?.let { url ->
                fdWebviewModalPresenter.present(
                    source = url,
                    authMode = AuthMode.Current,
                    titleString = title.orEmpty(),
                    actionButtonString = null
                )
            }
            webviewVisible = true

        } else {
            currentPage = page.name

            when(page) {

                FDWebviewPage.None -> {}
                FDWebviewPage.Home -> {
                    Wallet.instance.showAccountHomeAsync(AppEnv.current.fanduel_appdomain)
                    webviewVisible = true
                }
                FDWebviewPage.Deposit -> {
                    Wallet.instance.depositFundsAsync(AppEnv.current.fanduel_appdomain)
                    webviewVisible = true
                }
                FDWebviewPage.Withdrawal -> {
                    Wallet.instance.withdrawFundsAsync(AppEnv.current.fanduel_appdomain)
                    webviewVisible = true
                }
                FDWebviewPage.Transactions -> {
                    Wallet.instance.showTransactionsAsync(AppEnv.current.fanduel_appdomain)
                    webviewVisible = true
                }
                FDWebviewPage.Login -> {}
                FDWebviewPage.Join -> {}
                FDWebviewPage.ResponsiblePlay -> {
                    ResponsibleGaming.instance.presentResponsiblePlaySettingsAsync(
                        AppEnv.current.fanduel_appdomain,
                        showHeader = false,
                    )
                    webviewVisible = true
                }
                FDWebviewPage.TaxInfo -> {
                    Wallet.instance.showTaxAsync(AppEnv.current.fanduel_appdomain)
                    webviewVisible = true
                }
                FDWebviewPage.UserLimits -> {}
                FDWebviewPage.SetUpMfa -> {}
                FDWebviewPage.Verification -> {
                    val accountVerification = CoreIoC.instance.resolve(IAccountVerification::class.java)
                    accountVerification?.verifyUserAsync(AppEnv.current.fanduel_appdomain)
                    webviewVisible = true
                }
                FDWebviewPage.AcceptTerms -> {
                    Account.instance.acceptTermsAsync(AppEnv.current.fanduel_appdomain)
                    webviewVisible = true
                }
                FDWebviewPage.Activity -> {
                    Wallet.instance.showActivityAsync(AppEnv.current.fanduel_appdomain)
                    webviewVisible = true
                }
                FDWebviewPage.Terms -> {
                    Account.instance.acceptTermsAsync(AppEnv.current.fanduel_appdomain)
                    webviewVisible = true
                }
                FDWebviewPage.ProfitBoosts -> {}
                FDWebviewPage.RealityCheck -> {
                    ResponsibleGaming.instance.presentRealityCheckSettingsAsync(AppEnv.current.fanduel_appdomain)
                    webviewVisible = true
                }
                FDWebviewPage.ProvePrefill -> {}
                FDWebviewPage.TopUpFunds -> {}
                FDWebviewPage.Settings -> {
                    Account.instance.presentAccountSettingsAsync(AppEnv.current.fanduel_appdomain)
                    webviewVisible = true
                }
                FDWebviewPage.Support ->{
                    showWebviewFD(page = null, sourceURL = "/support",title)
                }
                else-> { }
            }

        }

    }

    val hasSessionData : Boolean
        get() = (sessionDataSessionID != null
                && sessionDataDateCreated != null
                && sessionDataToken != null)

    fun resetLogin() {
        sessionDataToken = null
        sessionDataSessionID = null
        sessionDataDateCreated = null
        sessionDataLoginToken = null
        // Forcibly clear the session. See #fx-3111
        Account.instance.clearSession(AppEnv.current.fanduel_appdomain)
    }

    fun keepAlive() {
        Account.instance.keepAlive(AppEnv.current.fanduel_appdomain)
    }

    fun login() {
        coroutineScopes.main.launch {
            val envConfig = EnvConfigProviderImpl.envConfig
            if (envConfig.env.isNonProd) {
                toastManager.showToast("Using \"${envConfig.envHost?.envHeader}\" environment")
            }

            val session = Account.instance.getSessionAsync(SessionHint.ForceNew).await()
//            val session = Account.instance.getSessionAsync(SessionHint.Normal).await()

            handleSessionResult(session)
        }
    }

    fun signup()
    {
        coroutineScopes.main.launch {
            val session = Account.instance.getSessionAsync(SessionHint.NewUser).await()

            //special code for amplitude experiments
            //this tracks the registration event for anyone that signs up and isn't logged in
            if(!hasSessionData && session?.sessionId != null && session?.token != null)
            {
                AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Completed_Registration.value))
            }

            if(authenticationManager.isGuest && session == null)
            {
                NavManager.instance.navigateBackRequested()
            } else {
                handleSessionResult(session)
            }
        }
    }

    suspend fun handleSessionResult(session : ISession?) {


        if(session?.sessionId != null && session?.token != null) {
            //Clear the webview visibility flag during login, this allows the webview to close without any app navigation occurring upon returning to the main activity
            webviewVisible = false
            currentPage = null

            sessionDataSessionID = session?.sessionId
            sessionDataDateCreated = session?.created
            sessionDataToken = session?.token

            Logger.d(TAG, "handleSessionResult:\nsession.sessionId: ${session.sessionId}\nsession.token: ${session.token}\nsession.created: ${session.created}\nsession.expires: ${session.expires}")

            val user = Account.instance.getUserAsync(SessionHint.Silent).await()

            Logger.d(TAG, "Account.instance.getUserAsync: ${user}")

            val loginState = authenticationManager.login(
                authToken = sessionDataToken!!,
                false)

            if (signingUp && loginState.isSuccess()) {
                AppsFlyerManager.instance.logEvent(AppsFlyerEventFactory.instance.completeRegistration)
                AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Completed_Registration.value))
                PreferencesManager.instance.setIsHapticsEnabled(false)
            }

        } else {
            //don't post on session changed for guests here, if you do the user will get logged out, badness etc..
            if(!authenticationManager.isGuest){
                EventBus.getDefault().post(OnSessionChangedEvent(false, "FDManager.handleSessionResult"))
            }
        }


    }



    fun showAcceptTerms() {
        coroutineScopes.main.launch {
            Account.instance.acceptTermsAsync(AppEnv.current.fanduel_appdomain).await()
        }
    }

    fun setupMFA() {
        coroutineScopes.main.launch {

            val result = AccountMFA.instance.setupMFAAsync(AppEnv.current.fanduel_appdomain).await()

            Logger.d(TAG, "AccountMFA.instance.setupMFAAsync ${result}")
        }
    }



    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReturnToMainActivityEventHandler(event: OnReturnToMainActivityEvent) {

        //returning to main activity after leaving webview, this should handle both hardware back and navigation icon click returning from webview
        if(webviewVisible) {
            NavManager.instance.navigateBackRequested()
        }
        webviewVisible = false
        currentPage = null

        //if needed update api me so that avatar url updates
        if(visitedAvatarPage != false) {
            visitedAvatarPage = false
            coroutineScopes.main.launch {
                authenticationManager.getMe()
            }
        }
    }

    //this primes the session data, without this call auth doesn't work in external pages that require auth.
    suspend fun tryGetExistingSessionData() {


        val attemptedAwToUM = preferencesManager.getAttemptedAwToUM()
        val loginToken = sessionDataLoginToken

        var session : ISession? = null

        //try migrate to UM
        if(!attemptedAwToUM && loginToken != null) {
            preferencesManager.setAttemptedAwToUM(true)

            Logger.d(TAG, "tryGetExistingSessionData: migrating to UM")

            session = Account.instance.getSessionAsync(SessionHint.UseLoginToken(loginToken)).await()

        } else {

            val validSessionResult = Account.instance.checkValidSession().await()
//
            Logger.d(TAG, "tryGetExistingSessionData: validSessionResult: ${validSessionResult}")

            session = Account.instance.getSessionAsync(SessionHint.Silent).await()

        }

        if(session != null) {
            Logger.d(TAG, "tryGetExistingSessionData:\nsession.sessionId: ${session.sessionId}\nsession.token: ${session.token}\nsession.created: ${session.created}\nsession.expires: ${session.expires}")
        }

        //null session resetting login
        if(session == null){
            Logger.d(TAG, "tryGetExistingSessionData: null session, resetting login")
            resetLogin()
            return
        }

        if(session?.sessionId != null && session?.token != null) {
            sessionDataToken = session?.token //fd have said to use session.token in place of sessionData.id from AWWebview //authToken
            sessionDataSessionID = session?.sessionId
            sessionDataDateCreated = session?.created
        }
    }

}
