package com.gametaco.app_android_fd.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APIGamesResponse
import com.gametaco.app_android_fd.data.entity.APIGamesResponseGame
import com.gametaco.app_android_fd.data.entity.APITournament
import com.gametaco.app_android_fd.data.entity.APITournamentsResponse
import com.gametaco.app_android_fd.data.entity.GCGameData
import com.gametaco.app_android_fd.data.entity.GameCatalogResponse
import com.gametaco.app_android_fd.data.entity.GameCatalogVersion
import com.gametaco.app_android_fd.data.entity.RowTypes
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.AlertDialogManager
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.ErrorManager
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.models.LobbyUiState
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.utilities.ResourceState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch


class GamesViewModel : ViewModel() {

    init {
        Logger.d(TAG, "Init GamesViewModel")
    }

    private val tournamentManager: TournamentManager by lazy {
        TournamentManager.instance
    }

    private val _firstTimeLoad = MutableStateFlow(true)
    private val dataReady = MutableStateFlow(false)

    // Define a channel for UI events
    private val _uiEvent = Channel<UiEvent>(Channel.BUFFERED)
    val uiEvent = _uiEvent.receiveAsFlow()

    // Function to trigger scroll to top
    fun scrollLobbyToTop() {
        _uiEvent.trySend(UiEvent.ScrollToTop)
    }

    // Define UI events
    sealed class UiEvent {
        object ScrollToTop : UiEvent()
        // Add other events if needed
    }


    fun loadData(){
        viewModelScope.launch (Dispatchers.IO) {
            getRecentGames()
            getGameCatalog()
            getGamesData()
            populateGamesDataDictionary()
            _firstTimeLoad.value = false
            dataReady.value = true
        }
    }

    private val _recentlyPlayedGames = MutableStateFlow<List<String>>(listOf())
    val recentlyPlayedGames = _recentlyPlayedGames.asStateFlow()


    private suspend fun getRecentGames(){
        if(AuthenticationManager.instance.isGuest) {
            return
        }
        val res = WorldWinnerAPI.instance.getRecentPlayedGames()
        if(res is ResourceState.Success){
            _recentlyPlayedGames.value= res.data.results
        }
    }

    private val _gameCatalog : MutableStateFlow<ResourceState<GameCatalogResponse>> = MutableStateFlow(ResourceState.Loading())
    private val gameCatalog : StateFlow<ResourceState<GameCatalogResponse>> = _gameCatalog

    private val _gameCatalogVersion: MutableStateFlow<GameCatalogVersion?> = MutableStateFlow(null)
    private val gameCatalogVersion: GameCatalogVersion?
        get() = _gameCatalogVersion.value

    private suspend fun updateGameCatalogVersion():Boolean{
        val currentVersion: GameCatalogVersion? = _gameCatalogVersion.value

        val res = WorldWinnerAPI.instance.getGameCatalogVersion()
        if(res is ResourceState.Success){
            val data: GameCatalogVersion = res.data
            _gameCatalogVersion.value = data

            if (currentVersion == null || currentVersion != data) {
                return true
            }
        }
        return false
    }
    private suspend fun getGameCatalog() {
        updateGameCatalogVersion()
        val gameCatalogVersion = gameCatalogVersion
        val cachedGameCatalogVersion = PreferencesManager.instance.getCachedGameCatalogVersion()
        val catalogCache = PreferencesManager.instance.getCatalogCache()
        val update = gameCatalogVersion == null
                || cachedGameCatalogVersion != gameCatalogVersion
                || catalogCache == null

        if (update) {
            Logger.d(TAG, "Fetching new catalog")
            WorldWinnerAPI.instance.getGameCatalog()
                .collectLatest { gamesCatalogResponse ->
                    if (_firstTimeLoad.value) {
                        _gameCatalog.value = gamesCatalogResponse
                    }

                    when (gamesCatalogResponse) {
                        is ResourceState.Loading -> {
                        }
                        is ResourceState.Error -> {
                            if(!ErrorManager.instance.handleResourceStateError(gamesCatalogResponse)) {
                                AlertDialogManager.instance.showDialog("Error", gamesCatalogResponse.error, "Confirm", {} )
                            }

                            Logger.e(TAG, "getGameCatalog is not success, not calling UtilityManager.instance.updateGameData")
                        }
                        is ResourceState.Success -> {
                            if(!_firstTimeLoad.value){
                                _gameCatalog.value = gamesCatalogResponse
                            }
                            PreferencesManager.instance.setCatalogCache(
                                data = gamesCatalogResponse.data,
                                gameCatalogVersion = requireNotNull(this.gameCatalogVersion),
                            )
                            tournamentManager.updateGameData(gamesCatalogResponse.data.data.games)
                        }
                    }
                }
        } else {
            requireNotNull(catalogCache) { "Catalog cache should not be null" }
            _firstTimeLoad.value = false
            _gameCatalog.value = ResourceState.Success(catalogCache)
            tournamentManager.updateGameData(catalogCache.data.games)
            Logger.d(TAG, "Using cached catalog")
        }
    }

    private val _gamesData : MutableStateFlow<ResourceState<APIGamesResponse>> = MutableStateFlow(ResourceState.Loading())
    private val gamesData : StateFlow<ResourceState<APIGamesResponse>> = _gamesData

    private suspend fun getGamesData(){
        if(AuthenticationManager.instance.isGuest) {
            val gamesDataResponse = WorldWinnerAPI.instance.getGuestGames()
            if(_firstTimeLoad.value){
                _gamesData.value = gamesDataResponse
            }

            when (gamesDataResponse) {
                is ResourceState.Loading -> {
                }
                is ResourceState.Error -> {
                    if(!ErrorManager.instance.handleResourceStateError(gamesDataResponse)) {
                        AlertDialogManager.instance.showDialog(
                            "Error",
                            gamesDataResponse.error,
                            "Confirm",
                            {})
                    }
                }
                is ResourceState.Success -> {
                    if(!_firstTimeLoad.value){
                        _gamesData.value = gamesDataResponse
                    }
                    PreferencesManager.instance.setAvailableGamesCache((gamesDataResponse as ResourceState.Success).data)
                }
            }

        } else {
            val gamesDataResponse = WorldWinnerAPI.instance.getGames()
            if(_firstTimeLoad.value){
                _gamesData.value = gamesDataResponse
            }

            when (gamesDataResponse) {
                is ResourceState.Loading -> {
                }
                is ResourceState.Error -> {
                    if(!ErrorManager.instance.handleResourceStateError(gamesDataResponse)) {
                        AlertDialogManager.instance.showDialog(
                            "Error",
                            gamesDataResponse.error,
                            "Confirm",
                            {})
                    }
                }
                is ResourceState.Success -> {
                    if(!_firstTimeLoad.value){
                        _gamesData.value = gamesDataResponse
                    }
                    PreferencesManager.instance.setAvailableGamesCache((gamesDataResponse as ResourceState.Success).data)
                }
            }
        }
    }

    private val gamesDataDictionary: MutableMap<String, APIGamesResponseGame> = mutableMapOf()

    private suspend fun populateGamesDataDictionary(){
        gamesDataDictionary.clear()

        if(_gamesData.value is ResourceState.Success){
            val data = (_gamesData.value as ResourceState.Success).data
            for (gameData in data.results){
                gamesDataDictionary[gameData.id!!] = gameData
            }
        }

        if(_gameCatalog.value is ResourceState.Success){
            refreshFeaturedTournaments((_gameCatalog.value as ResourceState.Success).data)
        }

    }

    fun getGameDataById(
        id: String
    ): APIGamesResponseGame? {
        if(ExperimentManager.instance.isNewLobbyEnabled.value){
            return LobbyViewModel.instance.getGameDataById(id)
        }
        if (gamesDataDictionary.containsKey(id)){
            return gamesDataDictionary[id]
        }

        return null
    }

    fun getGameDateByName(gameName:String):APIGamesResponseGame?{
        if(ExperimentManager.instance.isNewLobbyEnabled.value){
            return LobbyViewModel.instance.getGameDateByName(gameName)
        }
        return gamesDataDictionary.values.firstOrNull { it.name == gameName }
    }

    private val _featuredTournaments:MutableStateFlow<MutableMap<String, APITournament>> = MutableStateFlow(mutableMapOf())
    val featuredTournaments:StateFlow<MutableMap<String, APITournament>> = _featuredTournaments.asStateFlow()

    private suspend fun refreshFeaturedTournaments(catalog:GameCatalogResponse) {
        val tournamentIds = mutableSetOf<String>()
        for (row in catalog.rows){
            if(row.row_type == RowTypes.TOURNAMENT){
                for (item in row.items){
                    tournamentIds.add(item.tournament_id?:"")
                }
            }
        }
        val ids = tournamentIds.joinToString(",")

        viewModelScope.launch (Dispatchers.IO){

            if(AuthenticationManager.instance.isGuest) {
                val tournamentsDataResponse = WorldWinnerAPI.instance.getGuestTournamentsByTournamentIDs(ids)
                updateFeaturedTournaments(tournamentsDataResponse)
            } else {
                val tournamentsDataResponse = WorldWinnerAPI.instance.getTournamentsByTournamentIDs(ids)
                updateFeaturedTournaments(tournamentsDataResponse)
            }

        }
    }

    private fun updateFeaturedTournaments(response : ResourceState<APITournamentsResponse>){
        if(response.isSuccess()){
            val responseData = (response as ResourceState.Success).data
            val dic = mutableMapOf<String,APITournament>()
            for (tournament in responseData.results){
                dic[tournament.id] = tournament
                if(tournament.game_id != null) {
                    tournamentManager.addTournamentsIdsForGameId(
                        tournament,
                        tournament.game_id
                    )
                }
            }
            _featuredTournaments.value = dic
        } else if (response.isError()) {
            if(!ErrorManager.instance.handleResourceStateError(response)) {
                AlertDialogManager.instance.showDialog(
                    "Error",
                    (response as ResourceState.Error).error,
                    "Confirm",
                    {})
            }
        }
    }

    fun getGCGameDataForGameId(gameId : String?) : GCGameData?{
        if(!gameCatalog.value.isSuccess())
            return null

        val games = (gameCatalog.value as ResourceState.Success).data.data.games

        return games.values.find { it.game_id == gameId }
    }

    private val isGuest get() = AuthenticationManager.instance.isGuest
    private val isFtue get() = AuthenticationManager.instance.showLobbyFtue

    private val currentTournamentData = MutableStateFlow<APITournament?>(null)
    private val infoModalExpanded = MutableStateFlow(false)
    private val gameModalExpanded = MutableStateFlow(false)

    private val selectedGameData: MutableStateFlow<APIGamesResponseGame?> = MutableStateFlow(null)
    private val selectedGameCatalogData: MutableStateFlow<GCGameData?> = MutableStateFlow(null)

    private fun onLobbyUiInfoClick(tournamentData: APITournament, gameData: GCGameData?) {
        currentTournamentData.value = tournamentData
        infoModalExpanded.value = true
        selectedGameData.value = getGameDataById(
                gameData?.game_id ?: ""
            )
    }

    private fun onGameTileClicked(
        navManager: NavManager,
        gameData: GCGameData,
        lobbySelection: String?,
    ) {
        val apiGamesResponseGame = getGameDataById(gameData.game_id)
        selectedGameCatalogData.value = gameData
        selectedGameData.value = apiGamesResponseGame

        PlayViewModel.instance.setGameData(
            gameData = gameData,
            game = apiGamesResponseGame,
        )

        if (isFtue) {
            gameModalExpanded.value = true
        } else {
            navManager.navigate(Routes.CONTEST_SCREEN)

            AnalyticsManager.instance.logEvent(
                AnalyticsEvent(
                    analyticsEvent = AnalyticsEventName.Game_Selected.value,
                    properties = mapOf(
                        "Game Name" to (apiGamesResponseGame?.name ?: "Unknown"),
                        "Lobby Selection" to (lobbySelection ?: "Unknown"),
                    )
                )
            )
        }
    }

    fun resetLobbyUiState() {
        currentTournamentData.value = null
        infoModalExpanded.value = false
        selectedGameData.value = null
        selectedGameCatalogData.value = null
    }

    val lobbyUiState: StateFlow<LobbyUiState> = combine(
        gameCatalog,
        gamesData,
        dataReady,
    ) { catalogState, dataState, ready ->
        when {
            catalogState is ResourceState.Loading || dataState is ResourceState.Loading ->
                LobbyUiState.Loading
            catalogState is ResourceState.Error -> LobbyUiState.Error(catalogState.error)
            dataState is ResourceState.Error -> LobbyUiState.Error(dataState.error)
            !ready -> LobbyUiState.Loading
            catalogState is ResourceState.Success && dataState is ResourceState.Success ->
                LobbyUiState.Success(
                    catalogState.data,
                    dataState.data,
                    currentTournamentData = currentTournamentData,
                    selectedGameData = selectedGameData,
                    infoModalExpanded = infoModalExpanded,
                    gameModalExpanded = gameModalExpanded,
                    onInfoClick = ::onLobbyUiInfoClick,
                    onGameTileClicked = ::onGameTileClicked,
                )
            else -> LobbyUiState.Error("Unknown state")
        }
    }
        .stateIn(viewModelScope, SharingStarted.Eagerly, LobbyUiState.Loading)

    companion object {
        const val TAG = "GamesViewModel"
        val instance: GamesViewModel by lazy { GamesViewModel() }
    }
}
