package com.gametaco.app_android_fd.utils.log

import android.content.Context
import android.content.Intent
import androidx.core.content.FileProvider
import com.gametaco.app_android_fd.MainApplication
import com.gametaco.app_android_fd.data.LoggerInitializerImpl
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.CoroutineScopes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File

object Logger {

    internal const val Tag = "AppLogger"

    fun d(message: String, vararg args: Any?) {
        Timber.d(message, *args)
    }

    fun d(tag: String, message: String, vararg args: Any?) {
        Timber.tag(tag)
        Timber.d(message, *args)
    }

    fun e(message: String, vararg args: Any?) {
        Timber.e(message, *args)
    }

    fun e(tag: String, message: String, vararg args: Any?) {
        Timber.tag(tag)
        Timber.e(message, *args)
    }

    fun i(message: String, vararg args: Any?) {
        Timber.i(message, *args)
    }

    fun i(tag: String, message: String, vararg args: Any?) {
        Timber.tag(tag)
        Timber.i(message, *args)
    }

    fun w(message: String, vararg args: Any?) {
        Timber.w(message, *args)
    }

    fun w(tag: String, message: String, vararg args: Any?) {
        Timber.tag(tag)
        Timber.w(message, *args)
    }

    val allLogs: StateFlow<List<String>> get() = instance.allLogs

    fun clearAll() {
        instance.clearAll()
    }

    suspend fun loadAllLogs() {
        instance.loadAllLogs()
    }

    val instance by lazy {
        LoggerHelper(MainApplication.context, resolve())
    }

}

class LoggerHelper(
    private val context: Context,
    private val coroutineScopes: CoroutineScopes,
) {

    private val _allLogs = MutableStateFlow<List<String>>(emptyList())
    val allLogs: StateFlow<List<String>> get() = _allLogs

    /**
     * Loads the log file in ~1MB chunks and updates the flow after each chunk.
     * Stops reading once maxLogMemory bytes have been loaded.
     *
     * @param maxLogMemory The maximum number of bytes to load from the log file (default 8MB).
     */
    suspend fun loadAllLogs(maxLogMemory: Int = 1 * 1024 * 1024) {
        if (!LoggerInitializerImpl.loggingEnabled) return

        // Safely get the external files directory.
        val externalDir = context.getExternalFilesDir(null)
        if (externalDir == null) {
            Timber.e("External files directory not available")
            _allLogs.value = emptyList()
            return
        }

        // Look in the "logs" folder where session log files are stored.
        val logDir = File(externalDir, "logs")
        if (!logDir.exists()) {
            _allLogs.value = emptyList()
            return
        }

        // Get the most recent log file (the current session's log file).
        val logFile = logDir.listFiles { file ->
            file.name.startsWith("app_logs_") && file.name.endsWith(".txt")
        }?.maxByOrNull { it.lastModified() }

        if (logFile == null || !logFile.exists()) {
            _allLogs.value = emptyList()
            return
        }

        println("Reading log file: ${logFile.absolutePath} with size: ${logFile.length()} bytes")

        withContext(Dispatchers.IO) {
            try {
                val chunkSize = 1024 * 1024 // 1MB
                val logList = mutableListOf<String>()
                var totalBytesRead = 0L
                var currentChunkSize = 0L
                val chunkBuffer = mutableListOf<String>()

                logFile.bufferedReader().use { reader ->
                    for (line in reader.lineSequence()) {
                        val lineSize = line.toByteArray().size + 1 // approximate size (with newline)
                        if (totalBytesRead + lineSize > maxLogMemory) {
                            // Stop reading if the max allowed memory is reached.
                            break
                        }
                        totalBytesRead += lineSize
                        chunkBuffer.add(line)
                        currentChunkSize += lineSize

                        if (currentChunkSize >= chunkSize) {
                            logList.addAll(chunkBuffer)
                            _allLogs.value = logList.toList() // update flow after each chunk
                            chunkBuffer.clear()
                            currentChunkSize = 0L
                        }
                    }
                }

                // Append any remaining lines.
                if (chunkBuffer.isNotEmpty()) {
                    logList.addAll(chunkBuffer)
                    _allLogs.value = logList.toList()
                }
            } catch (e: OutOfMemoryError) {
                Timber.e(e, "OutOfMemoryError while loading logs")
            }
        }
    }

    fun clearAll() {
        val externalDir = context.getExternalFilesDir(null)
        if (externalDir == null) {
            Timber.e("External files directory not available")
            _allLogs.value = emptyList()
            return
        }
        val logDir = File(externalDir, "logs")
        if (!logDir.exists()) {
            _allLogs.value = emptyList()
            return
        }
        // Get the most recent session log file.
        val logFile = logDir.listFiles { file ->
            file.name.startsWith("app_logs_") && file.name.endsWith(".txt")
        }?.maxByOrNull { it.lastModified() }

        if (logFile != null && logFile.exists()) {
            try {
                logFile.writeText("")
            } catch (e: Exception) {
                Timber.e(e, "Error clearing log file")
            }
        }
        _allLogs.value = emptyList()
    }

    fun getCurrentLogFile(): File? {
        val externalDir = context.getExternalFilesDir(null) ?: return null
        val logDir = File(externalDir, "logs")
        if (!logDir.exists()) return null
        return logDir.listFiles { file ->
            file.name.startsWith("app_logs_") && file.name.endsWith(".txt")
        }?.maxByOrNull { it.lastModified() }
    }

    fun shareLogFile(logFile: File) {
        if (!logFile.exists()) {
            Timber.e("No log file available for sharing")
            return
        }

        coroutineScopes.io.launch {
            try {
                val fileUri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    logFile
                )
                val shareIntent = Intent(Intent.ACTION_SEND).apply {
                    type = "text/plain"
                    putExtra(Intent.EXTRA_STREAM, fileUri)
                    putExtra(Intent.EXTRA_SUBJECT, "Log File")
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
                // Create a chooser intent and ensure it has the NEW_TASK flag.
                val chooserIntent = Intent.createChooser(shareIntent, "Share logs").apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                withContext(Dispatchers.Main) {
                    context.startActivity(chooserIntent)
                }
            } catch (e: Exception) {
                Timber.e(e, "Error sharing log file")
            }
        }
    }
}
