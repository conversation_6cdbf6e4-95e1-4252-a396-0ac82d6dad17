package com.gametaco.app_android_fd.data

import com.fanduel.coremodules.config.contract.AppDomain
import com.fanduel.coremodules.config.contract.Environment

object EnvConfigDevelopment {

    fun EnvConfig(
        env: ENV,// = ENV.DEV,
        environmentSpec: EnvironmentSpec,
        fanduel_account_url: String = "",
        fanduel_environment: Environment = Environment.DevStack(environmentSpec.environmentName),
    ) = EnvConfig(
        env = env,
        envHost = environmentSpec,
        api_endpoint = environmentSpec.apiEndpointUrl,
        api_app_id = AppConstants.API_APP_ID,
        app_version = AppConstants.API_APP_VERSION,
        braze_api_endpoint = AppConstants.BRAZE_API_ENDPOINT_DEV,
        braze_api_key = AppConstants.BRAZE_API_KEY_DEV,
        sift_account_id = AppConstants.SIFT_ACCOUNT_ID_DEV,
        sift_beacon_key = AppConstants.SIFT_BEACON_KEY_DEV,
        amplitude_api_key = AppConstants.AMPLITUDE_API_KEY_DEV,
        amplitude_app_id = AppConstants.AMPLITUDE_APP_ID_DEV,
        amplitude_experiments_key = AppConstants.AMPLITUDE_EXPERIMENTS_KEY_DEV,
        fanduel_account_url = fanduel_account_url,
        firebase_cloud_messaging_sender_id = AppConstants.FIREBASE_CLOUD_MESSAGING_SENDER_ID,
        fanduel_environment = fanduel_environment,
        fanduel_appdomain = AppDomain.SkilledGames("us"),
        maintenanceURL = environmentSpec.maintenanceUrl,
        fanduel_salesforce_orgId = "00D8G000000sbQL",
        fanduel_salesforce_deploymentId = "5725Y000000QSWF",
        fanduel_salesforce_buttonId = "5735Y000000QSsW",
        fanduel_salesforce_liveAgentPod = "d.la1-c1cs-ia6.salesforceliveagent.com",
    )

    fun EnvConfigDev(
        environmentSpec: EnvironmentSpec,
        fanduel_account_url: String = "",
        fanduel_environment: Environment = Environment.DevStack(environmentSpec.environmentName),
    ): EnvConfig {
        return EnvConfig(
            env = ENV.DEV,
            environmentSpec = environmentSpec,
            fanduel_account_url = fanduel_account_url,
            fanduel_environment = fanduel_environment,
        )
    }

    fun EnvConfigQa(
        environmentSpec: EnvironmentSpec,
        fanduel_account_url: String = "",
        fanduel_environment: Environment = Environment.DevStack(environmentSpec.environmentName),
    ): EnvConfig {
        return EnvConfig(
            env = ENV.QA,
            environmentSpec = environmentSpec,
            fanduel_account_url = fanduel_account_url,
            fanduel_environment = fanduel_environment,
        )
    }
}