package com.gametaco.app_android_fd.data

import android.app.Application
import com.gametaco.app_android_fd.utils.log.DiskLoggingTree
import timber.log.Timber
import com.gametaco.app_android_fd.BuildConfig

object LoggerInitializerImpl : LoggerInitializer {

    override val loggingEnabled: <PERSON>olean
        get() = BuildConfig.DEBUG

    override fun init(application: Application) {
        if (loggingEnabled) {
            Timber.plant(Timber.DebugTree(), DiskLoggingTree(application))
        }
    }
}