package com.gametaco.app_android_fd.ui.screens

import PullRefreshIndicator
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.gametaco.app_android_fd.LocalNavManager
import com.gametaco.app_android_fd.LocalUIManager
import com.gametaco.app_android_fd.data.AppConstants
import com.gametaco.app_android_fd.data.entity.APITournament
import com.gametaco.app_android_fd.data.entity.APITournamentState
import com.gametaco.app_android_fd.data.navigation.NavigationData
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.BrazeEventName
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.SystemBarState
import com.gametaco.app_android_fd.manager.WalletManager
import com.gametaco.app_android_fd.ui.components.BonusCashBanner
import com.gametaco.app_android_fd.ui.components.ContentBox
import com.gametaco.app_android_fd.ui.components.ContestDetail
import com.gametaco.app_android_fd.ui.components.ContestGroup
import com.gametaco.app_android_fd.ui.components.ContestList
import com.gametaco.app_android_fd.ui.components.ContestListItem
import com.gametaco.app_android_fd.ui.components.GamePreview
import com.gametaco.app_android_fd.ui.components.GuestBlockComponent
import com.gametaco.app_android_fd.ui.components.NavBar
import com.gametaco.app_android_fd.ui.components.TopBar
import com.gametaco.app_android_fd.ui.components.getTournamentState
import com.gametaco.app_android_fd.ui.modifiers.onAppear
import com.gametaco.app_android_fd.ui.modifiers.onDisappear
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.viewmodel.LoginViewModel
import com.gametaco.app_android_fd.viewmodel.PlayViewModel
import com.gametaco.utilities.ResourceState
import com.gametaco.utilities.STR
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import pullRefresh
import rememberPullRefreshState
import resources.R
import toDp

const val TAG_PLAY_SCREEN = "PlayScreen"

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun PlayScreen(
    playViewModel: PlayViewModel
) {
    val TAG = TAG_PLAY_SCREEN

    val navManager = LocalNavManager.current
    val uiManager = LocalUIManager.current

    LaunchedEffect(Unit) {
        uiManager.setSystemBarState(SystemBarState.TransparentDarkStatusBarIcons)
    }

    val bottomModalExpanded = remember { MutableStateFlow(false) }
    val showPreview = MutableStateFlow(false)
//    BackHandler(bottomModalExpanded) {
//        bottomModalExpanded = false
//    }
    val tournamentsResponse by playViewModel.contestData.collectAsState()
    val gameData by playViewModel.gameData.collectAsState()
    val gameResponse by playViewModel.game.collectAsState()
    val wallet by WalletManager.instance.wallet.collectAsState()
    val forceShowPreview by playViewModel.forceShowPreview.collectAsState()
    val experimentsReady by ExperimentManager.instance.experimentsReady.collectAsState()

    var isTurboActive = true
//    if(experimentsReady && !AuthenticationManager.instance.isGuest){
//        isTurboActive = ExperimentManager.instance.turbosMode?.isAvailable() == false || ExperimentManager.instance.turbosMode?.isActive() == true
////        println("isTurboActive:"+isTurboActive)
//    }

    var activeTournament: APITournament? by remember{ mutableStateOf(null) }
    var isHeadToHead by remember { mutableStateOf(false) }

    val gameName = gameResponse?.display_name ?: "Contest"
    val gameDesc = gameResponse?.short_description ?: "Description"
    val gameId = gameResponse?.id

    var topColor = MaterialTheme.colorScheme.background
    var bottomColor = MaterialTheme.colorScheme.background

    if (gameData != null) {
        topColor = Color(android.graphics.Color.parseColor(gameData?.gradient_top_color))
        bottomColor = Color(android.graphics.Color.parseColor(gameData?.gradient_bottom_color))
    }
    val backgroundBrush = Brush.verticalGradient(listOf(topColor, bottomColor))

    when (tournamentsResponse){
        is ResourceState.Error -> {
            NavManager.instance.setWasOnContestScreen(false)
            NavManager.instance.navigate(Routes.GAMES_SCREEN)
        }
        is ResourceState.Loading -> {
            Logger.d(TAG, "Inside Loading")
            LoadingScreen()
        }
        is ResourceState.Success -> {
            val contestGroups: MutableList<Pair<String,@Composable ()->Unit>> = mutableListOf()
            val contestGroupTabs: MutableList<Pair<String,@Composable ()->Unit>> = mutableListOf()
            val contentState = rememberLazyListState()
            val coroutineScope = rememberCoroutineScope()
            var menuClicked by remember { mutableStateOf(false) }
            val density = LocalDensity.current
            val menuHeight = 62.dp

            fun scrollToContent(index:Int,animate:Boolean = true){
                val offset = with(density){
                    menuHeight.toPx() + 8.dp.toPx()
                }
                coroutineScope.launch {
                    println("goto:" + index)
                    contentState.animateScrollToItem(5 + index,-offset.toInt())
                }
            }
            if(AuthenticationManager.instance.isGuest) {
                val responseData = (tournamentsResponse as ResourceState.Success).data
                val allTournaments = responseData.results
                playViewModel.processTournamentData(allTournaments)
                val gameModes = playViewModel.getGameModes()


                for (gameMode in gameModes){
                    val groupTitle = gameMode.name
                    val groupSubtitle = gameMode.description
                    val tournamentsForGameMode =
                        playViewModel.getTournamentsByGameModeId(gameMode.id) ?: continue

                    val brandGroups: MutableMap<String, MutableList<APITournament>> = mutableMapOf()
                    for (tournament in tournamentsForGameMode) {
                        if (tournament.brand.isNullOrEmpty()) {
                            continue // Ignore tournaments without brand
                        }

                        if (!brandGroups.containsKey(tournament.brand)) {
                            brandGroups[tournament.brand?: ""] = mutableListOf()
                        }

                        brandGroups[tournament.brand]?.add(tournament)
                    }

                    val contestLists: MutableList<@Composable () -> Unit> = mutableListOf()
                    for (brandGroup in brandGroups) {
                        val tournamentListItems: MutableList<@Composable () -> Unit> = mutableListOf()
                        for (tournament in brandGroup.value) {
                            tournamentListItems.add {
                                ContestListItem(
                                    tournament = tournament,
                                    wallet = wallet,
                                    onInfoButton = {
                                        activeTournament = tournament
                                        isHeadToHead = false
                                        bottomModalExpanded.value = true
                                    },
                                    gameId = gameId
                                )
                            }
                        }

                        if (brandGroup.value.isNotEmpty()){
                            contestLists.add {
                                Column {
                                    for (index in tournamentListItems.indices){
                                        HorizontalDivider(
                                            modifier = Modifier.padding(start = 9.dp)
                                        )

                                        tournamentListItems[index]()
                                    }
                                }
//                                ContestList(
//                                    title = brandGroup.value[0].brand?:"",
//                                    subtitle = brandGroup.value[0].brand_description?:"",
//                                    tournamentListItems,
//                                    false,
//                                )
                            }
                        }
                        contestGroups.add(Pair(brandGroup.value[0].brand?:"Guest Mode",{
                            ContestGroup(
                                title = brandGroup.value[0].brand?:"Guest Mode",
                                subtitle = brandGroup.value[0].brand_description?:"Test your skills for free before you sign up.",
                                contestLists = contestLists
                            )
                        }))
                    }

//                    contestGroups.add{
//                        ContestGroup(
//                            title = groupTitle?:"Guest Mode",
//                            subtitle = groupSubtitle?:"Test your skills for free before you sign up.",
//                            contestLists = contestLists
//                        )
//                    }
                }

                //Guest Account Standard Game Group
                val standardContestLists: MutableList<@Composable () -> Unit> = mutableListOf()
                val imageAsContestList: MutableList<@Composable () -> Unit> = mutableListOf() // List using image
                var size by remember {
                    mutableStateOf(IntSize(0,0))
                }
                imageAsContestList.add {
                    Box(modifier = Modifier.onSizeChanged {
                        size = it
                    })
                    {
                        Image(
                            painter = painterResource(id = R.drawable.img_guest_contest),
                            contentDescription = null,
                            contentScale = ContentScale.Fit,
                            modifier = Modifier.fillMaxWidth()
                        )
                        Box(Modifier.height(size.toDp().height))
                        {
                            GuestBlockComponent(
                                title = "Join now to unlock all contests",
                                topText = "Play against another person of similar skill level - winner take all",
                                desc = "Once you join, you can <b>play for cash</b>, try all game modes, and join tournaments for large prizes.",
                                label = "Join now and unlock all contests",
                                removeFade = false,
                                onClick = { LoginViewModel.instance.fanduelSignupFromGuestMode() },
                            )
                        }
                    }
                }

                standardContestLists.add{

                    ContestList(
                        title = "Head to Head",
                        subtitle = null,
                        listItems = imageAsContestList,
                        showGuestBlock = false)
                }

                contestGroups.add(Pair("Standard",{
                    ContestGroup(
                        title = "Standard",
                        subtitle = "Play with standard rules and scoring.",
                        contestLists = standardContestLists
                    )
                }))
            }
            else{
                val groups by playViewModel.groups.collectAsState()
                val currentGroup by playViewModel.currentGroup.collectAsState()
                val tournamentIdFromDeeplink by PlayViewModel.instance.tournamentIdFromDeeplink.collectAsState()

                groups.forEachIndexed{index,group ->
                    var showedNoFounds = false
                    val tournamentBrandItems: MutableMap<String,MutableList<@Composable () -> Unit>> = mutableMapOf()
                    val tournamentBrandNoFunds: MutableMap<String,Boolean> = mutableMapOf()

                    // we don't need to sort by entry_fee, now backend handles the orders
//                    val tournamentsSorted = group.tournaments.sortedBy { (it.entry_fee?:"0.00").toDouble() }
                    val tournamentsSorted = group.tournaments
                    tournamentsSorted.forEachIndexed { index, tournament ->
                        val available = (tournament.is_turbo != true || (tournament.is_turbo == true && isTurboActive))
                                && tournament.showInTournamentList
                        if(available){
                            val brandLabel = tournament.brandLabel
                            val noFunds = (getTournamentState(tournament,wallet) == APITournamentState.fundsIneligibility)
                                    && (wallet.balance.toDouble() == 0.0) && (wallet.bonus_balance.toDouble() == 0.0)

                            val showNoFounds = noFunds && !showedNoFounds
                            if(showNoFounds){
                                showedNoFounds = true
                            }

                            val brandList = tournamentBrandItems.getOrDefault(brandLabel, mutableListOf())
                            if(!tournamentBrandItems.containsKey(brandLabel)){
                                tournamentBrandItems.put(brandLabel,brandList)
                                tournamentBrandNoFunds.put(brandLabel,noFunds)
                            }
                            if(tournament.free_tickets?.isNotEmpty() == true){
                                tournament.free_tickets?.forEach{
                                    val tournamentCopy = tournament.copy(free_entry_ticket_expires_at = it.expires_at, discountPercentage = it.discount_percent)
                                    brandList.add {
                                        ContestListItem(
                                            tournament = tournamentCopy,
                                            wallet = wallet,
                                            onInfoButton = {
                                                activeTournament = tournamentCopy
                                                isHeadToHead = false
                                                bottomModalExpanded.value = true
                                            },
                                            gameId = gameId,
                                            showNoFounds = showNoFounds,  //only show for the first one
                                        )
                                    }
                                }
                            }else {
                                brandList.add {
                                    ContestListItem(
                                        tournament = tournament,
                                        wallet = wallet,
                                        onInfoButton = {
                                            activeTournament = tournament
                                            isHeadToHead = false
                                            bottomModalExpanded.value = true
                                        },
                                        gameId = gameId,
                                        showNoFounds = showNoFounds,  //only show for the first one
                                    )
                                }
                            }
                        }
                    }


                    val icon = PlayViewModel.instance.getModeIcon(group.group_type,group.name,gameName,group.isSurvivor)
                    if(tournamentBrandItems.size > 0){
                        val contestLists:MutableList<@Composable () -> Unit> = mutableListOf()
                        tournamentBrandItems.forEach { brandLabel, items ->
                            contestLists.add {
                                ContestList(title = brandLabel, listItems = items , noFunds = tournamentBrandNoFunds.getOrDefault(brandLabel,false))
                            }
                        }

                        contestGroups.add(Pair(group.groupKey,{
                            ContestGroup(
                                title = group.name,
                                subtitle = group.description ?: "",
                                icon = icon,
                                isSurvivor = group.isSurvivor,
                                survivorEndTime = group.survivorEndTime,
                                contestLists = contestLists
                            )
                        }))
                    }
                    val selected = currentGroup == group.groupKey
                    if(selected && tournamentIdFromDeeplink != null){
//                        println("currentGroup:${currentGroup}")
                        scrollToContent(index)
                    }
                    contestGroupTabs.add(Pair(group.groupKey,{
                        Button(
                            shape = RoundedCornerShape(28.dp),
                            contentPadding = PaddingValues(12.dp),
                            colors = ButtonDefaults.buttonColors(containerColor = if(selected)Color.White else Color.Transparent, contentColor = if(selected)Color.Black else Color.White),
                            onClick = {
                                menuClicked = true
                                playViewModel.changeCurrentGroup(group.groupKey)
                                scrollToContent(index)
                            }){
                            Text(group.name,
                                textAlign = TextAlign.Center,
                                color = if(selected)Color.Black else Color.White,
                                style = if(selected)MaterialTheme.typography.bodyMedium else MaterialTheme.typography.bodySmall)
                        }
                    }))
                }
            }


            val refreshScope = rememberCoroutineScope()
            var refreshing by remember { mutableStateOf(false) }

            fun refresh() = refreshScope.launch {
                refreshing = true
                playViewModel.refreshData()
                refreshing = false
            }
            val state = rememberPullRefreshState(refreshing, ::refresh)

            var lastGroupHeightPx by remember { mutableStateOf(0) }
            var rootColumnHeightPx by remember { mutableStateOf(0) }

            AppandroidwwTheme {
                Scaffold(
                    topBar = {
//                        TopBarLogo()
                        TopBar(title = "$gameName ${STR(R.string.contests)}"){
                            navManager.setWasOnContestScreen(false)
                            navManager.navigate(Routes.GAMES_SCREEN)
                        }
                    },
                    bottomBar = { NavBar(NavigationData.instance, navManager) },
                    modifier = Modifier
                        .onAppear {
                            if (forceShowPreview || !PreferencesManager.instance.isGameSeen(
                                    gameData?.game_id ?: "null"
                                )
                            ) {
                                showPreview.value = true
                                if (!forceShowPreview) {
                                    PreferencesManager.instance.addGameToSeen(
                                        gameData?.game_id ?: "null"
                                    )
                                }
                            }
                            refresh()
                            BrazeManager.instance.logEvent(BrazeEventName.User_Visited_Tournament_Page_Of_Game_.value + gameData?.game_id)
                        }
                        .onDisappear {
                            playViewModel.changeCurrentGroup(null)
                            showPreview.value = false
                            menuClicked = false
                            playViewModel.setForceShowPreview(false)
                        }
                ) { paddingValues ->
                    BoxWithConstraints {
                        var tabletPadding = PaddingValues()
                        val boxWithConstraintsScope = this
                        val maxWidth = boxWithConstraintsScope.maxWidth
                        if (maxWidth > AppConstants.VIEW_MAX_WIDTH.dp) {
                            tabletPadding = PaddingValues(
                                horizontal = (maxWidth - AppConstants.VIEW_MAX_WIDTH.dp) / 2f
                            )
                        }

                        Surface(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(paddingValues)
                                .pullRefresh(state = state)
                                .background(MaterialTheme.colorScheme.background)
                                .padding(tabletPadding)
                        ) {
                            Box(
                                modifier = Modifier
                                    .background(topColor)
//                                    .padding(top = 240.dp)
//                                    .background(topColor)
                            )
                            Box(
                                modifier = Modifier
                                    .pullRefresh(state = state)
                                    .fillMaxSize()
                                    .onGloballyPositioned { coordinates ->
                                        rootColumnHeightPx = coordinates.size.height
                                    }
                            ) {
                                LazyColumn(
                                    modifier = Modifier
                                        .fillMaxHeight()
                                        .nestedScroll(object : NestedScrollConnection {
                                            override fun onPreScroll(
                                                available: Offset,
                                                source: NestedScrollSource
                                            ): Offset {
//                                                println("onDragStart")
                                                menuClicked = false
                                                playViewModel.clearTournamentIdFromDeeplink()
                                                return Offset.Zero
                                            }
                                        }),
                                    state = contentState
                                ) {
                                    item{
                                        if (wallet.bonus_balance.toDouble() > 0) {
                                            Column {
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .fillMaxWidth(),
                                                    thickness = 1.dp,
                                                    color = Color(0xFFB0B7BF)
                                                )
                                                ContentBox(
                                                    titleComposable = {
                                                        BonusCashBanner(value = wallet.bonus_balance)
                                                    },
                                                    cornerRoundness = 0.dp,
                                                    boarderSize = 0.dp
                                                )
                                            }
                                        }
                                    }
                                    item {
                                        Box(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .wrapContentHeight()
                                        ) {
                                            //Top Box
                                            AsyncImage(
                                                model = gameData?.hero_image_url,
                                                contentDescription = "Game Banner Image",
                                                contentScale = ContentScale.FillWidth,
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .align(Alignment.TopCenter)
//                                                    .height(240.dp),
                                            )
                                            Icon(
                                                painter = painterResource(R.drawable.ic_info),
                                                contentDescription = "Game Info Button",
                                                tint = MaterialTheme.colorScheme.onPrimary,
                                                modifier = Modifier
                                                    .align(Alignment.TopEnd)
                                                    .padding(top = 8.dp, end = 8.dp)
                                                    .size(15.dp)
                                                    .clickable {
                                                        showPreview.value = true
                                                    }
                                            )
                                        }
                                    }
                                    item{
                                        Text(
                                            text = gameDesc,
                                            textAlign = TextAlign.Center,
                                            lineHeight = 18.sp,
                                            style = MaterialTheme.typography.titleMedium.copy(fontSize = 14.sp, fontWeight = FontWeight.W600),
                                            color = MaterialTheme.colorScheme.onPrimary,
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .align(Alignment.BottomCenter)
                                                .padding(
                                                    start = 30.dp,
                                                    end = 30.dp,
                                                    top = 16.dp,
                                                    bottom = 16.dp
                                                )
                                        )
                                    }

                                    stickyHeader("menu"){
                                        val menuState = rememberLazyListState()
                                        val menuOffset = with(density){
                                             8.dp.toPx()
                                        }
                                        coroutineScope.launch {
                                            snapshotFlow { contentState.layoutInfo.visibleItemsInfo }
                                                .collect { visibleItems ->
                                                    run {
                                                        if(!menuClicked && playViewModel.tournamentIdFromDeeplink.value == null){
                                                            val menuItem = visibleItems.first() {
                                                                it.key == "menu"
                                                            }
                                                            visibleItems.forEach {visibleItem->
//                                                                println("looking at key: ${visibleItem.key}")
                                                                val item = contestGroupTabs.firstOrNull {
                                                                    it.first == visibleItem.key && (visibleItem.offset + visibleItem.size >= (menuItem.offset + menuItem.size + menuOffset))
                                                                }
                                                                if(item != null){
//                                                                    println("found item: ${item.first}")
                                                                    playViewModel.changeCurrentGroup(item.first)
                                                                    menuState.animateScrollToItem(contestGroupTabs.indexOf(item))
                                                                    return@run
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                        }

                                        if(contestGroupTabs.size > 1){
                                            LazyRow(modifier = Modifier
                                                .fillMaxWidth()
                                                .height(menuHeight)
                                                .background(topColor)
                                                .background(Color(0f, 0f, 0f, 0.25f)),
                                                state = menuState,
//                                            .padding(start = 8.dp,end = 8.dp),
                                                horizontalArrangement = Arrangement.spacedBy(
                                                    4.dp,
                                                    Alignment.Start
                                                ),
                                                verticalAlignment = Alignment.CenterVertically) {
                                                item{
                                                    Spacer(modifier = Modifier.width(8.dp))
                                                }

                                                for(tab in contestGroupTabs){
                                                    item(key = tab.first) {
                                                        tab.second()
                                                    }
                                                }
                                                item{
                                                    Spacer(modifier = Modifier.width(8.dp))
                                                }
                                            }
                                        }
                                    }
                                    item{
                                        Spacer(modifier = Modifier.height(8.dp))
                                    }

                                    for ((index, group) in contestGroups.withIndex()) {
                                        val isLastGroup = index == contestGroups.lastIndex
                                        item(key = group.first) {
                                            Box(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .padding(16.dp, 8.dp)
                                                    .then(
                                                        if (isLastGroup) {
                                                            Modifier.onGloballyPositioned { coordinates ->
                                                                lastGroupHeightPx =
                                                                    coordinates.size.height
                                                            }
                                                        } else {
                                                            Modifier
                                                        }
                                                    )
                                            ) {
                                                group.second()
                                            }
                                        }
                                    }

                                    item {
                                        val containerHeightDp = with(LocalDensity.current) { rootColumnHeightPx.toDp() }
                                        val lastGroupHeightDp = with(LocalDensity.current) { lastGroupHeightPx.toDp() }
                                        val spacerHeight = with(LocalDensity.current) {
                                            // The -24.dp is a magic number to perfectly size
                                            (containerHeightDp - lastGroupHeightDp - menuHeight - 22.dp).coerceAtLeast(0.dp)
                                        }

                                        Spacer(modifier = Modifier.height(spacerHeight))
                                    }
                                }
                                PullRefreshIndicator(
                                    modifier = Modifier.align(alignment = Alignment.TopCenter),
                                    refreshing = refreshing,
                                    state = state,
                                )
                            }
                        }
                    }
                }
            }
            ContestDetail(bottomModalExpanded,activeTournament,wallet, AuthenticationManager.instance.isGuest, gameId = gameId)
            GamePreview(showPreview,gameResponse,AuthenticationManager.instance.showLobbyFtue, AuthenticationManager.instance.isGuest)
        }
    }
}

@Preview
@Composable
fun PlayScreenPreview(){
    PreviewRoot {
        PlayScreen(playViewModel = PlayViewModel.instance)
    }
}
