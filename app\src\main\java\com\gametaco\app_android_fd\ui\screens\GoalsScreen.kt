package com.gametaco.app_android_fd.ui.screens

import PullRefreshIndicator
import androidx.activity.compose.BackHandler
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.absoluteOffset
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.graphics.ClipOp
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.BuildConfig
import com.gametaco.app_android_fd.LocalNavManager
import com.gametaco.app_android_fd.LocalUIManager
import com.gametaco.app_android_fd.data.AppConstants
import com.gametaco.app_android_fd.data.entity.APIActiveGoal
import com.gametaco.app_android_fd.data.entity.APICompletableGoal
import com.gametaco.app_android_fd.data.entity.APIGoalDetail
import com.gametaco.app_android_fd.data.entity.APIGoalRewardType
import com.gametaco.app_android_fd.data.entity.APIGoalType
import com.gametaco.app_android_fd.data.entity.APIGoalsTask
import com.gametaco.app_android_fd.data.entity.DailyRewardInfoType
import com.gametaco.app_android_fd.data.navigation.NavigationData
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.FDWebviewPage
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.SystemBarState
import com.gametaco.app_android_fd.manager.WalletManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.ui.components.ArrowMode
import com.gametaco.app_android_fd.ui.components.BottomSheetModal
import com.gametaco.app_android_fd.ui.components.ClaimedActiveReward
import com.gametaco.app_android_fd.ui.components.ClaimedRewardElement
import com.gametaco.app_android_fd.ui.components.ClusterRewardElement
import com.gametaco.app_android_fd.ui.components.ClusterRewardsBox
import com.gametaco.app_android_fd.ui.components.ContentBox
import com.gametaco.app_android_fd.ui.components.ContentListBody
import com.gametaco.app_android_fd.ui.components.ContentTitle
import com.gametaco.app_android_fd.ui.components.DailyRewardsDisplay
import com.gametaco.app_android_fd.ui.components.FirstDepositTile
import com.gametaco.app_android_fd.ui.components.GetRewardTypeString
import com.gametaco.app_android_fd.ui.components.NavBar
import com.gametaco.app_android_fd.ui.components.RewardsBonusCashDisplay
import com.gametaco.app_android_fd.ui.components.RewardsInfoBody
import com.gametaco.app_android_fd.ui.components.RewardsInfoTitle
import com.gametaco.app_android_fd.ui.components.RewardsProgressBar
import com.gametaco.app_android_fd.ui.components.RewardsStepList
import com.gametaco.app_android_fd.ui.components.Tooltip
import com.gametaco.app_android_fd.ui.components.TopBar
import com.gametaco.app_android_fd.ui.components.getActiveRewardElement
import com.gametaco.app_android_fd.ui.components.getClusterIcon
import com.gametaco.app_android_fd.ui.components.getValueAsCurrency
import com.gametaco.app_android_fd.ui.modifiers.onAppear
import com.gametaco.app_android_fd.ui.modifiers.onDisappear
import com.gametaco.app_android_fd.utils.formatLocal
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.app_android_fd.utils.toDollarWithCommaString
import com.gametaco.app_android_fd.viewmodel.GoalsViewModel
import com.gametaco.app_android_fd.viewmodel.LoginViewModel
import com.gametaco.utilities.ResourceState
import com.gametaco.utilities.STR
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import pullRefresh
import rememberPullRefreshState
import resources.R
import java.time.LocalDateTime
import java.util.Date

const val TAG_GOALS_SCREEN = "GoalsScreen"

@Composable
fun GoalsScreen(
    goalsViewModel: GoalsViewModel
){
    val TAG = TAG_GOALS_SCREEN;
    val uiManager = LocalUIManager.current
    LaunchedEffect(Unit) {
        uiManager.setSystemBarState(SystemBarState.TransparentDarkStatusBarIcons)
    }

    val navManager = LocalNavManager.current
    val goalsResponse by goalsViewModel.goals.collectAsState()
    val collectableCount by goalsViewModel.collectableCount.collectAsState()
    val lifetimeRewardsTotal by goalsViewModel.lifetimeTotal.collectAsState()
    val claimedActiveRewards = remember { mutableStateListOf<APIActiveGoal>() }
    val scope = rememberCoroutineScope()
    val navData = NavigationData.instance
    val goalInfoModalExpanded = remember { mutableStateOf(false) }
    var animateLifetimeRewardsTotal by remember { mutableStateOf(false) }
    val wallet by WalletManager.instance.wallet.collectAsState()
    val isDailyRewardEnabled by ExperimentManager.instance.isDailyRewardEnabled.collectAsState()
    val lifetimeRewardsValue = animateFloatAsState(
        targetValue = lifetimeRewardsTotal,
        animationSpec = tween(
            durationMillis = if(animateLifetimeRewardsTotal) 1000 else 0
        )
    )
    LaunchedEffect(Unit) {
        delay(1000)
        animateLifetimeRewardsTotal = true
    }

    val activeRewardInfo:MutableState<APIActiveGoal?> = remember { mutableStateOf(null) }
    val activeClusterRewardTotalTitle:MutableState<String?> = remember { mutableStateOf(null) }
    var ftueVisible by remember {
        mutableStateOf(
            value = AuthenticationManager.instance.showRewardsFtue &&
                    !AuthenticationManager.instance.isGuest
        )
    }
    var ftueItemPosition: Offset? by remember { mutableStateOf(null) }
    var ftueItemSize: IntSize? by remember { mutableStateOf(null) }
    val ftueOnClick = {
        AuthenticationManager.instance.showRewardsFtue = false
        ftueVisible = false
    }

    BackHandler(goalInfoModalExpanded.value) {
        goalInfoModalExpanded.value = false
    }
    val completableGoalsTitle = STR(R.string.active_rewards)
    var padding by remember { mutableStateOf(PaddingValues())}

    val refreshScope = rememberCoroutineScope()
    var refreshing by remember { mutableStateOf(false) }

    fun refresh() = refreshScope.launch {
        refreshing = true
        goalsViewModel.fetchGoals()
        refreshing = false
    }
    val state = rememberPullRefreshState(refreshing, ::refresh)
    val scrollState = rememberScrollState()
    Scaffold(
        topBar = { TopBar(title = STR(R.string.rewards)) },
        bottomBar = { NavBar(navData, navManager) }
    ) { paddingValues ->
        padding = paddingValues
        Surface(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth()
                .pullRefresh(state = state)
                .onAppear {

                    if (goalsViewModel.needRefresh()) {
                        goalsViewModel.fetchGoals()
                    }
                }
                .onDisappear {
                    animateLifetimeRewardsTotal = false
                    claimedActiveRewards.clear()
                    PreferencesManager.instance.setGoalsScreenLastSeen(Date())
                    NavigationData.instance.setNotificationCountGoals(collectableCount)
                }
/////////test code
//                .detectMultiTap {
//                    goalsViewModel.addLifeTimeReward(8.6f)
//                }
/////////test code
        ) {
            Box(modifier = Modifier.pullRefresh(state = state)) {
                if (AuthenticationManager.instance.isGuest) {
                    BoxWithConstraints {
                        var tabletPadding = PaddingValues()
                        val boxWithConstraintsScope = this
                        val maxWidth = boxWithConstraintsScope.maxWidth
                        if (maxWidth > AppConstants.VIEW_MAX_WIDTH.dp) {
                            tabletPadding =
                                PaddingValues(
                                    horizontal = (maxWidth - AppConstants.VIEW_MAX_WIDTH.dp) / 2f
                                )
                        }

                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp),
                            modifier = Modifier
                                .background(MaterialTheme.colorScheme.background)
                                .padding(paddingValues)
                                .padding(tabletPadding)
                                .padding(horizontal = 16.dp)
                                .verticalScroll(scrollState)
                        )
                        {
                            Spacer(modifier = Modifier.height(16.dp))
                            Column {
                                Text(
                                    text = STR(R.string.play_more_earn_more),
                                    fontWeight = FontWeight.Bold,
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.background(Color.Transparent)
                                )
                                Text(
                                    text = STR(R.string.complete_task_to_earn_bonus_cash),
                                    fontWeight = FontWeight.Normal,
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier
                                        .background(Color.Transparent)
                                        .padding(vertical = 8.dp)
                                )
                            }
                            ContentBox(
                                //AuthenticationManager.instance.isGuest would break the preview so add LocalInspectionMode.current to fix
                                bodyBlockedIfGuest = true,
                                guestBlockTitle = STR(R.string.join_now_to_unlock_rewards),
                                guestBlockLabel = STR(R.string.join_now_and_earn_rewards),
                                guestBlockDesc = STR(R.string.rewards_are_waiting_for_you_register),
                                guestBlockClick = {
                                    LoginViewModel.instance.fanduelSignupFromGuestMode()
                                },
                                titleComposable = {
                                    ContentTitle(
                                        icon = R.drawable.ic_rewards_app,
                                        title = completableGoalsTitle
                                    )
                                },
                                bodyComposable = {
                                    Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                                        Image(
                                            painter = painterResource(id = R.drawable.img_guest_goals),
                                            contentDescription = null,
                                            contentScale = ContentScale.Fit,
                                            modifier = Modifier.fillMaxWidth()
                                        )
//                                ContentListBody(listElements = fakeGoalList) //Guest Screen w/ Mock Data
                                    }
                                },
                                bodyPaddingValues = PaddingValues()
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                        }
                    }
                } else {
                    when (goalsResponse) {
                        is ResourceState.Loading -> {
                            Logger.d(TAG, "Inside Loading")
                            LoadingScreen()
                        }

                        is ResourceState.Success -> {
                            Logger.d(TAG, "Inside Success")
                            val pageTitle = STR(R.string.play_more_earn_more)
                            val pageSubtitle = STR(R.string.complete_task_to_earn_bonus_cash)

                            val lifetimeRewardsTitle = STR(R.string.lifetime_rewards_claimed)
                            val recentlyClaimedTitle = STR(R.string.recently_claimed_rewards)
                            val response = (goalsResponse as ResourceState.Success).data

                            val completableGoalDataList = response.completableGoals
                            val collectedGoalDataList = response.collectedGoals
                            val activeGoalDataList = response.activeGoals
                            val clusterRewardsDataMap: MutableMap<String, APIActiveGoal> = mutableMapOf()

//                        var completableDepositGoals: Int = completableGoalDataList.count { it.is_deposit_goal }
//                        Logger.d(TAG, "COMPLETABLE DEPOSIT GOALS: $completableDepositGoals")
//
//                        var collectedDepositGoals: Int = collectedGoalDataList.count { it.is_deposit_goal }
//                        Logger.d(TAG, "COLLECTED DEPOSIT GOALS: $collectedDepositGoals")
//
//                        var activeDepositGoals: Int = activeGoalDataList.count { it.is_deposit_goal }
//                        Logger.d(TAG, "ACTIVE DEPOSIT GOALS: $activeDepositGoals")

                            val depositGoal: APICompletableGoal? =
                                completableGoalDataList.firstOrNull { it.is_deposit_goal }

//                        println("depositGoal : ${depositGoal}")
                            //Logger.d(TAG, "activeGoalDataList:${activeGoalDataList.size}")
                            //Create Completable Goal Composables
                            val activeGoalElements: MutableList<@Composable () -> Unit> =
                                mutableListOf()
                            activeGoalDataList.forEachIndexed { goalsIndex, goalDataModel: APIActiveGoal ->
                                if (depositGoal == null || goalDataModel.is_deposit_goal != true) {
                                    if (goalDataModel.goalType == APIGoalType.Cluster) {
                                        clusterRewardsDataMap[goalDataModel._id] = goalDataModel
                                    }
                                    else {
                                        val modifier = if (goalsIndex == 0) {
                                            Modifier
                                                .onGloballyPositioned { coordinates ->
                                                    ftueItemPosition =
                                                        coordinates.boundsInWindow().topLeft
                                                    ftueItemSize = coordinates.size
                                                }
                                        } else {
                                            Modifier
                                        }

                                        //val isLast = goalsIndex == activeGoalDataList.size - 1
                                        activeGoalElements.add(
                                            getActiveRewardElement(
                                                goalDataModel = goalDataModel,
                                                modifier = modifier,
                                                onClaimClicked = { callback ->
                                                    AuthenticationManager.instance.showRewardsFtue =
                                                        false

                                                    if (goalDataModel.goalUserInstanceTaskId != null) {
                                                        goalsViewModel.collectGoal(
                                                            goalUserInstanceTaskId = goalDataModel.goalUserInstanceTaskId!!,
                                                            goalType = goalDataModel.goalType,
                                                            isDailyReward = goalDataModel.isDailyReward,
                                                            clusterTitle = goalDataModel.cluster_title,
                                                            rewardAmount = goalDataModel.rewardValue,
                                                            rewardTitle = goalDataModel.titleString,
                                                            callback = callback
                                                        )
                                                    }
                                                },
                                                onClaimComplete = { success ->
                                                    scope.launch {
                                                        claimedActiveRewards.add(goalDataModel)
                                                        ftueVisible = false

                                                        if (success) {
                                                            goalsViewModel.addLifeTimeReward(
                                                                goalDataModel.rewardValue
                                                            )
                                                        }
                                                        scrollState.animateScrollTo(
                                                            scrollState.maxValue,
                                                            animationSpec = tween(durationMillis = 1000)
                                                        )
                                                    }
                                                },
                                                onInfoClicked = {
                                                    activeRewardInfo.value = goalDataModel
                                                    goalInfoModalExpanded.value = true
                                                },
                                                //showDivider = !isLast
                                            )
                                        )
                                    }
                                }
                            }
                            if (!AuthenticationManager.instance.isGuest) {
                                if (depositGoal != null) {
                                    activeGoalElements.add(0) {
                                        FirstDepositTile(depositGoal,
                                            onDepositClick = {
                                                FDManager.instance.showWebviewFD(FDWebviewPage.Deposit)
                                            }, onClick = {
                                                activeRewardInfo.value = depositGoal
                                                goalInfoModalExpanded.value = true
                                            })
                                    }
                                }
                            }
                            if (activeGoalElements.isEmpty() || response.collectableGoals.isEmpty()) {
                                ftueVisible = false
                            }

                            val clusterRewardsElementsMap: MutableMap<String, Pair<Double, MutableList<@Composable () -> Unit>>> =
                                mutableMapOf()
                            clusterRewardsDataMap.forEach { goalDataPair: Map.Entry<String, APIActiveGoal> ->
                                val goalData = goalDataPair.value
                                val clusterRewardElements: MutableList<@Composable () -> Unit> = mutableListOf()
                                val clusterElementDataList = goalData.sortedTasks
                                var totalRewards = 0.0

                                clusterElementDataList.forEachIndexed { goalsIndex, taskData: APIGoalsTask ->
                                    totalRewards += taskData.reward?.toDouble() ?: 0.0

                                    //if the cluster task is claimable, then find the actual collectable goal data
                                    val collectableTaskGoal = response.getCollectableClusterGoalByTaskId(taskData.id)
                                    if(collectableTaskGoal != null){
                                        Logger.d("find a collectableTaskGoal:" + collectableTaskGoal)
                                    }
                                    
                                    val isLast = goalsIndex == clusterElementDataList.size - 1
                                    val claimed = taskData.isComplete && taskData.goal_user_instance_task_id == null
                                    val isExpired = taskData.ends_at?.toDate()?.before(Date()) == true
                                    if(!claimed && !isExpired){
                                        clusterRewardElements.add {
                                            ClusterRewardElement(
                                                taskData = taskData,
                                                goalDataModel = collectableTaskGoal ?: goalData,
                                                onClaimClicked = { callback ->
                                                    AuthenticationManager.instance.showRewardsFtue = false

                                                    if (taskData.goal_user_instance_task_id != null) {
                                                        goalsViewModel.collectGoal(
                                                            goalUserInstanceTaskId = taskData.goal_user_instance_task_id!!,
                                                            goalType = APIGoalType.Cluster,
                                                            isDailyReward = goalData.isDailyReward,
                                                            clusterTitle = goalData.cluster_title,
                                                            rewardAmount = taskData.reward?.toFloat()?:0f,
                                                            rewardTitle = taskData.title,
                                                            callback = callback
                                                        )
                                                    }
                                                },
                                                onClaimComplete = { success ->
                                                    scope.launch {
                                                        val goalData = response.getCollectableClusterGoalByTaskId(taskData.id)
                                                        if(goalData != null){
                                                            claimedActiveRewards.add(goalData)
                                                        }

                                                        ftueVisible = false

                                                        if (success && taskData.reward != null) {
                                                            goalsViewModel.addLifeTimeReward(
                                                                taskData.reward.toFloat()
                                                            )
                                                        }
                                                        scrollState.animateScrollTo(
                                                            scrollState.maxValue,
                                                            animationSpec = tween(durationMillis = 1000)
                                                        )
                                                    }
                                                },
                                                showDivider = !isLast
                                            )
                                        }
                                    }

                                }

                                clusterRewardsElementsMap[goalData._id] = Pair(totalRewards, clusterRewardElements)
                            }

                            //Create Collectable Goal Composables
                            val collectedGoalElements: MutableList<@Composable () -> Unit> =
                                mutableListOf()
                            claimedActiveRewards.forEachIndexed {index,it ->
                                val isLast = collectedGoalDataList.isEmpty() && index == (claimedActiveRewards.size - 1)

                                collectedGoalElements.add {
                                    if (it.eligibleToClaim){
                                        ClaimedActiveReward(
                                            goalDataModel = it,
                                            showDivider = !isLast
                                        )
                                    } else {
                                        val time = Date()
                                            .formatLocal("MM/dd/yyyy 'at' HH:mma z")
                                        ClaimedRewardElement(
                                            title = it.task_title ?:"",
                                            timeStamp = time
                                                .replace("AM", "am")
                                                .replace("PM", "pm"),
                                            value = "",
                                            eligible = it.eligibleToClaim,
                                            showDivider = !isLast
                                        )
                                    }
                                }
                            }

                            collectedGoalDataList.forEachIndexed { index, rewardDataModel ->
                                val isLast = index == collectedGoalDataList.size - 1
                                collectedGoalElements.add {
                                    val time = rewardDataModel.collected_at.toDate()
                                        ?.formatLocal("MM/dd/yyyy 'at' HH:mma z") ?: ""
                                    ClaimedRewardElement(
                                        title = rewardDataModel.task_title ?: "",
                                        timeStamp = time.replace("AM", "am").replace("PM", "pm"),
                                        value = if(rewardDataModel.reward_type == DailyRewardInfoType.CASH) rewardDataModel.reward else rewardDataModel.tournament?.entry_fee ?: rewardDataModel.reward,
                                        rewardType = rewardDataModel.reward_type,
                                        eligible = rewardDataModel.was_eligible_to_claim,
                                        showDivider = !isLast
                                    )
                                }
                            }

                            BoxWithConstraints {
                                var tabletPadding = PaddingValues()
                                val boxWithConstraintsScope = this
                                val maxWidth = boxWithConstraintsScope.maxWidth
                                if (maxWidth > AppConstants.VIEW_MAX_WIDTH.dp) {
                                    tabletPadding =
                                        PaddingValues(
                                            horizontal = (maxWidth - AppConstants.VIEW_MAX_WIDTH.dp) / 2f
                                        )
                                }

                                Box(modifier = Modifier.fillMaxSize()) {
                                    Column(
                                        modifier = Modifier
                                            .background(MaterialTheme.colorScheme.background)
                                            .fillMaxHeight()
                                            .padding(paddingValues)
                                            .padding(tabletPadding)
                                            .padding(horizontal = 16.dp)
                                            .verticalScroll(scrollState),
                                        verticalArrangement = Arrangement.spacedBy(12.dp)
                                    ) {
                                        Spacer(modifier = Modifier)
                                        Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
                                            Text(
                                                text = pageTitle,
                                                style = MaterialTheme.typography.bodyMedium,
                                            )
                                            Text(
                                                text = pageSubtitle,
                                                style = MaterialTheme.typography.bodySmall,
                                            )
                                        }

                                        if(isDailyRewardEnabled){
                                            DailyRewardsDisplay()
                                        }

                                        ContentTitle(
                                            icon = R.drawable.ic_rewards_app,
                                            title = completableGoalsTitle,
                                            style = MaterialTheme.typography.titleSmall
                                        )
                                        for (element in activeGoalElements){
                                            element()
                                        }

                                        for (clusterRewardEntry in clusterRewardsElementsMap) {
                                            val data = clusterRewardsDataMap[clusterRewardEntry.key]
                                            if (data?.cluster_title == null) continue

                                            val totalReward = clusterRewardEntry.value.first
                                            val listElements = clusterRewardEntry.value.second

                                            val valueTitle = STR(
                                                GetRewardTypeString(data.rewardType),
                                                "in"
                                            )

                                            val totalTitle = "Earn ${totalReward.toDollarWithCommaString()} ${valueTitle}!"

                                            Spacer(modifier = Modifier)
                                            ContentTitle(
                                                icon = getClusterIcon(data.cluster_icon),
                                                title = data.cluster_title!!,
                                                style = MaterialTheme.typography.titleSmall
                                            )
                                            ClusterRewardsBox(
                                                listElements = listElements,
                                                title = totalTitle,
                                                subTitle = data.titleString,
                                                onInfoClicked = {
                                                    activeRewardInfo.value = data
                                                    activeClusterRewardTotalTitle.value = totalTitle
                                                    goalInfoModalExpanded.value = true
                                                },
                                            )
                                        }

                                        Spacer(modifier = Modifier)
                                        ContentTitle(
                                            icon = R.drawable.ic_dollar_shield,
                                            iconColor = MaterialTheme.colorScheme.tertiary,
                                            title = lifetimeRewardsTitle,
                                            style = MaterialTheme.typography.titleSmall
                                        )
                                        RewardsBonusCashDisplay(
                                            getValueAsCurrency(lifetimeRewardsValue.value)
                                        )

                                        Spacer(modifier = Modifier)
                                        ContentTitle(
                                            icon = R.drawable.ic_recently_claimed_rewards,
                                            title = recentlyClaimedTitle,
                                            style = MaterialTheme.typography.titleSmall
                                        )
                                        if (collectedGoalElements.isEmpty()) {
                                            Text(
                                                text = STR(R.string.play_to_earn_bonus_cash),
                                                style = MaterialTheme.typography.bodySmall,
                                                modifier = Modifier.padding(16.dp, 0.dp, 16.dp, 12.dp)
                                            )
                                        } else {
                                            ContentBox(
                                                bodyPaddingValues = PaddingValues(0.dp)
                                            ) {
                                                ContentListBody(
                                                    listElements = collectedGoalElements,
                                                    precedingSpacerHeight = 0.dp,
                                                    elementSpacingHeight = 0.dp,
                                                    dividerPadding = PaddingValues(16.dp, 0.dp),
                                                    showDivider = false
                                                )
                                            }
                                        }
                                        Spacer(modifier = Modifier)

                                        // Version number
                                        Text(
                                            text = "Version ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})",
                                            style = MaterialTheme.typography.labelMedium,
                                            textAlign = TextAlign.Center,
                                            color = Color.Black,
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                        Spacer(modifier = Modifier.height(16.dp))
                                    }

                                    Ftue(
                                        ftueVisible,
                                        activeGoalElements,
                                        ftueItemPosition,
                                        ftueItemSize,
                                        activeGoalDataList,
                                        ftueOnClick,
                                    )
                                }
                            }
                        }

                        is ResourceState.Error -> {
                            Logger.d(TAG, "Inside Error")
                        }
                    }
                }
                PullRefreshIndicator(
                    modifier = Modifier.align(alignment = Alignment.TopCenter),
                    refreshing = refreshing,
                    state = state,
                )
            }
        }
    }


    // The mask over the top app bar and bottom bar
    if (ftueVisible) {
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            // Mask covering the top app bar
            val topBarHeight = padding.calculateTopPadding()
            FtueScrim(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(topBarHeight)
                    .align(Alignment.TopCenter),
                ftueOnClick,
            )

            // Mask covering the bottom bar
            val bottomBarHeight = padding.calculateBottomPadding()
            FtueScrim(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(bottomBarHeight)
                    .align(Alignment.BottomCenter),
                ftueOnClick,
            )
        }
    }

    GoalsInfoModal(goalInfoModalExpanded, activeRewardInfo, activeClusterRewardTotalTitle)
}

@Composable
fun GoalsInfoModal(
    goalInfoModalExpanded: MutableState<Boolean>,
    activeRewardInfo: MutableState<APIActiveGoal?>,
    activeClusterRewardTotalReward: MutableState<String?>
) {
    BottomSheetModal(
        title = STR(R.string.reward_info),
        showBackButton = false,
        showDivider = true,
        expanded = goalInfoModalExpanded.value,
        closeAction = {
            goalInfoModalExpanded.value = false;
            AnalyticsManager.instance.logEvent(
                AnalyticsEvent(
                    analyticsEvent = AnalyticsEventName.Modal_Closed.value,
                    properties = mapOf("name" to "Goals Bottom Sheet")
                )
            )
        },
        contentBackgroundColor = MaterialTheme.colorScheme.background,
    ) {
        val data = activeRewardInfo.value
        if (data == null) { // Close modal if data is null todo: Error popup? This shouldn't happen
            goalInfoModalExpanded.value = false
            return@BottomSheetModal
        }

        val isCluster = data.goalType == APIGoalType.Cluster

        val tasks = data.sortedTasks
        var currentTask: APIGoalsTask? = null
        var currentTaskIndex = 0
        for (index in tasks.indices) {
            val task = tasks[index]
            if (!task.isComplete || task == tasks.last()) {
                currentTask = task
                currentTaskIndex = index
                break
            }
        }

        val rewardType = when (data.rewardType) {
            APIGoalRewardType.faceoff -> R.string.bonus_cash
            APIGoalRewardType.sportsbook -> R.string.sports_book_bonus_bet
            APIGoalRewardType.freebetbonus -> R.string.sports_book_bonus_bet
            else -> R.string.bonus_cash
        }

        Column(
            verticalArrangement = Arrangement.spacedBy(16.dp, Alignment.Top),
            modifier = Modifier.padding(horizontal = 16.dp)
        ) {
            Spacer(modifier = Modifier)
            val title = if(data.has_ticket_reward == true){
                STR(R.string.free_game_entry)
            }else if (isCluster) {
                activeClusterRewardTotalReward.value ?: STR(rewardType, data.cashString)
            } else {
                STR(rewardType, data.cashString)
            }

            ContentBox(
                titleComposable = {
                    Box{
                        RewardsInfoTitle(
                            primaryTitle = title,
                            secondaryTitle = data.titleString,
                            subTitle = data.subTitleString,
                            isCluster = isCluster
                        )
//                        if(isCluster){
//                            Row(modifier = Modifier
//                                .align(Alignment.TopEnd)
//                                .offset(-14.dp,14.dp)){
//                                Image(
//                                    modifier = Modifier
//                                        .size(15.dp)
//                                        .offset(0.dp, 0.dp),
//                                    painter = painterResource(R.drawable.ic_clock),
//                                    contentDescription = null,
//                                    colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.onPrimary),
//                                    alignment = Alignment.Center
//                                )
//                                Text(
//                                    text = " ${data.ends_at?.toDate()?.getLeftTimeString() ?: "0S"} ",
//                                    textAlign = TextAlign.Center,
//                                    style = MaterialTheme.typography.labelSmall.copy(fontSize = 14.sp),
//                                    color = MaterialTheme.colorScheme.onPrimary,
//                                    modifier = Modifier.align(Alignment.CenterVertically)
//                                )
//                            }
//                        }
                    }

                },
                titlePaddingValues = PaddingValues()
            ) {
                var desc = data.descString
//                if(data.goalType == APIGoalType.Cluster){
//                    desc += "\n\nCluster reward ends on ${data.ends_at?.toDate()?.formatEST("MM/dd/yyyy 'at' hh:mm a")} EST."
//                }
                RewardsInfoBody(bodyText = desc)
            }

            if (tasks.size > 1 && currentTask != null) {
                ContentBox(
                    titleComposable = {
                        ContentTitle(
                            title = STR(R.string.progress),
                            style = MaterialTheme.typography.titleSmall
                        )
                    },
                    bodyPaddingValues = PaddingValues()
                ) {
                    RewardsStepList(
                        steps = tasks,
                        activeStepIndex = currentTaskIndex,
                        currentStepProgress = currentTask.current_progress?.toFloat()?.toInt()
                            ?: 0,
                        isCluster = isCluster
                    )
                }
            } else {
                ContentBox(
                    titleComposable = { ContentTitle(title = STR(R.string.progress), style = MaterialTheme.typography.titleSmall) },
                    bodyPaddingValues = PaddingValues(
                        start = 16.dp,
                        top = 12.dp,
                        end = 16.dp,
                        bottom = 16.dp
                    )
                ) {
                    RewardsProgressBar(
                        task = currentTask,
                        currentStepProgress = currentTask?.current_progress?.toFloat()?.toInt()
                            ?: 0,
                        currentStepMaxProgress = currentTask?.required_progress?.toFloat()?.toInt()
                            ?: 0,
                        spacerHeight = 5.dp,
                        ctaString = data.ctaString,
                        link = data.linkString,
                        gameId = data.gameId,
                        goalInfoModalExpanded = goalInfoModalExpanded
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))
        }
    }
}

@Composable
private fun Ftue(
    ftueVisible: Boolean,
    activeGoalElements: MutableList<@Composable () -> Unit>,
    ftueItemPosition: Offset?,
    ftueItemSize: IntSize?,
    activeGoalDataList: List<APIActiveGoal>,
    onClick:() -> Unit,
) {
    if (ftueVisible && activeGoalElements.isNotEmpty()) {
        val position = ftueItemPosition
        val size: IntSize? = ftueItemSize
        if (position != null && size != null) {
            val density = LocalDensity.current
            val widthInDp = with(density) { size.width.toDp() }
            val heightInDp = with(density) { size.height.toDp() }

//            Box(
//                modifier = Modifier
//                    .absoluteOffset { IntOffset(position.x.toInt(), position.y.toInt()) }
//                    .size(widthInDp, heightInDp)
//            )

            FtueScrimWithCutout(position, size, onClick)

            FtueTooltip(
                activeGoalDataList,
                ftueVisible,
                modifier = Modifier
                    .width(widthInDp - (16.dp.times(2)))
                    .absoluteOffset { IntOffset(position.x.toInt(), position.y.toInt()) }
                    .padding(start = 24.dp, top = heightInDp + 32.dp)
//                    .align(Alignment.TopCenter),
            )
        }
    }
}

@Composable fun FtueScrim(
    modifier: Modifier,
    onClick:() -> Unit,
) {
    Box(
        modifier = modifier
            .background(Color.Black.copy(alpha = 0.5f))
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) {
                AuthenticationManager.instance.showRewardsFtue = false
                onClick.invoke()
            }
    )
}

@Composable
fun FtueScrimWithCutout(
    position: Offset,
    size: IntSize,
    onClick: () -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .drawWithContent {
                drawContent()
                drawIntoCanvas { canvas ->
                    val path = Path().apply {
                        addRoundRect(
                            RoundRect(
                                left = position.x,
                                top = position.y,
                                right = position.x + size.width,
                                bottom = position.y + size.height,
                                radiusX = 0f,
                                radiusY = 0f
                            )
                        )
                    }
                    clipPath(path, clipOp = ClipOp.Difference) {
                        drawRoundRect(
                            color = Color.Black.copy(alpha = 0.5f),
                            topLeft = Offset.Zero,
                            size = this.size,
                            cornerRadius = CornerRadius.Zero
                        )
                    }
                }
            }
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) {
                AuthenticationManager.instance.showRewardsFtue = false
                onClick.invoke()
            }
    )
}

@Composable
fun FtueTooltip(
    activeGoalDataList: List<APIActiveGoal>,
    ftueVisible: Boolean,
    modifier: Modifier = Modifier,
) {
    Tooltip(
        modifier = modifier
            .defaultMinSize(minHeight = 80.dp),
        arrowHeight = 18.dp,
        enabled = ftueVisible,
        arrowMode = ArrowMode.Top,
        arrowPositionNormalised = 0.75f
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .padding(all = 6.dp)
        ) {
            val firstMessage = buildAnnotatedString {
                append("Reward achieved! You completed the tutorial and earned " + activeGoalDataList[0].cashString + " in Bonus Cash. Collect your reward by selecting ")
                withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)){
                    append("Claim")
                }
                append(".")
            }
            Text(
                text = firstMessage
            )
            Text(
                text = "When you complete more rewards, you'll earn even more rewards. Keep it up!",
            )
        }
    }
}

@Composable
fun exampleGoal(taskCount: Int = 0): APICompletableGoal {
    return APICompletableGoal(
        id = "Example",
        starts_at = LocalDateTime.now().toString(),
        ends_at = "1",
        goal = APIGoalDetail(
            title = STR(R.string._100_deposit_match_bonus_up_to_10),
            subtitle = "Example subtitle text that spans over two lines without ellipses.",
            description = STR(R.string.make_your_first_deposit_and_get_a_100_match_up_to_10_instantly),
            type = "standard",
            repeat_limit = 0,
            icon = "cashGameRewards",
            reward_type = "faceoff",
            cluster_title = null,
            cluster_icon = null
        ),
        tasks = emptyList(),
        is_deposit_goal = true,
        task_title = null

    )
}

@Preview
@Composable
fun GoalScreenPreview(){
    PreviewRoot {
        FirstDepositTile(exampleGoal(0), onDepositClick = { }, onClick = { })
    }
}
