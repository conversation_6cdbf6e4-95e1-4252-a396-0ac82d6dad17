package com.gametaco.app_android_fd.data.entity

import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.app_android_fd.utils.toNumberWithCommaString
import java.net.URL
import java.util.Date

data class APITournamentInstanceEntryList (
    val count: Int,
    var next: String?,
    var previous: String?,
    val results: List<APITournamentEntryResult>,
){
    fun nextCursor():String?{
        if(this.next == null) return null

        val url = try { URL(next) } catch (e:Exception) { null }

        url?.let {
            val query = it.query
            val queryPairs = query.split("&").associate {
                val index = it.indexOf("=")
                it.substring(0, index) to it.substring(index + 1)
            }
            return queryPairs["cursor"]
        }
        return null
    }
}

data class APITournamentEntryResultTicketInfo(
    val id:String,
    val discount_percent: Int? = null,
){
    val isFreeTicket: Boolean
        get() = discount_percent == null || discount_percent == 100
    val isDiscountedTicket: Boolean
        get() = discount_percent != null && discount_percent > 0 && discount_percent < 100
}
data class APITournamentEntryResult(
    val id: String,
    val created_at: String?,
    val tournament_instance_id: String,
    val started_at: String?,
    val entry_fee: String,
    val game_icon: String?,
    val game_id: String?,
    val maximum_slots: Int?,
    val is_winner: Boolean,
    val is_tie: Boolean,
    val tournament_instance_status: String,
    val tournament_instance_closed_at: String?,
    val status: String,
    val tournament_brand: String,
    val prizes: List<Prize>?,
    val game_display_name: String,
    val game_options: GameOptions?,
    val tournament_start_information: TournamentStartInformation?,
    val winnings: Winnings?,
    val tournament_description: String?,
    val game_mode_name: String?,
    val game_mode_description: String?,
    val game_mode_icon: String?,
    val is_legacy:Boolean? = false,
    val has_replay:Boolean?,
    val ticket_info:APITournamentEntryResultTicketInfo? = null,
){
    val entryFee: EntryFee by lazy { EntryFee(entry_fee) }

    // Calculate discounted entry fee when a discounted ticket is available
    val discountedEntryFee: String?
        get() {
            //Only proceed if there's a ticket and it's a discount ticket
            if(ticket_info == null || !ticket_info.isDiscountedTicket) return null

            val originalFee = entry_fee.toDoubleOrNull() ?: return null
            val discountPercent = ticket_info.discount_percent ?: return null
            val discountAmount = originalFee * (discountPercent / 100.0)
            val discountedFee = originalFee - discountAmount
            return discountedFee.toNumberWithCommaString()
        }

    fun displayDate():Date{
        return tournament_instance_closed_at?.toDate() ?: started_at?.toDate() ?: created_at?.toDate() ?: Date()
    }

}

data class Prize(
    val start: Int,
    val end: Int,
    val prize_amount: String
)

data class GameOptions(
    val join:Map<String,String>,
    val start:Map<String,String>?,
)

data class TournamentStartInformation(
    val tournament_id: String,
    val game_name: String,
    val game_options: GameOptions,
    val global_game_options : Map<String, Any>?
)

data class Winnings(
    val place: Int,
    val prize_amount: String
)


enum class APITournamentInstanceStatus(val rawValue: String){
    Open("OPEN"),
    Closing("CLOSING"),
    Closed("CLOSED"),
    Refunded("REFUNDED")
}

enum class APITournamentStatus(val rawValue:String){
    notStarted("NOT_STARTED"),
    completed("COMPLETED"),
    inProgress("IN_PROGRESS"),
    refunded("REFUNDED"),
    timedOut("TIMED_OUT"),
}


