package com.gametaco.app_android_fd.ui.components

import android.net.Uri
import android.os.Build
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.ViewModel
import coil.compose.AsyncImage
import coil.decode.GifDecoder
import coil.decode.ImageDecoderDecoder
import coil.request.ImageRequest
import com.braze.enums.CardType
import com.braze.jetpackcompose.contentcards.cards.BrazeCustomContentCard
import com.braze.jetpackcompose.contentcards.styling.ContentCardStyling
import com.braze.models.cards.CaptionedImageCard
import com.braze.models.cards.Card
import com.braze.models.cards.ImageOnlyCard
import com.braze.models.cards.ShortNewsCard
import com.gametaco.app_android_fd.manager.BrazeContentCardType
import com.gametaco.app_android_fd.manager.BrazeEventName
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.ui.modifiers.clickableWithoutRipple
import com.gametaco.app_android_fd.ui.modifiers.onAppear
import com.gametaco.app_android_fd.utils.log.Logger
import org.json.JSONObject
import resources.R


@Composable
fun BrazeCarousel(){
    val brazeCarouselViewModel = BrazeCarouselViewModel.instance
    val cards by BrazeManager.instance.cards.collectAsState()
    val filteredCards =  brazeCarouselViewModel.getIndexedContentCards(cards)

    //println("cards: ${filteredCards}")

    Box(modifier = Modifier
        .fillMaxSize()
        .padding(bottom = 2.dp)
        .onAppear {
            BrazeManager.instance.requestContentCardsRefresh()
        }) {
        val list = mutableListOf<@Composable ()->Unit>()
        val decoderFactory =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) ImageDecoderDecoder.Factory() else GifDecoder.Factory()

        filteredCards.forEach {
            list.add {
                if (it.card != null) {
                    BrazeCustomContentCard(card = it.card, { card ->
                        try{
                            val uri = Uri.parse(card.url)
                            val event = JSONObject()
                            event.put("BannerID",card.id)
                            event.put("action token",uri.host)
                            event.put("arg token",uri.query)
                            BrazeManager.instance.logEvent(BrazeEventName.Promo_Banner_Clicked.value,event)
                            AnalyticsManager.instance.logEvent(
                                AnalyticsEvent(analyticsEvent = AnalyticsEventName.Promotion_Opened.value,
                                    properties = mapOf("Promotion ID" to it.card.id, "Promotion Name" to (it.card.extras.get("promotion_name") ?:"Unknown"),
                                        "Message" to uri.host.toString(), "Promotion Position" to it.id.toString()))
                            )
                        }catch (e:Exception){
                            Logger.e("Braze Card Click Error:" + e.message)
                        }
                        false
                    }, ContentCardStyling(modifier = Modifier.fillMaxHeight(),listPadding = 0.dp,imageComposable = {
                        val url = when(it.cardType){
                            CardType.IMAGE -> (it as ImageOnlyCard).imageUrl
                            CardType.CAPTIONED_IMAGE -> (it as CaptionedImageCard).imageUrl
                            CardType.SHORT_NEWS -> (it as ShortNewsCard).imageUrl
                            else -> ""
                        }
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(url)
                                .decoderFactory(decoderFactory)
                                .build(),
                            contentDescription = null,
                            contentScale = ContentScale.Crop,
                            modifier = Modifier
                                .fillMaxHeight()
                        )
                    }))
                } else {
                    Image(
                        painter = painterResource(id = it.placeholderImageName),
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .fillMaxHeight()
                            .clickableWithoutRipple {

                            }
                    )
                }
            }
        }
        LobbyCarousel(list)
    }
}

@Composable
fun BrazeNewCarousel(){
    val brazeCarouselViewModel = BrazeCarouselViewModel.instance
    val cards by BrazeManager.instance.cards.collectAsState()
    val filteredCards =  brazeCarouselViewModel.getIndexedContentCards(cards)

    //println("cards: ${filteredCards}")

    Box(modifier = Modifier
        .fillMaxSize()
        .background(Color(0xFF005FC8))
        .padding(bottom = 2.dp)
        .onAppear {
            BrazeManager.instance.requestContentCardsRefresh()
        }) {
        val list = mutableListOf<@Composable ()->Unit>()
        val decoderFactory =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) ImageDecoderDecoder.Factory() else GifDecoder.Factory()

        filteredCards.forEach {
            list.add {
                Box(modifier = Modifier.size(306.dp,157.dp)){
                    if (it.card != null) {
                        BrazeCustomContentCard(card = it.card, { card ->
                            try{
                                val uri = Uri.parse(card.url)
                                val event = JSONObject()
                                event.put("BannerID",card.id)
                                event.put("action token",uri.host)
                                event.put("arg token",uri.query)
                                BrazeManager.instance.logEvent(BrazeEventName.Promo_Banner_Clicked.value,event)
                                AnalyticsManager.instance.logEvent(
                                    AnalyticsEvent(analyticsEvent = AnalyticsEventName.Promotion_Opened.value,
                                        properties = mapOf("Promotion ID" to it.card.id, "Promotion Name" to (it.card.extras.get("promotion_name") ?:"Unknown"),
                                            "Message" to uri.host.toString(), "Promotion Position" to it.id.toString()))
                                )
                            }catch (e:Exception){
                                Logger.e("Braze Card Click Error:" + e.message)
                            }
                            false
                        }, ContentCardStyling(modifier = Modifier.fillMaxHeight(),listPadding = 0.dp,imageComposable = {
                            val url = when(it.cardType){
                                CardType.IMAGE -> (it as ImageOnlyCard).imageUrl
                                CardType.CAPTIONED_IMAGE -> (it as CaptionedImageCard).imageUrl
                                CardType.SHORT_NEWS -> (it as ShortNewsCard).imageUrl
                                else -> ""
                            }
                            AsyncImage(
                                model = ImageRequest.Builder(LocalContext.current)
                                    .data(url)
                                    .decoderFactory(decoderFactory)
                                    .build(),
                                contentDescription = null,
                                contentScale = ContentScale.Crop,
                                modifier = Modifier
                                    .fillMaxHeight()
                            )
                        }), modifier = Modifier
                            .clip(RoundedCornerShape(4.dp))
                            .border(1.dp, Color.White.copy(0.5f), RoundedCornerShape(4.dp)))
                    } else {
                        Image(
                            painter = painterResource(id = it.placeholderImageName),
                            contentDescription = null,
                            contentScale = ContentScale.Crop,
                            modifier = Modifier
                                .fillMaxHeight()
                                .clickableWithoutRipple {

                                }
                        )
                    }
                }
            }
        }
        LazyRow(contentPadding = PaddingValues(16.dp),horizontalArrangement = Arrangement.spacedBy(16.dp)) {
            list.forEach {
                item {
                    it()
                }
            }
        }
//        LobbyCarousel(list, contentPadding = PaddingValues(16.dp), pageSpacing = 16.dp, pagerButtonEnabled = false)
    }
}
class BrazeCarouselViewModel:ViewModel(){
    companion object {
        val instance: BrazeCarouselViewModel by lazy { BrazeCarouselViewModel() }

        val placeholderCard:List<IndexedContentCard> = listOf(
            IndexedContentCard(0,null, R.drawable.img_lobby_banner)
        )
    }
//    private val _cards : MutableStateFlow<List<IndexedContentCard>> = MutableStateFlow(placeholderCard)
//    val cards = _cards.asStateFlow()

    fun getIndexedContentCards(cards:List<Card>):List<IndexedContentCard>{
        if(cards.isEmpty()){
            return placeholderCard
        }else{
            val list = mutableListOf<IndexedContentCard>()
            cards.forEachIndexed { index, card ->
                list.add(
                    IndexedContentCard(index,card,R.drawable.img_lobby_banner)
                )
            }
            //todo: this filter logic is temp for test, could change at the end
            val res =  list.filter { it.card?.extras?.get("section") == BrazeContentCardType.carousel.name }.sortedBy { it.card?.extras?.get("display_order") }
            if(res.isEmpty()){
                return placeholderCard
            }else{
                return res
            }
        }
    }
}
data class IndexedContentCard(
    val id:Int,
    val card: Card?,
    val placeholderImageName:Int
)
@Preview
@Composable
fun BrazeCarouselPreview(){
    val list = mutableListOf<@Composable ()->Unit>()
    list.add{
        Image(
            painter = painterResource(id = R.drawable.img_welcome_1),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .fillMaxHeight()
                .clickableWithoutRipple {

                }
        )
    }
    list.add{
        Image(
            painter = painterResource(id = R.drawable.img_welcome_2),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .fillMaxHeight()
                .clickableWithoutRipple {

                }
        )
    }
    list.add{
        Image(
            painter = painterResource(id = R.drawable.img_welcome_3),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .fillMaxHeight()
                .clickableWithoutRipple {

                }
        )
    }
    LazyRow(contentPadding = PaddingValues(16.dp),horizontalArrangement = Arrangement.spacedBy(16.dp)) {
        list.forEach {
            item {
                it()
            }
        }
    }
//    LobbyCarousel(list, contentPadding = PaddingValues(16.dp), pageSpacing = 16.dp)
}