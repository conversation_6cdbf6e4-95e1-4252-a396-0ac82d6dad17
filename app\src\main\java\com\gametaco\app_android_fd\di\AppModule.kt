package com.gametaco.app_android_fd.di

import AppsFlyerManager
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.api.WorldWinnerNetworkService
import com.gametaco.app_android_fd.manager.ActivityManager
import com.gametaco.app_android_fd.manager.AlertDialogManager
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.ClipboardManager
import com.gametaco.app_android_fd.manager.DeepLinkManager
import com.gametaco.app_android_fd.manager.DeviceManager
import com.gametaco.app_android_fd.manager.ErrorManager
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.GeoComplyManager
import com.gametaco.app_android_fd.manager.HapticsManager
import com.gametaco.app_android_fd.manager.LoadingManager
import com.gametaco.app_android_fd.manager.LocationManager
import com.gametaco.app_android_fd.manager.MaintenanceModeManager
import com.gametaco.app_android_fd.manager.MockModeManager
import com.gametaco.app_android_fd.manager.ModalPopupManager
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.PermissionsManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.SiftManager
import com.gametaco.app_android_fd.manager.ToastManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.WalletManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.manager.analytics.AppsFlyerEventFactory
import com.gametaco.app_android_fd.ui.screens.GeoAlertModel
import com.gametaco.app_android_fd.utils.CoroutineScopes
import org.koin.dsl.module

val appModule = module {
    single<ActivityManager> { ActivityManager(get(), get()) }
    single<AlertDialogManager> { AlertDialogManager() }
    single<AnalyticsManager> { AnalyticsManager(get()) }
    single<ExperimentManager> { ExperimentManager(get(), get()) }
    single<AppsFlyerManager> { AppsFlyerManager() }
    single<AuthenticationManager> { AuthenticationManager(get(), get(), get(), get(), lazy { get() }, lazy { get() }, get(),lazy { get() }, get(), get()) }
    single<AppsFlyerEventFactory> { AppsFlyerEventFactory(get(), get(), get(), get()) }
    single<BrazeManager> { BrazeManager(get()) }
    single<ClipboardManager> { ClipboardManager(get()) }
    single<CoroutineScopes> { CoroutineScopes() }
    single<DeviceManager> { DeviceManager(get()) }
    single<FDManager> { FDManager(get(), get(), get(), get(), get(), get(), get(), get()) }
    single<GeoComplyManager> { GeoComplyManager(get(), get(), get(), get(), get(), get()) }
    single<HapticsManager> { HapticsManager(get(), get()) }
    single<LocationManager> { LocationManager(get(), get(), get(), get()) }
    single<MaintenanceModeManager> { MaintenanceModeManager(get(), get(), get(), get()) }
    single<ModalPopupManager> { ModalPopupManager() }
    single<NavManager> { NavManager() }
    single<PermissionsManager> { PermissionsManager(get()) }
    single<PreferencesManager> { PreferencesManager(get()) }
    single<SiftManager> { SiftManager() }
    single<ToastManager> { ToastManager(get()) }
    single<TournamentManager> { TournamentManager(get(), get(), get(), get(), get(), get(), get(), get(), get(), get()) }
    single<WalletManager> { WalletManager(get(), get(), get(), get(),get()) }
    single<WorldWinnerAPI> { WorldWinnerAPI(get()) }
    single<WorldWinnerNetworkService> { WorldWinnerNetworkService() }
    single<ErrorManager> { ErrorManager(get()) }
    single<DeepLinkManager> { DeepLinkManager(get(),get(), get()) }
    single<GeoAlertModel> { GeoAlertModel() }
    single<LoadingManager> { LoadingManager() }
    single<MockModeManager> { MockModeManager(get(), get(), get(), get()) }
}
