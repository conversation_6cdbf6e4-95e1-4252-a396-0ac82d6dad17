package com.gametaco.app_android_fd.manager

import com.gametaco.app_android_fd.MainApplication
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APIGeocomplyTokenExchangeRequest
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.CoroutineScopes
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.utilities.ResourceState
import com.geocomply.client.GeoComplyClient
import com.geocomply.client.GeoComplyClientDeviceConfigListener
import com.geocomply.client.GeoComplyClientException
import com.geocomply.client.GeoComplyClientIpChangeListener
import com.geocomply.client.GeoComplyClientListener
import com.geocomply.client.IGeoComplyClient
import com.geocomply.client.ReasonCode
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import java.util.Date

data class OnGeocomplyTokenExchangeEvent(val token: String?)

class GeoComplyManager(
    private val fdManager: FDManager,
    private val worldWinnerAPI: WorldWinnerAPI,
    private val authenticationManager: AuthenticationManager,
    private val preferencesManager: PreferencesManager,
    private val alertDialogManager: AlertDialogManager,
    private val coroutineScopes: CoroutineScopes,
) : GeoComplyClientListener, GeoComplyClientDeviceConfigListener {
    companion object {
        val instance: GeoComplyManager
            get() = resolve()
        const val TAG = "GeoComplyManager"
    }

    data class AuthDetails(
        val fdSessionId: String,
        val fdUserId: String
    )

    private var geoComplyClient: GeoComplyClient? = null
    var latestAuthDetails : AuthDetails? = null

    var license : String? = null
    private var geoPacket : String? = null
    var decodedJWT : String? = null
    var latestCountryCode : String? = null
    var latestRegionCode : String? = null

    private var isFirstTimeRequest:Boolean = true
    fun reset(){
        isFirstTimeRequest = true
    }

    var isRefreshingToken = false

    override fun onGeolocationAvailable(data: String) {
        geoPacket = data

        Logger.d(TAG, "onGeolocationAvailable, geoPacket: $geoPacket")

        if (!geoPacket.isNullOrEmpty()) {
            coroutineScopes.main.launch {
                var tokenExchangeResponse = worldWinnerAPI.postGeocomplyTokenExchange(
                    APIGeocomplyTokenExchangeRequest(
                        geo_packet = geoPacket!!
                    )
                )

                if (tokenExchangeResponse.isSuccess()) {
                    decodedJWT = (tokenExchangeResponse as ResourceState.Success).data.token
                    latestCountryCode = (tokenExchangeResponse as ResourceState.Success).data.country_code
                    latestRegionCode = (tokenExchangeResponse as ResourceState.Success).data.region_code
                    Logger.d(TAG, "Decoded token JWT as: $decodedJWT")
                    if(isFirstTimeRequest){
                        EventBus.getDefault().post(OnGeocomplyTokenExchangeEvent(decodedJWT))
                    }
                    isRefreshingToken = false
                } else if(tokenExchangeResponse.isError()) {
                    Logger.d(TAG, "postGeocomplyTokenExchange failed")
                    decodedJWT = null
                    if(isFirstTimeRequest){
                        alertDialogManager.showDialog("Geolocation Error","Geocomply token is null, which should not happen, cannot perform geolocation","Confirm",{})
                        EventBus.getDefault().post(OnGeocomplyTokenExchangeEvent(null))
                    }
                    isRefreshingToken = false
                }
                isFirstTimeRequest = false
            }
        }
    }



    override fun onGeolocationFailed(var1: com.geocomply.client.Error?, var2: String?) {
        Logger.d(TAG, "onGeolocationFailed: " + var1?.toString())
        isRefreshingToken = false

        if (var1 == com.geocomply.client.Error.LICENSE_EXPIRED)
            if (var1.isNeedRetry) {
                requestGeolocation()
            } else {
                EventBus.getDefault().post(OnGeocomplyTokenExchangeEvent(null))
            }

    }

    override fun onLocationServicesDisabled(serviceTypes: Set<IGeoComplyClient.LocationServiceType>): Boolean {
        Logger.d(TAG, "onLocationServicesDisabled")
        return false
    }



    @Throws(GeoComplyClientException::class)
    fun requestGeolocation() {
        if(authenticationManager.isGuest){
            Logger.d(TAG, "skip geocomply for guest mode")
            EventBus.getDefault().post(OnGeocomplyTokenExchangeEvent(null))
            isRefreshingToken = false
            return
        }
        
        // Skip geolocation for mock users
        if(MockModeManager.instance.isMockModeEnabled){
            Logger.d(TAG, "skip geocomply for mock mode")
            EventBus.getDefault().post(OnGeocomplyTokenExchangeEvent("FAKE_TOKEN"))
            isRefreshingToken = false
            return
        }

        LocationManager.instance.checkPermissions(false) { granted ->

            if (granted) {
                coroutineScopes.main.launch {
                    // request the license first
                    requestLicense()

                    val apiMe = authenticationManager.apiMe

                    Logger.d(TAG, "apiMe contents: $apiMe.toString()")

                    // Don't request geo location if license is null
                    if (!license.isNullOrEmpty() && apiMe != null) {

                        try {
                            if (geoComplyClient == null) {
                                geoComplyClient =
                                    GeoComplyClient.getInstance(MainApplication.context.applicationContext)
                            }

                            Logger.d(TAG, "requestGeolocation with params")
                            Logger.d(TAG, "FanduelID: " + apiMe.fanduel_id.toString())
                            Logger.d(TAG, "SessionID: " + fdManager.sessionDataSessionID!!)
                            Logger.d(TAG, "License: " + license!!)

                            geoComplyClient?.apply {
                                eventListener = this@GeoComplyManager
                                deviceConfigEventListener = this@GeoComplyManager
                                userId = apiMe.fanduel_id.toString()
                                //setUserSessionID(FDManager.instance.sessionDataSessionID!!)
                                geolocationReason = "Login"
                                reasonCode = ReasonCode.LOGIN
                                //setUserPhoneNumber("**********")
                                setLicense(license!!) // license from ww
                                customFields.put("session_id", fdManager.sessionDataSessionID!!)
                                requestGeolocation()
                            }
                        } catch (e: Exception) {
                            Logger.w(TAG, e)
                            onPermissionFail(e.toString())
                        }
                    } else {
                        Logger.w(
                            TAG,
                            "license is null in GeoComplyManager, this should not happen, cannot perform geolocation"
                        )
                        onPermissionFail("license is null in GeoComplyManager, this should not happen, cannot perform geolocation")
                    }
                }
            } else {
                onPermissionFail("Location permissions not granted")
            }
        }
    }
    private fun onPermissionFail(msg:String){
        isRefreshingToken = false
        EventBus.getDefault().post(OnGeocomplyTokenExchangeEvent(null))
    }

    private suspend fun checkLicenseExpiry(expires_at:String) {
        val expireDate = expires_at.toDate()
        val currentDate = Date()

        if (expireDate != null && expireDate.after(currentDate)) {
            Logger.d(TAG, "GeoComply Licence is valid, yeah")
            val gap =  expireDate.time - currentDate.time
            Logger.d("gap is $gap")

            coroutineScopes.io.launch {
                Thread.sleep(gap)
                requestGeolocation()
            }
        } else {
            Logger.d(TAG, "GeoComply Licence is invalid, request again")
            //requestGeolocation()
        }
    }

    private suspend fun requestLicense() {
        Logger.d(TAG, "Requesting GeoComply License")

        // Skip license request for mock users
        if(MockModeManager.instance.isMockModeEnabled){
            Logger.d(TAG, "skip geocomply license request for mock mode")
            license = "FAKE_LICENSE"
            return
        }

        val licenceResponse = worldWinnerAPI.getGeocomplyLicense()

        if (licenceResponse.isSuccess()) {

            Logger.d(TAG, "getGeocomplyLicense Successful")
            val data = (licenceResponse as ResourceState.Success).data

            if (data.license.isNotEmpty()) {
                preferencesManager.setGeoComplyLicense(data.license)
                preferencesManager.setGeoComplyLicenseExpiry(data.expires_at)
                Logger.d("data.expires_at>>>> ${data.expires_at}")
                license = data.license

//                checkLicenseExpiry(data.expires_at)
            } else {
                Logger.w(TAG, "Received empty license from server")
            }
        } else if (licenceResponse.isError()){
            Logger.d(TAG, "Error getting GeoComply license")
        }
    }

    private fun makeGeolocationRequest(license: String, authDetails: AuthDetails) {
        setAuthDetails(authDetails)

        // Geocomply is regulated, and sometimes has to provide geolocation reasons to regulators.
        // As a result, we need to add meta data for the reason for the geolocation request.
        geoComplyClient?.geolocationReason = "login"
        geoComplyClient?.reasonCode = ReasonCode.LOGIN

        try {
            geoComplyClient?.setLicense(license)
        } catch (error: Exception) {
            Logger.e("Error: $error")
            return
        }

        coroutineScopes.main.launch {
            // Geocomply might be accessing UI components, so it is wrapped in runOnUiThread
            try {
                geoComplyClient?.requestGeolocation(13)
            } catch (error: Exception) {
                Logger.e(TAG, "Error: $error")
            }
        }
    }

    suspend fun stopIndoorGeolocation() {
        geoComplyClient?.stopUpdating()
    }

    private fun startIPChangeDetectionService() {
        try {
            geoComplyClient?.setLicense(license ?: "")
        } catch (error: Exception) {
            Logger.e("Error: $error")
        }

        geoComplyClient?.startMyIpService(object : GeoComplyClientIpChangeListener {
            override fun onMyIpSuccess(p0: String?) {
                geoComplyClient?.ackMyIpSuccess()
                coroutineScopes.main.launch {
                    val latestAuthDetails = latestAuthDetails
                    if (license != null && latestAuthDetails != null) {
                        makeGeolocationRequest(license!!, latestAuthDetails)
                    }
                }
            }

            override fun onMyIpFailure(p0: Int, p1: String?, p2: Long) {
            }
        })
    }

    private fun stopIPChangeDetectionService() {
        geoComplyClient?.stopMyIpService()
    }

    private fun setAuthDetails(authDetails: AuthDetails) {
        geoComplyClient?.userId = authDetails.fdUserId
        geoComplyClient?.setUserSessionID(authDetails.fdSessionId)
        geoComplyClient?.customFields?.clear()
        geoComplyClient?.customFields?.put("session_id", authDetails.fdSessionId)
    }

    suspend fun refreshGeoComplyToken() {
        // Skip refresh for mock users
        if(MockModeManager.instance.isMockModeEnabled){
            Logger.d(TAG, "skip geocomply refresh for mock mode")
            decodedJWT = "FAKE_TOKEN"
            isRefreshingToken = false
            return
        }
        
        isRefreshingToken = true

        requestGeolocation()
    }
}
