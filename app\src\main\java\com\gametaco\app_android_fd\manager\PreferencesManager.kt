package com.gametaco.app_android_fd.manager

import android.content.Context
import android.content.SharedPreferences
import com.gametaco.app_android_fd.data.ENV
import com.gametaco.app_android_fd.data.EnvironmentSpec
import com.gametaco.app_android_fd.data.entity.APICatalog
import com.gametaco.app_android_fd.data.entity.APICatalogVersion
import com.gametaco.app_android_fd.data.entity.APIGamesResponse
import com.gametaco.app_android_fd.data.entity.APITournament
import com.gametaco.app_android_fd.data.entity.GameCatalogResponse
import com.gametaco.app_android_fd.data.entity.GameCatalogVersion
import com.gametaco.app_android_fd.data.entity.Prize
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.log.Logger
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.adapter
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import java.util.Date

const val EXPIRY_KEY = "__expiry"

class PreferencesManager(
    private val context: Context,
) {

    companion object {
        val instance: PreferencesManager
            get() = resolve()
        const val preferencesName = "AppPreferences"
    }

    val sharedPreferences: SharedPreferences
        get() = context.getSharedPreferences(preferencesName, Context.MODE_PRIVATE)


    fun checkAndRemoveExpiredKeys(){
        val allEntries = sharedPreferences.all
        val currentTime = System.currentTimeMillis()
        for ((key, value) in allEntries) {
            if (key.endsWith(EXPIRY_KEY)) {
//                println("find ttl key:" + key)

                val expiryTime = sharedPreferences.getLong(key, 0)
                if (currentTime > expiryTime) {
                    sharedPreferences.edit().apply {
                        remove(key.replace(EXPIRY_KEY,""))//remove original key
                        remove(key)//remove ttl key
                        Logger.d("remove expired ttl key:" + key)
                        apply()
                    }
                }
            }
        }
    }

    inline fun <reified T> getPreference(key: String, defaultValue: T? = null): T? {
        return when (T::class) {
            String::class -> sharedPreferences.getString(key, defaultValue as? String) as? T
            Int::class -> sharedPreferences.getInt(key, defaultValue as? Int ?: -1) as? T
            Boolean::class -> sharedPreferences.getBoolean(key, defaultValue as? Boolean ?: false) as? T
            Float::class -> sharedPreferences.getFloat(key, defaultValue as? Float ?: -1f) as? T
            Long::class -> sharedPreferences.getLong(key, defaultValue as? Long ?: -1L) as? T
            else -> throw IllegalArgumentException("Unsupported preference type")
        }
    }

    inline fun <reified T> setPreference(key: String, value: T?,ttl: Long? = null) {
        val editor = sharedPreferences.edit()
        if(value == null) {
            editor.remove(key)
        } else {
            when (value) {
                is String -> editor.putString(key, value)
                is Int -> editor.putInt(key, value)
                is Boolean -> editor.putBoolean(key, value)
                is Float -> editor.putFloat(key, value)
                is Long -> editor.putLong(key, value)
                else -> throw IllegalArgumentException("Unsupported preference type")
            }
            //save the expiry time
            if(ttl != null){
                val expiryTime = System.currentTimeMillis() + ttl
                editor.putLong("${key}${EXPIRY_KEY}", expiryTime)
            }
        }
        editor.apply()
    }

    fun getDeviceID() : String? {
        return getPreference<String>("deviceID")
    }

    fun setDeviceID(value: String?) {

        setPreference<String>("deviceID", value)
    }

    fun getFanduelSessionDataSessionID() : String? {
        return getPreference<String>("fanduelSessionDataSessionID")
    }

    fun setFanduelSessionDataSessionID(value : String?) {
        setPreference<String>("fanduelSessionDataSessionID", value)
    }


    fun getFanduelSessionDataDateCreated() : String? {
        return getPreference<String>("fanduelSessionDataDate")
    }

    fun setFanduelSessionDataDateCreated(value : String?) {
        setPreference<String>("fanduelSessionDataDate", value)
    }

    fun setFanduelSessionDataToken(value : String?) {
        setPreference<String>("fanduelSessionDataToken", value)
    }

    fun getFanduelSessionDataToken() : String? {
        return getPreference<String>("fanduelSessionDataToken")
    }

    fun getFanduelSessionDataLoginToken() : String? {
        return getPreference<String>("fanduelSessionDataLoginToken")
    }

    fun setFanduelSessionDataLoginToken(value : String?) {
        setPreference<String>("fanduelSessionDataLoginToken", value)
    }

    fun getGeoComplyLicense() : String? {
        return getPreference<String>("geoComplyLicense")
    }

    fun setGeoComplyLicense(value : String?) {
        setPreference<String>("geoComplyLicense", value)
    }

    fun getGeoComplyLicenseExpiry() : String? {
        return getPreference<String>("geoComplyLicenseExpiry")
    }

    fun setGeoComplyLicenseExpiry(value : String?) {
        setPreference<String>("geoComplyLicenseExpiry", value)
    }

    fun getWWAuthToken() : String? {
        return getPreference<String>("wwAuthToken")
    }

    fun setWWAuthToken(value: String?) {
        setPreference<String>("wwAuthToken", value)
    }

    fun getIsGuest() : Boolean? {
        return getPreference<Boolean>("isGuest")
    }

    fun setIsGuest(value: Boolean) {
        setPreference<Boolean>("isGuest", value)
    }

    fun getHasPriorLogIn() : Boolean {
        return getPreference<Boolean>("hasPriorLogIn")?: false
    }

    fun setHasPriorLogIn(value: Boolean) {
        setPreference<Boolean>("hasPriorLogIn", value)
    }

    fun getIsBalanceHidden() : Boolean {
        return getPreference<Boolean>("isBalanceHidden", false)?: false
    }

    fun setIsBalanceHidden(value: Boolean) {
        setPreference<Boolean>("isBalanceHidden", value)
    }

    fun getShowLobbyFtue() : Boolean {
        return getPreference<Boolean>("showLobbyFtue", true)?: true
    }

    fun setShowLobbyFtue(value: Boolean) {
        setPreference<Boolean>("showLobbyFtue", value)
    }


    fun getShowRewardsFtue() : Boolean {
        return getPreference<Boolean>("showRewardsFtue", true)?: true
    }

    fun setShowRewardsFtue(value: Boolean) {
        setPreference<Boolean>("showRewardsFtue", value)
    }

    fun getIsHapticsEnabled() : Boolean {
        return getPreference<Boolean>("isHapticsEnabled", false)?: false
    }

    fun setIsHapticsEnabled(value: Boolean) {
        setPreference<Boolean>("isHapticsEnabled", value)
    }

    fun getAppENV() : String{
        return getPreference<String>("appENV") ?: ENV.QA.name
    }
    fun setAppENV(env:String){
        setPreference<String>("appENV",env)
    }
    fun getEnvironmentSpec(): EnvironmentSpec? {
        return getPreference<String?>("environmentSpec")
            ?.let { EnvironmentSpec.fromJsonString(it) }
    }
    fun setEnvironmentSpec(envHost: String) {
        setPreference<String>("environmentSpec", envHost)
    }

    fun getWinTimes():Int{
        return Math.max(0,getPreference<Int>("winTimes") ?: 0)
    }
    fun accumulateWinTime(){
        setPreference<Int>("winTimes",getWinTimes() +  1)
    }
    fun setWinTime(value:Int){
        setPreference<Int>("winTimes",value)
    }
    fun getLastSeenWinTimes():Int{
        return getPreference<Int>("lastSeenWinTimes") ?: 0
    }
    fun setLastSeenWinTime(value:Int){
        setPreference<Int>("lastSeenWinTimes",value)
    }
    fun getScoresScreenLastSeen():Date{
        val time = getPreference<Long>("scoresScreenLastSeen")
        if(time != null){
            return Date(time)
        }
        return Date()
    }
    fun setScoresScreenLastSeen(date: Date){
        setPreference<Long>("scoresScreenLastSeen",date.time)
    }
    fun clearScoresScreenLastSeen(){
        setPreference<Long>("scoresScreenLastSeen",null)
    }

    fun getGoalsScreenLastSeen():Date{
        val time = getPreference<Long>("goalsScreenLastSeen")
        if(time != null){
            return Date(time)
        }
        return Date()
    }
    fun setGoalsScreenLastSeen(date: Date){
        setPreference<Long>("goalsScreenLastSeen",date.time)
    }
    fun clearGoalsScreenLastSeen(){
        setPreference<Long>("goalsScreenLastSeen",null)
    }
    fun isGameSeen(gameId:String):Boolean{
        val list = getGamesSeen()
        return list?.contains(gameId) == true
    }
    fun getGamesSeen():String?{
        return getPreference<String>("gamesSeen")
    }
    fun addGameToSeen(gameId:String){
        var list = getGamesSeen() ?: ""
        if(!list.contains(gameId)){
            list += ",${gameId}"
            setPreference<String>("gamesSeen",list)
        }
    }
    fun clearGamesSeen(){
        setPreference<String>("gamesSeen",null)
    }
    fun clearCacheAfterLogout(){
        clearGoalsScreenLastSeen()
        clearGamesSeen()
        clearGamePlayed()
        clearTurboViewed()
        clearDailyReward()
//        clearReplayViewed()
        
        // Clear mock mode data on logout
        MockModeManager.instance.clearMockData()
    }

    fun getBrazeTestDevice() : Boolean {
        return getPreference<Boolean>("brazeTestDevice")?: false
    }

    fun setBrazeTestDevice(value: Boolean) {
        setPreference<Boolean>("brazeTestDevice", value)
    }

    fun getAppFirstLaunch() : Boolean {
        return getPreference<Boolean>("appFirstLaunch", true)?: true
    }
    fun setAppFirstLaunch(value: Boolean) {
        setPreference<Boolean>("appFirstLaunch", value)
    }

    // Mock Mode preferences
    fun getMockModeEnabled(): Boolean {
        return getPreference<Boolean>("mockModeEnabled") ?: false
    }
    
    fun setMockModeEnabled(value: Boolean) {
        setPreference<Boolean>("mockModeEnabled", value)
    }
    
    fun getMockUsername(): String? {
        return getPreference<String>("mockUsername")
    }
    
    fun setMockUsername(value: String?) {
        setPreference<String>("mockUsername", value)
    }
    
    fun getMockUserId(): String? {
        return getPreference<String>("mockUserId")
    }
    
    fun setMockUserId(value: String?) {
        setPreference<String>("mockUserId", value)
    }
    fun getGamePlayed() : Int {
        return getPreference<Int>("gamePlayed", 0)?: 0
    }
    fun accumulateGamePlayed() {
        val count = this.getGamePlayed() + 1
        setPreference<Int>("gamePlayed", count)
    }
    fun clearGamePlayed(){
        setPreference<Int>("gamePlayed", 0)
    }

    fun getRestrictedStatus() : Boolean {
        return getPreference<Boolean>("restrictedStatus", false)?: false
    }

    fun setRestrictedStatus(value: Boolean) {
        setPreference<Boolean>("restrictedStatus", value)
    }

    fun getAttemptedAwToUM() : Boolean {
        return getPreference<Boolean>("attemptedAwToUM", false)?: false
    }

    fun setAttemptedAwToUM(value: Boolean) {
        setPreference<Boolean>("attemptedAwToUM", value)
    }

    fun getIsTurboViewed():Boolean{
        return getPreference<Boolean>("isTurboViewed", false)?: false
    }
    fun setIsTurboViewed(){
        setPreference<Boolean>("isTurboViewed", true)
    }
    fun clearTurboViewed(){
        setPreference<Boolean>("isTurboViewed", false)
    }
    fun getIsReplayViewed():Boolean{
        return getPreference<Boolean>("isReplayViewed", false)?: false
    }
    fun setIsReplayViewed(){
        setPreference<Boolean>("isReplayViewed", true)
    }
    fun clearReplayViewed(){
        setPreference<Boolean>("isReplayViewed", false)
    }
    @OptIn(ExperimentalStdlibApi::class)
    fun getTournamentPrizesCache(tournamentId:String?): List<Prize>?{
        val str =  getPreference<String>("tournament_prize_${tournamentId}", null)
        if(str != null){
            try {
                val moshi = Moshi.Builder()
                    .add(KotlinJsonAdapterFactory())
                    .build()
                val jsonAdapter: JsonAdapter<List<Prize>> = moshi.adapter()
                val prize = jsonAdapter.fromJson(str)
//                println("getTournamentPrizesCache:" + prize)
                return prize
            }catch(e: Exception) {
                Logger.e("getTournamentPrizesCache error:" + e.message)
            }
        }
        return null
    }
    @OptIn(ExperimentalStdlibApi::class)
    fun setTournamentPrizesCache(tournament: APITournament){
        try {
            val moshi = Moshi.Builder()
                .add(KotlinJsonAdapterFactory())
                .build()
            val jsonAdapter: JsonAdapter<List<Prize>> = moshi.adapter()
            val prize = jsonAdapter.toJson(tournament.prizes)
//            println("setTournamentPrizesCache for ${tournament.id}:" + prize)
            setPreference("tournament_prize_${tournament.id}",prize,7 * 24 * 3600 * 1000)//save for 7 days
        }catch(e: Exception) {
            Logger.e("setTournamentPrizesCache for ${tournament.id} error:" + e.message)
        }
    }
    @OptIn(ExperimentalStdlibApi::class)
    fun getCatalogCache(): GameCatalogResponse?{
        val str =  getPreference<String>("catalog", null)
        if(str != null){
            try {
                val moshi = Moshi.Builder()
                    .add(KotlinJsonAdapterFactory())
                    .build()
                val jsonAdapter: JsonAdapter<GameCatalogResponse> = moshi.adapter()
                val json = jsonAdapter.fromJson(str)
                return json
            }catch(e: Exception) {
                Logger.e("getCatalogCache error:" + e.message)
            }
        }
        return null
    }
    fun getCachedGameCatalogVersion(): GameCatalogVersion? {
        return getPreference<String>("cachedGameCatalogVersion", null)?.let {
            try {
                return GameCatalogVersion.fromJsonString(it)
            } catch (e: Exception) {
                Logger.e("getCatalogCacheVersionNumber error:" + e.message)
                null
            }
        }
    }
    private fun setCachedGameCatalogVersion(version: GameCatalogVersion){
        setPreference<String>("cachedGameCatalogVersion", version.jsonString)
    }
    @OptIn(ExperimentalStdlibApi::class)
    fun getNewCatalogCache(): APICatalog?{
        val str =  getPreference<String>("catalogNew", null)
        if(str != null){
            try {
                val moshi = Moshi.Builder()
                    .add(KotlinJsonAdapterFactory())
                    .build()
                val jsonAdapter: JsonAdapter<APICatalog> = moshi.adapter()
                val json = jsonAdapter.fromJson(str)
                return json
            }catch(e: Exception) {
                Logger.e("getNewCatalogCache error:" + e.message)
            }
        }
        return null
    }
    fun getCachedNewGameCatalogVersion(): APICatalogVersion? {
        return getPreference<String>("cachedNewGameCatalogVersion", null)?.let {
            try {
                return APICatalogVersion.fromJsonString(it)
            } catch (e: Exception) {
                Logger.e("getNewCatalogCacheVersionNumber error:" + e.message)
                null
            }
        }
    }
    private fun setCachedNewGameCatalogVersion(version: APICatalogVersion){
        setPreference<String>("cachedNewGameCatalogVersion", version.jsonString)
    }
    fun setDailyRewardSeen(value:Boolean){
        setPreference<Boolean>("isDailyRewardSeen", value)
    }
    fun getDailyRewardSeen():Boolean{
        return getPreference<Boolean>("isDailyRewardSeen", false)?: false
    }
    fun getDailyRewardNextAvailableDate():Date?{
        val time = getPreference<Long>("dailyRewardNextAvailableDate")
        if(time != null){
            return Date(time)
        }
        return null
    }
    fun saveDailyRewardNextAvailableDate(date: Date?){
        setPreference<Long>("dailyRewardNextAvailableDate",date?.time)
    }
    fun clearDailyReward(){
        setDailyRewardSeen(false)
        saveDailyRewardNextAvailableDate(null)
    }
    @OptIn(ExperimentalStdlibApi::class)
    fun setCatalogCache(data: GameCatalogResponse, gameCatalogVersion: GameCatalogVersion){
        try {
            val moshi = Moshi.Builder()
                .add(KotlinJsonAdapterFactory())
                .build()
            val jsonAdapter: JsonAdapter<GameCatalogResponse> = moshi.adapter()
            val json = jsonAdapter.toJson(data)
            setCachedGameCatalogVersion(gameCatalogVersion)
            setPreference("catalog",json)
            Logger.d("setCatalogCache success: $json")
        }catch(e: Exception) {
            Logger.e("setCatalogCache error: ${e.message}")
        }
    }
    @OptIn(ExperimentalStdlibApi::class)
    fun setNewCatalogCache(data: APICatalog, gameCatalogVersion: APICatalogVersion){
        try {
            val moshi = Moshi.Builder()
                .add(KotlinJsonAdapterFactory())
                .build()
            val jsonAdapter: JsonAdapter<APICatalog> = moshi.adapter()
            val json = jsonAdapter.toJson(data)
            setCachedNewGameCatalogVersion(gameCatalogVersion)
            setPreference("catalogNew",json)
            Logger.d("setNewCatalogCache success: $json")
        }catch(e: Exception) {
            Logger.e("setNewCatalogCache error: ${e.message}")
        }
    }
    @OptIn(ExperimentalStdlibApi::class)
    fun getAvailableGamesCache(): APIGamesResponse?{
        val str = getPreference<String>("availableGames", null)
        if(str != null){
            try {
                val moshi = Moshi.Builder()
                    .add(KotlinJsonAdapterFactory())
                    .build()
                val jsonAdapter: JsonAdapter<APIGamesResponse> = moshi.adapter()
                val json = jsonAdapter.fromJson(str)
                return json
            }catch(e: Exception) {
                Logger.e("getAvailableGamesCache error: ${e.message}")
            }
        }
        return null
    }
    @OptIn(ExperimentalStdlibApi::class)
    fun setAvailableGamesCache(data: APIGamesResponse){
        try {
            val moshi = Moshi.Builder()
                .add(KotlinJsonAdapterFactory())
                .build()
            val jsonAdapter: JsonAdapter<APIGamesResponse> = moshi.adapter()
            val json = jsonAdapter.toJson(data)
            setPreference("availableGames",json)
            Logger.d("setAvailableGamesCache success: $json")
        }catch(e: Exception) {
            Logger.e("setAvailableGamesCache error: ${e.message}")
        }
    }
}
