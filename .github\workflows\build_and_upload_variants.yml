name: Build and Upload Single Env Variants

on:
  workflow_dispatch:
    inputs:
      buildType:
        description: 'Type of build to perform (QA/Production)'
        required: true
        type: choice
        options:
          - QA
          - Production
      variant:
        description: 'App variant to build'
        required: true
        type: choice
        options:
          - Default
          - Games
env:
  ANDROID_NDK_DIR: /opt/homebrew/share/android-commandlinetools/ndk/23.1.7779620
  ANDROID_HOME: /opt/homebrew/share/android-commandlinetools

jobs:
  prepare-build-environment:
    runs-on: [self-hosted, X64]
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          lfs: true

      - name: GetUnity
        run: |
          git submodule update --init --recursive

      - name: Prepare Build Environment
        run: |
          make build-checks
        shell: bash

  check-aws:
    runs-on: [self-hosted, X64]
    needs: prepare-build-environment
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Check if AWS CLI is installed
        id: check_aws_cli
        run: |
          if ! command -v aws &> /dev/null; then
            echo "AWS CLI is not installed"
            echo "install_aws_cli=true" >> $GITHUB_ENV
          else
            echo "AWS CLI is already installed"
            echo "install_aws_cli=false" >> $GITHUB_ENV
          fi

      - name: Install AWS CLI
        if: ${{ env.install_aws_cli == 'true' }}
        run: |
          sudo apt-get update
          sudo apt-get install -y awscli

  build_qa:
    if: ${{ github.event.inputs.buildType == 'QA' }}
    runs-on: [self-hosted, X64]
    needs: [prepare-build-environment, check-aws]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Java environment
        run: |
          echo "/opt/homebrew/share/android-commandlinetools/build-tools/34.0.0" >> $GITHUB_PATH
          echo "JAVA_HOME=/opt/homebrew/opt/openjdk@17" >> $GITHUB_ENV
          echo "/opt/homebrew/opt/openjdk@17/bin" >> $GITHUB_PATH
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: "us-west-2"

      - name: BuildQaRelease
        run: |
          if [ "${{ github.event.inputs.variant }}" == "Default" ]; then
            make build-development-default-release
          else
            make build-qa-release
          fi

      - name: Upload to s3 Release
        run: |
          if [ "${{ github.event.inputs.variant }}" == "Default" ]; then
            ./signanduploadtos3.sh "./app/build/outputs/apk/developmentDefault/release/app-development-default-release-unsigned.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "Release"
          else
            ./signanduploadtos3.sh "./app/build/outputs/apk/developmentGames/release/app-development-games-release-unsigned.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "Release"
          fi

      - name: BuildQaDebug
        run: |
          if [ "${{ github.event.inputs.variant }}" == "Default" ]; then
            make build-development-default-debug
          else
            make build-qa-debug
          fi

      - name: Upload to s3 Debug
        run: |
          if [ "${{ github.event.inputs.variant }}" == "Default" ]; then
            ./signanduploadtos3.sh "./app/build/outputs/apk/developmentDefault/debug/app-development-default-debug.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "Debug"
          else
            ./signanduploadtos3.sh "./app/build/outputs/apk/developmentGames/debug/app-development-games-debug.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "Debug"
          fi

      - name: Trigger Increment Build Number Workflow
        uses: the-actions-org/workflow-dispatch@v4
        with:
          workflow: Increment Build Number
          ref: ${{ github.ref_name }}
          token: ${{ secrets.GITHUB_TOKEN }}
          wait-for-completion: true

      - name: Trigger Save Commit SHA Workflow
        uses: the-actions-org/workflow-dispatch@v4
        with:
          workflow: Save Commit SHA
          ref: ${{ github.ref_name }}
          token: ${{ secrets.GITHUB_TOKEN }}
          wait-for-completion: true

      - name: Create and push Git tag
        if: success()
        run: |
          ./scripts/create_tag.sh "${{ env.VERSION_NAME }}" "${{ env.VERSION_CODE }}" "qa"

  buildProduction:
    if: ${{ github.event.inputs.buildType == 'Production' }}
    runs-on: [self-hosted, X64]
    needs: [prepare-build-environment, check-aws]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Java environment
        run: |
          echo "/opt/homebrew/share/android-commandlinetools/build-tools/34.0.0" >> $GITHUB_PATH
          echo "JAVA_HOME=/opt/homebrew/opt/openjdk@17" >> $GITHUB_ENV
          echo "/opt/homebrew/opt/openjdk@17/bin" >> $GITHUB_PATH
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: "us-west-2"

      - name: BuildProductionRelease
        run: |
          if [ "${{ github.event.inputs.variant }}" == "Default" ]; then
            make build-production-release
          else
            make build-production-games-release
          fi

      - name: Upload to s3 ProductionRelease
        run: |
          if [ "${{ github.event.inputs.variant }}" == "Default" ]; then
            ./signanduploadtos3.sh "./app/build/outputs/apk/productionDefault/release/app-production-default-release-unsigned.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "ProductionRelease"
          else
            ./signanduploadtos3.sh "./app/build/outputs/apk/productionGames/release/app-production-games-release-unsigned.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "ProductionRelease"
          fi

      - name: BuildProductionDebug
        run: |
          if [ "${{ github.event.inputs.variant }}" == "Default" ]; then
            make build-production-debug
          else
            make build-production-games-debug
          fi

      - name: Upload to s3 ProductionDebug
        run: |
          if [ "${{ github.event.inputs.variant }}" == "Default" ]; then
            ./signanduploadtos3.sh "./app/build/outputs/apk/productionDefault/debug/app-production-default-debug.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "ProductionDebug"
          else
            ./signanduploadtos3.sh "./app/build/outputs/apk/productionGames/debug/app-production-games-debug.apk" ${{ secrets.KEYSTORE_ALIAS }} ${{ secrets.KEYSTORE_PASSWORD }} "ProductionDebug"
          fi

      - name: Trigger Increment Build Number Workflow
        uses: the-actions-org/workflow-dispatch@v4
        with:
          workflow: Increment Build Number
          ref: ${{ github.ref_name }}
          token: ${{ secrets.GITHUB_TOKEN }}
          wait-for-completion: true

      - name: Trigger Save Commit SHA Workflow
        uses: the-actions-org/workflow-dispatch@v4
        with:
          workflow: Save Commit SHA
          ref: ${{ github.ref_name }}
          token: ${{ secrets.GITHUB_TOKEN }}
          wait-for-completion: true

      - name: Create and push Git tag
        if: success()
        run: |
          ./scripts/create_tag.sh "${{ env.VERSION_NAME }}" "${{ env.VERSION_CODE }}" "prod"