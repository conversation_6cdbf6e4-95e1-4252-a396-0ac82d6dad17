package com.gametaco.app_android_fd.manager

import com.gametaco.app_android_fd.data.api.APIMe
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.di.resolve
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.utilities.ResourceState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/**
 * Manages Mock Mode functionality for testing purposes
 */
class MockModeManager(
    private val preferencesManager: PreferencesManager,
    private val authenticationManager: AuthenticationManager,
    private val navManager: NavManager,
    private val worldWinnerAPI: WorldWinnerAPI = WorldWinnerAPI.instance
) {
    companion object {
        private const val TAG = "MockModeManager"
        val instance: MockModeManager
            get() = resolve()
    }

    val isMockModeEnabled: Boolean
        get() = preferencesManager.getMockModeEnabled()

    val mockUsername: String?
        get() = preferencesManager.getMockUsername()

    fun enableMockMode(username: String) {
        if (username.isBlank()) {
            Logger.e(TAG, "Cannot enable mock mode with empty username")
            return
        }

        CoroutineScope(Dispatchers.Main).launch {
            try {
                Logger.d(TAG, "Initiating mock login for user: $username")
                
                // Call backend mock login endpoint
                val response = worldWinnerAPI.mockLogin(username)
                
                when (response) {
                    is ResourceState.Success -> {
                        val authToken = response.data
                        
                        // Save mock user data and auth token
                        preferencesManager.apply {
                            setMockUsername(username)
                            setMockUserId("mock_${System.currentTimeMillis()}")
                            setMockModeEnabled(true)
                            setWWAuthToken(authToken.key)
                            setIsGuest(false)
                            setHasPriorLogIn(true)
                        }
                        
                        Logger.d(TAG, "Mock login successful for user: $username")
                        
                        // Set mock FD session token for API calls that require x-fanduel-auth-token header
                        FDManager.instance.sessionDataToken = "mock_fd_session_${username}"
                        Logger.d(TAG, "Set mock FD session token")
                        
                        // Get user info
                        val meResponse = authenticationManager.getMe()
                        
                        if (meResponse.isSuccess()) {
                            // Trigger session changed event to refresh the app state
                            EventBus.getDefault().post(OnSessionChangedEvent(true, "MockMode/login-success"))
                            
                            // Navigate to games screen
                            navManager.navigateClearBackStack(Routes.GAMES_SCREEN)
                        } else {
                            Logger.e(TAG, "Failed to get user info after mock login")
                            disableMockMode()
                        }
                    }
                    is ResourceState.Error -> {
                        Logger.e(TAG, "Mock login failed: ${response.error}")
                        disableMockMode()
                    }
                    else -> {
                        Logger.e(TAG, "Unexpected response from mock login")
                        disableMockMode()
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, "Failed to enable mock mode", e)
                disableMockMode()
            }
        }
    }

    fun disableMockMode() {
        preferencesManager.apply {
            setMockModeEnabled(false)
            setMockUsername(null)
            setMockUserId(null)
        }
        
        // Clear FD session token
        FDManager.instance.sessionDataToken = null
        
        Logger.d(TAG, "Mock mode disabled")
    }


    fun clearMockData() {
        disableMockMode()
        Logger.d(TAG, "Mock data cleared")
    }
}