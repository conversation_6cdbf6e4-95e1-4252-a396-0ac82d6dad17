package com.gametaco.app_android_fd

import AppsFlyerManager
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.View.OnSystemUiVisibilityChangeListener
import android.view.WindowManager
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.gametaco.app_android_fd.data.AppConstants.Trustly.PAYMENT_PROVIDER_ID
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.data.ENV
import com.gametaco.app_android_fd.data.EnvConfigProviderImpl
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.data.trustly.EstablishData
import com.gametaco.app_android_fd.data.trustly.RetrofitClientData
import com.gametaco.app_android_fd.environment.debug.EnvironmentDebugControllerImpl
import com.gametaco.app_android_fd.manager.AlertDialogManager
import com.gametaco.app_android_fd.manager.BrazeManager
import com.gametaco.app_android_fd.manager.ClipboardManager
import com.gametaco.app_android_fd.manager.DeepLinkManager
import com.gametaco.app_android_fd.manager.ErrorManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.LoadingManager
import com.gametaco.app_android_fd.manager.ModalPopupManager
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.RequestPermissionsResultEvent
import com.gametaco.app_android_fd.manager.SiftManager
import com.gametaco.app_android_fd.manager.SystemBarState
import com.gametaco.app_android_fd.manager.ToastManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.UIManager
import com.gametaco.app_android_fd.ui.modifiers.detectMultiTap
import com.gametaco.app_android_fd.ui.navigation.AppNavigationGraph
import com.gametaco.app_android_fd.ui.popups.AlertDialogPopup
import com.gametaco.app_android_fd.ui.popups.DailyRewardEndView
import com.gametaco.app_android_fd.ui.popups.ModalPopup
import com.gametaco.app_android_fd.ui.popups.ReplayPopup
import com.gametaco.app_android_fd.ui.screens.AlertGeo
import com.gametaco.app_android_fd.ui.screens.DebugMenuScreen
import com.gametaco.app_android_fd.ui.screens.GeoAlertModel
import com.gametaco.app_android_fd.ui.screens.GeoAlertType
import com.gametaco.app_android_fd.ui.screens.LeaderboardScreen
import com.gametaco.app_android_fd.ui.screens.LoadingScreen
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme
import com.gametaco.app_android_fd.utils.CoroutineScopes
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.viewmodel.LeaderboardViewModel
import com.gametaco.app_android_fd.viewmodel.LoginViewModel
import com.gametaco.app_android_fd.webview.trustly.LightBoxActivity
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import net.trustly.android.sdk.views.TrustlyView
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import org.koin.android.ext.android.inject
import resources.R
import java.io.Serializable

data class OnReturnToMainActivityEvent(val returned: Boolean)

val LocalAlertDialogManager = staticCompositionLocalOf<AlertDialogManager> { error("AlertDialogManager not provided") }
val LocalClipboardManager = staticCompositionLocalOf<ClipboardManager> { error("ClipboardManager not provided") }
val LocalErrorManager = staticCompositionLocalOf<ErrorManager> { error("ErrorManager not provided") }
val LocalGeoAlertModel = staticCompositionLocalOf<GeoAlertModel> { error("GeoAlertModel not provided") }
val LocalModalPopupManager = staticCompositionLocalOf<ModalPopupManager> { error("ModalPopupManager not provided") }
val LocalNavManager = staticCompositionLocalOf<NavManager> { error("NavManager not provided") }
val LocalToastManager = staticCompositionLocalOf<ToastManager> { error("ToastManager not provided") }
val LocalUIManager = staticCompositionLocalOf<UIManager> { error("UIManager not provided") }
val LocalLoadingManager = staticCompositionLocalOf<LoadingManager> { error("LoadingManager not provided") }

@Suppress("DEPRECATION", "OVERRIDE_DEPRECATION")
class MainActivity : UnityWrapperActivity(), UIManager {
    companion object {
        const val TAG = "MainActivity"
    }

    private val coroutineScopes: CoroutineScopes by inject()
    private val navManager: NavManager by inject()
    private val brazeManager: BrazeManager by inject()
    private val appsFlyerManager: AppsFlyerManager by inject()
    private val siftManager: SiftManager by inject()
    private val alertDialogManager: AlertDialogManager by inject()
    private val modalPopupManager: ModalPopupManager by inject()
    private val errorManager: ErrorManager by inject()
    private val geoAlertModel: GeoAlertModel by inject()
    private val deepLinkManager: DeepLinkManager by inject()
    private val tournamentManager: TournamentManager by inject()
    private val toastManager: ToastManager by inject()
    private val clipboardManager: ClipboardManager by inject()
    private val loadingManager: LoadingManager by inject()

    private val backPressed = mutableStateOf(false)
    private val establishDataValues = EstablishData.getEstablishDataValues().toMutableMap()

    private val mCompositeDisposable: CompositeDisposable
        get() = CompositeDisposable()

    private fun addDisposable(disposable: Disposable) {
        mCompositeDisposable.addAll(disposable)
    }

    private fun unsubscribe() {
        mCompositeDisposable.clear()
    }

    override fun onStart() {
        super.onStart()
        brazeManager.openSession()

        // Refresh login each time the app is opened. We specifically do NOT want to call this until
        // LoginViewModel has been created in the typical flow of the app, otherwise we risking
        // creating bugs with the Login system being called before it is ready. See #FX-3002.
        if (LoginViewModel.instanceExists) {
            LoginViewModel.instance.tryAndRefreshLogin()
        }
    }

    override fun onStop() {
        super.onStop()
        brazeManager.closeSession()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        if (savedInstanceState != null) {
            // This should only be true when returning to the app after app permissions were
            // changed via the system settings.
            Logger.w(TAG, "onCreate() - warning: ignoring non-null savedInstanceState!")
        }
        // Always pass null so as to not attempt to recreate the app's previous state, which the
        // code does not reliably support. This means upon the system recreating the Activity for
        // whatever reason, the app will boot through its known and tested flow. See KF-1049.
        super.onCreate(null)

        //setContentView(R.layout.activity_main)

        window.decorView.setOnSystemUiVisibilityChangeListener(onSystemUiVisibilityChangeListener)
        window.decorView.setOnApplyWindowInsetsListener(onApplyWindowInsetsListener)

        activityManager.registerMainActivity(this)

        brazeManager.enableInAppMessagePresenter()

        deepLinkManager.handleIntent(intent)

        checkForShortcut(intent)

        // Trustly test
//        Logger.d(TAG, "---- BEGIN TRUSTLY TEST ----")
//        if (EstablishData.DYNAMIC_REQUEST_SIGNATURE)
//            postRequestSignature()
//        else
//            initWidget()
//        Logger.d(TAG, "---- END TRUSTLY TEST ----")

        configureWindow()
        showSystemUI()
        updateSystemBars("onCreate()")

        lifecycleScope.launch {
            tournamentManager.isInGame.collect { isInGame ->
                if (isInGame) {
                    setSystemBarState(SystemBarState.Immersive)
                    inGameBackCallback = createInGameBackCallback().also {
                        onBackPressedDispatcher.addCallback(this@MainActivity, it)
                    }
                } else {
                    inGameBackCallback?.also {
                        it.remove()
                        inGameBackCallback = null
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        brazeManager.resumeActivity()
        siftManager.resume()
        EventBus.getDefault().post(OnReturnToMainActivityEvent(true))
        updateSystemBars("onResume()")
        brazeManager.clearNotifications()
    }

    override fun onPause() {
        super.onPause()
        brazeManager.pauseActivity()
        siftManager.pause()
    }

    override fun onDestroy() {
        super.onDestroy()
        siftManager.close()
        unsubscribe()
        activityManager.unregisterMainActivity(this)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        checkForShortcut(intent)
    }

    override fun setMainTheme() {
        // Set to black to fix #fx-1372.
        window.setBackgroundDrawableResource(R.drawable.black)
        updateSystemBars("setMainTheme()")
    }

    override fun bringToFront() {
        val intent = Intent(this, MainActivity::class.java)
        intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
        startActivity(intent)
    }

    override fun openURL(url:String){
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        this.startActivity(intent)
    }

    @Composable
    override fun AppEntryPoint(){
        val navHostController = rememberNavController()
        val showDebugMenu = remember { mutableStateOf(false) }

        navManager.registerNavController(navHostController){ route ->
            if (route == Routes.DEBUG_MENU_SHEET) {
                showDebugMenu.value = true

                //Explicitly tell we handled the "route"
                return@registerNavController true
            }

            return@registerNavController false
        }

        CompositionLocalProvider(
            LocalNavManager provides navManager,
            LocalAlertDialogManager provides alertDialogManager,
            LocalClipboardManager provides clipboardManager,
            LocalModalPopupManager provides modalPopupManager,
            LocalErrorManager provides errorManager,
            LocalGeoAlertModel provides geoAlertModel,
            LocalToastManager provides toastManager,
            LocalLoadingManager provides loadingManager,
            LocalUIManager provides this,
        ) {
            AppandroidwwTheme {
                Surface(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.White)
                ) {
                    Box(modifier = Modifier.detectMultiTap(numberOfTapsRequired = 4) {
                        if(AppEnv.current.env != ENV.PROD) {
                            showDebugMenu.value = true
                            FDManager.instance.refreshSessionExpiryValueForCurrentSession()
                        }
                    }) {
                        HandleBackPress(navHostController)
                        AppNavigationGraph(navHostController)
                        LeaderboardScreen(LeaderboardViewModel.instance)
                        ModalPopup(
                            modalData = modalPopupManager.modalState.value
                        )
                        AlertDialogPopup(
                            dialogData = alertDialogManager.dialogState.value,
                        )
                        AlertGeo()
                        LoadingScreen(show = loadingManager.isShowing())
                        DebugMenuScreen(show = showDebugMenu)
                        DailyRewardEndView()
                        ReplayPopup()
                    }
                }
            }
        }
    }

    private var inGameBackCallback: OnBackPressedCallback? = null
    private fun createInGameBackCallback(): OnBackPressedCallback = object : OnBackPressedCallback(true) {

        override fun handleOnBackPressed() {
            Logger.d("[back]", "inGameBackCallback - handleOnBackPressed()")

            backPressed.value = true
        }
    }

    @Composable
    fun HandleBackPress(navController: NavHostController) {
        if(backPressed.value) {
            backPressed.value = false

            //hide geo alert
            if(tournamentManager.handleBackButton()) {
                return
            }
            if(tournamentManager.unityProvider?.isShowingUnity() == true){
                val nav = JSONObject()
                nav.put("navigationType","rewards")
                tournamentManager.unityProvider?.hideUnity(nav.toString(),null)
            }else if (geoAlertModel.getGeoBlockedAlertScreen() != GeoAlertType.GEO_ALERT_NONE) {
                geoAlertModel.showHideGeoBlockedAlert(GeoAlertType.GEO_ALERT_NONE)
            } else if (alertDialogManager.isShowing()) { // hide alert dialog
                alertDialogManager.dismissDialog()
            } else if (modalPopupManager.isShowing()) { // hide modal popup
                modalPopupManager.dismissModal()
            } else if (LeaderboardViewModel.instance.isProfileShowing()) {
                LeaderboardViewModel.instance.closeProfile()
            } else if(LeaderboardViewModel.instance.isShowingReplay()){
                LeaderboardViewModel.instance.closeReplay()
            } else if (LeaderboardViewModel.instance.isShowing()) {
                LeaderboardViewModel.instance.closeLeaderboard()
            } else if (navController.currentBackStackEntry != null && !navController.navigateUp()) { //navigate up on navController or close app
                finishAffinity()
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {

            backPressed.value = true
            return true // Prevents the event from propagating further
        }
        return super.onKeyDown(keyCode, event)
    }

    fun showToast(message: String): Toast? {
        val text: CharSequence = message
        val duration = Toast.LENGTH_SHORT
        val toast = Toast.makeText(MainApplication.context.applicationContext, text, duration)
        toast.show()
        return toast
    }

    override val useDarkStatusBarIcons = MutableStateFlow(false)

//    override fun setStatusBarLightness(light : Boolean) {
//        Logger.d("[statusbar]", "setStatusBarLightness(light = $light)")
//        useDarkStatusBarIcons.value = !light
////        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
////            window.decorView.systemUiVisibility = if (light) {
////                window.decorView.systemUiVisibility and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv() // Light text
////            } else {
////                window.decorView.systemUiVisibility or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR // Dark text
////            }
////        }
//    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        EventBus.getDefault().post(RequestPermissionsResultEvent(requestCode = requestCode, permissions = permissions, grantResults = grantResults))
    }

    private fun postRequestSignature() {
        val retrofitClient = RetrofitClientData.getClient()
        addDisposable(retrofitClient.postRequestSignature(establishDataValues)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .doOnTerminate {
                initWidget()
            }
            .subscribe({
                val requestSignature = it.string()
                Logger.i(TAG, "Dynamic requestSignature: $requestSignature")
                establishDataValues["requestSignature"] = requestSignature
            }, {
                Logger.e(TAG, it.message, it)
            })
        )
    }

    fun initWidget() {
        val payWithMyBankWidget = findViewById<TrustlyView>(R.id.trustlyWidget)
        payWithMyBankWidget?.selectBankWidget(establishDataValues)?.onBankSelected { _, data ->
            establishDataValues[PAYMENT_PROVIDER_ID] = data[PAYMENT_PROVIDER_ID].toString()
            openLightbox()
        }
    }

    fun openLightbox() {
        val intent = Intent(this@MainActivity, LightBoxActivity::class.java)
        intent.putExtra(LightBoxActivity.ESTABLISH_DATA, establishDataValues as Serializable)
        startActivity(intent)
    }

    override fun openUrlInExternalBrowser(url: String) {
        val webpage: Uri = Uri.parse(url)
        val intent = Intent(Intent.ACTION_VIEW, webpage)

        // Verify that the intent will resolve to an activity
        if (intent.resolveActivity(packageManager) != null) {
            startActivity(intent)
        } else {
            // Handle the situation where no activity can handle the intent
            Toast.makeText(this, "No application can handle this request. Please install a web browser.", Toast.LENGTH_LONG).show()
        }
    }

    private fun updateSystemBars(via: String) {
//        showImmersiveSystemBars(showImmersiveSystemBars)
    }

    fun showImmersiveSystemBars(showImmersiveSystemBars: Boolean) {
        if (isFinishing) {
            Logger.w("[statusbar]", "isFinishing - early exit")
            return
        }

        Logger.d("[statusbar]", "showImmersiveSystemBars($showImmersiveSystemBars)")

        if (showImmersiveSystemBars) {
            hideSystemUI()
        } else {
            showSystemUI()
        }
    }

    private fun configureWindow() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS or
                WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
        window.statusBarColor = getColor(android.R.color.transparent)
        window.navigationBarColor = getColor(android.R.color.transparent)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            // Prevent jumping of the player on devices with cutout
            window.attributes.layoutInDisplayCutoutMode =
                WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
        }
    }

    private fun setLightStatusBarIcons(lightStatusBarIcons : Boolean) {
        useDarkStatusBarIcons.value = !lightStatusBarIcons
//        window.decorView.systemUiVisibility = if (lightStatusBarIcons) {
//            window.decorView.systemUiVisibility and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv() // Light text
//        } else {
//            window.decorView.systemUiVisibility or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR // Dark text
//        }

//        val controller = WindowInsetsControllerCompat(window, window.decorView)
//        controller.isAppearanceLightStatusBars = lightStatusBarIcons
//        controller.isAppearanceLightNavigationBars = lightStatusBarIcons

    }

    private val systemBarState: MutableStateFlow<SystemBarState?> = MutableStateFlow(null)
    override fun setSystemBarState(systemBarState: SystemBarState) {
        val lastSystemBarState = this.systemBarState.value
        val lastStateWasImmersive = lastSystemBarState == SystemBarState.Immersive
        val stateIsImmersive = systemBarState == SystemBarState.Immersive
        val immersiveModeChanged = lastStateWasImmersive != stateIsImmersive

        if (lastSystemBarState == systemBarState) {
            return
        }

        Logger.d("[statusbar]", "setSystemBarState(): $lastSystemBarState -> $systemBarState, immersiveModeChanged = $immersiveModeChanged")
        this.systemBarState.value = systemBarState

        when (systemBarState) {
            SystemBarState.Immersive -> {
                if (immersiveModeChanged) {
                    showImmersiveSystemBars(true)
                }
                setLightStatusBarIcons(lightStatusBarIcons = true)
            }
            SystemBarState.TransparentLightStatusBarIcons -> {
                if (immersiveModeChanged) {
                    showImmersiveSystemBars(false)
                }
                setLightStatusBarIcons(lightStatusBarIcons = true)
            }
            SystemBarState.TransparentDarkStatusBarIcons -> {
                if (immersiveModeChanged) {
                    showImmersiveSystemBars(false)
                }
                setLightStatusBarIcons(lightStatusBarIcons = false)
            }
        }
        updateNavigationBarColor()
    }

    val windowInsetsController by lazy {
        WindowCompat.getInsetsController(window, window.decorView)
            .also {
                it.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
    }

    fun hideSystemUI() {
        Logger.w("[statusbar]", "hideSystemUI")

        windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())

//        window.decorView.systemUiVisibility =
//            (View.SYSTEM_UI_FLAG_IMMERSIVE
//                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
//                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
//                    or View.SYSTEM_UI_FLAG_LOW_PROFILE
//                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
//                    or View.SYSTEM_UI_FLAG_FULLSCREEN)

        window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
    }

    fun showSystemUI() {
        Logger.w("[statusbar]", "showSystemUI")
//        window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
//                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)

        windowInsetsController.show(WindowInsetsCompat.Type.systemBars())

        window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
    }

    private fun updateSystemUI() {
        if ((mIsFullScreen)) {
            hideSystemUI()
        } else {
            showSystemUI()
        }
    }


    private var mIsFullScreen = false
    private val onSystemUiVisibilityChangeListener = OnSystemUiVisibilityChangeListener { visibility ->
        if ((visibility and View.SYSTEM_UI_FLAG_LOW_PROFILE) == 0) {
            mIsFullScreen = false
        } else {
            mIsFullScreen = ((visibility and View.SYSTEM_UI_FLAG_FULLSCREEN) != 0)
        }
        Logger.w("[statusbar]", "onSystemUiVisibilityChangeListener: mIsFullScreen = $mIsFullScreen")
        updateSystemUI()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        recreate()
    }

    var hasWindowFocus = true
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        Logger.w("[statusbar]", "onWindowFocusChanged(hasFocus: $hasFocus)")
        hasWindowFocus = hasFocus

        if (hasWindowFocus && systemBarState.value == SystemBarState.Immersive) {
            updateSystemBars("onWindowFocusChanged()")
            showImmersiveSystemBars(true)
        }

        updateSystemBars("onWindowFocusChanged()")
    }

    override val navigationBarColor: MutableStateFlow<Color> = MutableStateFlow(Color.Transparent)
    private fun updateNavigationBarColor() {
        val navigationBarColor = if (Build.VERSION.SDK_INT >= 30) {
            Color.Transparent
        } else {
            when (systemBarState.value) {
                SystemBarState.Immersive -> Color.Transparent
                else -> Color.White//Color.Red
            }
        }
        this.navigationBarColor.value = navigationBarColor
    }

    private val onApplyWindowInsetsListener =
        View.OnApplyWindowInsetsListener { v, insets ->
            Logger.d("[statusbar]", "onApplyWindowInsetsListener: $insets")
//            insets.consumeSystemWindowInsets()
            insets
        }

    private fun checkForShortcut(intent: Intent) {
        val shortcutId = intent.getStringExtra("SHORTCUT_ID") ?: return

        if (shortcutId == "open_debug_activity") {
            EnvironmentDebugControllerImpl.open(this)
            return
        }
    }

}

