package com.gametaco.app_android_fd.environment.debug

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun EnvironmentDebugContent(
    uiState: EnvironmentDebugUiState,
) {
    var textFieldValue by remember { mutableStateOf(uiState.envName) }
    var endpointUrlValue by remember { mutableStateOf(uiState.endpointUrl) }
    var maintenanceUrlValue by remember { mutableStateOf(uiState.maintenanceUrl) }
    
    // Update local state when UI state changes (e.g., from reset)
    LaunchedEffect(uiState.endpointUrl) {
        endpointUrlValue = uiState.endpointUrl
    }
    LaunchedEffect(uiState.maintenanceUrl) {
        maintenanceUrlValue = uiState.maintenanceUrl
    }
    
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = uiState.title,
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(bottom = 24.dp)
            )
            
            // Environment Section
            Text(
                text = uiState.environmentTitle,
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.Bottom
            ) {
                OutlinedTextField(
                    value = textFieldValue,
                    onValueChange = { textFieldValue = it },
                    label = { Text(uiState.textFieldLabel) },
                    placeholder = { Text(uiState.textFieldPlaceholder) },
                    modifier = Modifier.weight(1f),
                    singleLine = true
                )
                
                Button(
                    onClick = {
                        uiState.onApplyHostName(textFieldValue)
                    }
                ) {
                    Text(uiState.applyLabel)
                }
            }

            Text(
                text = uiState.currentLabel,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(top = 16.dp),
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Hostnames Section
            Text(
                text = uiState.hostnamesTitle,
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // Endpoint URL Field
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.Bottom
            ) {
                OutlinedTextField(
                    value = endpointUrlValue,
                    onValueChange = { endpointUrlValue = it },
                    label = { Text(uiState.endpointUrlLabel) },
                    placeholder = { Text(uiState.endpointUrlPlaceholder) },
                    modifier = Modifier.weight(1f),
                    singleLine = true
                )
                
                Button(
                    onClick = {
                        uiState.onApplyEndpointUrl(endpointUrlValue)
                    }
                ) {
                    Text(uiState.applyLabel)
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Maintenance URL Field
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.Bottom
            ) {
                OutlinedTextField(
                    value = maintenanceUrlValue,
                    onValueChange = { maintenanceUrlValue = it },
                    label = { Text(uiState.maintenanceUrlLabel) },
                    placeholder = { Text(uiState.maintenanceUrlPlaceholder) },
                    modifier = Modifier.weight(1f),
                    singleLine = true
                )
                
                Button(
                    onClick = {
                        uiState.onApplyMaintenanceUrl(maintenanceUrlValue)
                    }
                ) {
                    Text(uiState.applyLabel)
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Reset Button
            Button(
                onClick = uiState.onResetHostnames,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(uiState.resetButtonLabel)
            }
        }
    }
}
