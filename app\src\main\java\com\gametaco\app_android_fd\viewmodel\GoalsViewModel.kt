package com.gametaco.app_android_fd.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APICollectedGoal
import com.gametaco.app_android_fd.data.entity.APICompletableGoal
import com.gametaco.app_android_fd.data.entity.APIDailyDoorStatus
import com.gametaco.app_android_fd.data.entity.APIDailyDoorStatusRequest
import com.gametaco.app_android_fd.data.entity.APIGoalRewardType
import com.gametaco.app_android_fd.data.entity.APIGoalType
import com.gametaco.app_android_fd.data.entity.APIGoalsCollectableGoal
import com.gametaco.app_android_fd.data.entity.APIGoalsCollectableRequest
import com.gametaco.app_android_fd.data.entity.APIPostGoalsCollectableResponse
import com.gametaco.app_android_fd.data.entity.DailyRewardInfoType
import com.gametaco.app_android_fd.data.navigation.NavigationData
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.AlertDialogManager
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.ErrorManager
import com.gametaco.app_android_fd.manager.ExperimentManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.WalletManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.manager.analytics.getRewardNameEvent
import com.gametaco.app_android_fd.manager.analytics.getRewardSelectionValue
import com.gametaco.app_android_fd.models.GoalDataModel
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.utils.toDate
import com.gametaco.app_android_fd.utils.toDollarString
import com.gametaco.app_android_fd.utils.toNumberWithCommaString
import com.gametaco.utilities.ResourceState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.Date

data class OnReLoadGoalsDataEvent(val refresh: Boolean)

class GoalsViewModel: ViewModel() {
    val isFtue = AuthenticationManager.instance.showRewardsFtue


    private val _refresh : MutableStateFlow<Boolean>

    private val _goals : MutableStateFlow<ResourceState<GoalDataModel>>
    val goals : StateFlow<ResourceState<GoalDataModel>>

    private val _dailyDoorStatus:MutableStateFlow<APIDailyDoorStatus?>
    val dailyDoorStatus:StateFlow<APIDailyDoorStatus?>

    private val _isDailyRewardAvailable:MutableStateFlow<Boolean>
    val isDailyRewardAvailable:StateFlow<Boolean>

    private val _showDailyRewardEndPopup:MutableStateFlow<Boolean>
    val showDailyRewardEndPopup:StateFlow<Boolean>

    private val _showReplayPopup:MutableStateFlow<Boolean>
    val showReplayPopup:StateFlow<Boolean>

    private val _collectableCount : MutableStateFlow<Int>
    val collectableCount : StateFlow<Int>

    private val _lifetimeTotal : MutableStateFlow<Float>
    val lifetimeTotal : StateFlow<Float>

    init {
        Logger.d(TAG, "Init GoalsViewModel")

        _refresh = MutableStateFlow(false)
        _goals = MutableStateFlow(ResourceState.Loading())
        goals = _goals.asStateFlow()

        _dailyDoorStatus = MutableStateFlow(null)
        dailyDoorStatus = _dailyDoorStatus.asStateFlow()

        _isDailyRewardAvailable = MutableStateFlow(false)
        isDailyRewardAvailable = _isDailyRewardAvailable.asStateFlow()

        _showDailyRewardEndPopup = MutableStateFlow(false)
        showDailyRewardEndPopup = _showDailyRewardEndPopup.asStateFlow()

        _showReplayPopup = MutableStateFlow(false)
        showReplayPopup = _showReplayPopup.asStateFlow()

        _collectableCount = MutableStateFlow(0)
        collectableCount = _collectableCount.asStateFlow()


        _lifetimeTotal = MutableStateFlow(0f)
        lifetimeTotal = _lifetimeTotal.asStateFlow()

        if(!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this)

//        EventBus.getDefault().post(OnReLoadGoalsDataEvent(true))
    }
    fun openDailyRewardEndPopup(){
        _showDailyRewardEndPopup.value = true
    }
    fun closeDailyRewardEndPopup(){
        _showDailyRewardEndPopup.value = false
    }
    fun openReplayPopup(){
        _showReplayPopup.value = true
        PreferencesManager.instance.setIsReplayViewed()
    }
    fun closeReplayPopup(){
        _showReplayPopup.value = false
        checkAndNavigateToDailyRewardIfAvailable()
    }
    fun addLifeTimeReward(value:Float){
        _lifetimeTotal.value += value
    }

    fun needRefresh():Boolean{
        return _refresh.value
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onReLoadGoalsDataEventHandler(event: OnReLoadGoalsDataEvent) {
        fetchGoals()
    }

    fun fetchGoals()
    {
        if(AuthenticationManager.instance.authToken == null || !FDManager.instance.hasSessionData)
            return

        //don't do this in guest mode
        if(AuthenticationManager.instance.isGuest)
            return

        _refresh.value = false
        _goals.value = ResourceState.Loading()

        viewModelScope.launch (Dispatchers.IO) {
            val goalDataModel = GoalDataModel(
                completableGoals = fetchCompletable(),
                collectableGoals = fetchCollectable(),
                collectedGoals = fetchCollected(),
            )
            calculateBadges(goalDataModel)
            mapClusterGoals(goalDataModel)
//            println("goalDataModel: ${goalDataModel}")
            val goalDataModelState: ResourceState<GoalDataModel> = ResourceState.Success(goalDataModel)
            _goals.value = goalDataModelState
            _collectableCount.value = goalDataModel.collectableGoals.size

        }
        fetchDailyDoorStatus()
    }
    fun checkAndNavigateToDailyRewardIfAvailable(){
        viewModelScope.launch(Dispatchers.IO) {
            _checkAndNavigateToDailyRewardIfAvailable()
        }
    }
    private var lastDailyRewardNavigationTime:Long = 0
    private val dailyRewardCooldownDuration:Int = 3600 * 1000//1 hour

    fun resetDailyRewardCache(){
        lastDailyRewardNavigationTime = 0
        _isDailyRewardAvailable.value = false
        _dailyDoorStatus.value = null
        _showDailyRewardEndPopup.value = false
    }
    private suspend fun _checkAndNavigateToDailyRewardIfAvailable():Boolean{
        Logger.d(TAG,"_checkAndNavigateToDailyRewardIfAvailable")

        if(NavManager.instance.currentDestinationRoute != Routes.GAMES_SCREEN){
            Logger.d(TAG,"Skipping automatic navigating to daily reward because not in app home!")
            return false
        }
        if(AuthenticationManager.instance.isGuest || AuthenticationManager.instance.showLobbyFtue){
            Logger.d(TAG,"Skipping automatic navigating to daily reward because guest or ftue!")
            return false
        }
        if(!ExperimentManager.instance.isDailyRewardEnabled.value){
            // Handle end of daily reward experiment show popup
            if(PreferencesManager.instance.getDailyRewardSeen()){
                this.openDailyRewardEndPopup()
                PreferencesManager.instance.clearDailyReward()
            }
            Logger.d(TAG,"Skipping automatic navigating to daily reward due to experiment flag")
            return false
        }
        if(Date().time - lastDailyRewardNavigationTime < dailyRewardCooldownDuration){
            Logger.d(TAG,"Skipping automatic navigating to daily reward due to cooldown")
            return false
        }

        val cachedNextRewardDate = PreferencesManager.instance.getDailyRewardNextAvailableDate()
        if(cachedNextRewardDate != null){
            val currentDateString = TournamentManager.instance.unityProvider?.getDailyRewardFutureTime()
            var currentDate = currentDateString?.toDate()
            if(currentDate == null){
                Logger.d(TAG,"Using UTC now as fallback due to empty or invalid Unity date")
                currentDate = Date()
            }

            if(cachedNextRewardDate.after(currentDate)){
                Logger.d(TAG,"Skipping automatic navigation to daily reward due to cached future date")
                return false
            }
        }
        val res = WorldWinnerAPI.instance.getDailyDoorStatus(APIDailyDoorStatusRequest(
            is_digest = true,
            current_date = TournamentManager.instance.unityProvider?.getDailyRewardFutureTime()
        ))
        if (res.isSuccess()) {
            val data = (res as ResourceState.Success).data
            val next_reward_at = data.next_reward_at.toDate()
            if(next_reward_at != null){
                PreferencesManager.instance.saveDailyRewardNextAvailableDate(next_reward_at)
            }
            _isDailyRewardAvailable.value = data.isRewardAvailable
            if(data.isRewardAvailable){
                Logger.d(TAG,"Automatically navigating to daily reward")
                openDailyRewardInUnity()
                lastDailyRewardNavigationTime = Date().time
                return true
            }else{
                Logger.d(TAG,"Skipping automatic navigation to daily reward due no claimable daily reward")
            }
        }
        return false
    }
    fun openDailyRewardInUnity(){
        if(ExperimentManager.instance.isDailyRewardEnabled.value){
            Logger.d(TAG,"open DailyReward In Unity")
            TournamentManager.instance.unityProvider?.showUnityFromAction("daily")
//            PlayViewModel.instance.jumpToPlayerViewByName("puzzlepyramidskt3prime","01903770-ee44-796c-9fd4-996c92ba699b")
        }else{
            Logger.d(TAG," Skipping opening daily reward due to experiment flag")
        }
    }
    private fun fetchDailyDoorStatus(){
        viewModelScope.launch (Dispatchers.IO) {
            val res = WorldWinnerAPI.instance.getDailyDoorStatus(APIDailyDoorStatusRequest(
                is_digest = true,
                current_date = TournamentManager.instance.unityProvider?.getDailyRewardFutureTime()
            ))
            if (res.isSuccess()) {
                val data = (res as ResourceState.Success).data
                _dailyDoorStatus.value = data
                val nextRewardDate = data.nextRewardDate
                if(nextRewardDate != null){
                    PreferencesManager.instance.saveDailyRewardNextAvailableDate(nextRewardDate)
                }
                _isDailyRewardAvailable.value = data.isRewardAvailable && ExperimentManager.instance.isDailyRewardEnabled.value == true

            } else if(res.isError()){
                _dailyDoorStatus.value = null
            }
        }
    }
    private suspend fun fetchCollectable(): List<APIGoalsCollectableGoal> {
        val goalsCollectable = WorldWinnerAPI.instance.getGoalsCollectable()
        return if (goalsCollectable.isSuccess()) {
            (goalsCollectable as ResourceState.Success).data.results
        } else {
            listOf()
        }
    }
    private suspend fun fetchCompletable(): List<APICompletableGoal> {
        val goalsCompletable = WorldWinnerAPI.instance.getGoalsCompletable()
        return if (goalsCompletable.isSuccess()) {
            (goalsCompletable as ResourceState.Success).data.results
        } else {
            listOf()
        }
    }
    private suspend fun fetchCollected(): List<APICollectedGoal> {
        val goalsCollected = WorldWinnerAPI.instance.getGoalsCollected()
        val dailyDoorCollected = WorldWinnerAPI.instance.getDailyDoorCollectedRewards()
        val list = mutableListOf<APICollectedGoal>()
        var lifeTimeTotal = 0f

        if(goalsCollected.isSuccess()){
            val data = (goalsCollected as ResourceState.Success).data
            lifeTimeTotal = data.lifetime_rewards_claimed.toFloat()
            data.results.forEach {
                list.add(it)
            }
        }

        if(dailyDoorCollected.isSuccess()){
            val data = (dailyDoorCollected as ResourceState.Success).data
            data.results.forEach {
                list.add(APICollectedGoal(
                    id = it.id,
                    completed_at = it.collected_at,
                    collected_at = it.collected_at,
                    goal_title = it.name,
                    reward_type = it.reward.type,
                    reward = it.reward.value ?: "$0.00",
                    task_title = it.name,
                    was_eligible_to_claim = true,
                    is_deposit_goal = false,
                    starts_at = null,
                    ends_at = null,
                    has_ticket_reward = false,
                    has_tag_reward = false,
                    tournament = it.reward.tournament_value
                ))
                if(it.reward.type == DailyRewardInfoType.CASH){
                    lifeTimeTotal += it.reward.cash_value?.amount?.toFloat() ?:0f
                }
            }
        }
        list.sortByDescending { it.collected_at.toDate() }
        _lifetimeTotal.value = lifeTimeTotal

        return list
    }

    fun collectGoal(
        goalUserInstanceTaskId: String,
        goalType:APIGoalType?,
        isDailyReward: Boolean,
        clusterTitle: String?,
        rewardAmount:Float,
        rewardTitle:String?,
        callback: (res: APIPostGoalsCollectableResponse)->Unit,
    ){
        viewModelScope.launch (Dispatchers.IO) {
            val res = WorldWinnerAPI.instance.postGoalsCollectable(
               APIGoalsCollectableRequest(goal_user_instance_task_id = goalUserInstanceTaskId)
            )
            if (res.isSuccess()) {
                _refresh.value = true

                val claimResult = (res as ResourceState.Success).data

                _collectableCount.value -= 1
                NavigationData.instance.setNotificationCountGoals(_collectableCount.value)

//                if(_goals.value.isSuccess()){
//                    val currentDataModel = (_goals.value as ResourceState.Success).data
//                    val updatedDataModel = currentDataModel.copy(collectedGoals = fetchCollected())
//                    _goals.value = ResourceState.Success(updatedDataModel)
//                }

                val rewardType = claimResult.reward?.cash?.reward_type
                    ?.let { APIGoalRewardType.fromRawValue(it) }

                val tournamentTicket = claimResult.reward?.tournament_ticket
                val isDiscountedEntry = tournamentTicket != null

                AnalyticsManager.instance.logEvent(
                    AnalyticsEvent(
                        analyticsEvent = AnalyticsEventName.Reward_Claimed.value,
                        properties = mapOf(
                            "Reward Selection" to (goalType?.getRewardSelectionValue(clusterTitle, isDailyReward = isDailyReward) ?: "<null>"),
                            "Reward Name" to (rewardType?.getRewardNameEvent(isDiscountedEnty = isDiscountedEntry) ?: "<null>"),
                            "Reward Amount" to rewardAmount.toNumberWithCommaString().toDollarString(),
                            "Reward Title" to (rewardTitle ?: "Unknown")
                        )
                    )
                )

                if(!claimResult.was_eligible_to_claim){
                    AlertDialogManager.instance.showDialog(
                        title = "Not eligible to claim reward",
                        message = "Must be age 21+ to obtain Sportsbook Bonus Bets.",
                        confirmButtonText = "Got it",
                        onConfirm = {}
                    )
                }else{
                    //there is delay for the balance update
                    viewModelScope.launch (Dispatchers.IO) {
                        for (i in 1..3){
                            Thread.sleep(2000)
                            WalletManager.instance.refreshWallet()
                        }
                    }
                }
                callback(claimResult)
            }
            else if(res.isError()){
                val error = (res as ResourceState.Error).error
                if(!ErrorManager.instance.handleResourceStateError(res)) {
                    AlertDialogManager.instance.showDialog(
                        title = "Claim Error",
                        message = error,
                        confirmButtonText = "Confirm",
                        onConfirm = {}
                    )
                }
            }

        }
    }
    private fun calculateBadges(goalDataModel: GoalDataModel){
        val lastSeen = PreferencesManager.instance.getGoalsScreenLastSeen()
        Logger.d("lastSeen: ${lastSeen}")
        val count = goalDataModel.completableGoals.count {
            val collectedTime = it.starts_at?.toDate()
            collectedTime?.after(lastSeen) == true
        }
        NavigationData.instance.setNotificationCountGoals(count + goalDataModel.collectableGoals.size)
    }
    private fun mapClusterGoals(goalDataModel: GoalDataModel){
        val clusterGoals = goalDataModel.completableGoals.filter { it.goalType == APIGoalType.Cluster }
        val collectableClusterGoals = goalDataModel.collectableGoals.filter {  it.goalType == APIGoalType.Cluster }
        val collectableClusterMap = mutableMapOf<String,APIGoalsCollectableGoal>()
        for (goal in collectableClusterGoals){
            collectableClusterMap.put(goal.goal_task_id,goal)
        }
        //we need do a mapping to find the task's real goal_user_instance_task_id, which is actually coming from the collectable goals
        for (goal in clusterGoals){
            for (task in goal.tasks){
                if(collectableClusterMap.containsKey(task.id)){
                    task.goal_user_instance_task_id = collectableClusterMap[task.id]?.goal_user_instance_task_id
//                    println("find one:" + task.goal_user_instance_task_id)
                }
            }
        }
    }
    companion object {
        const val TAG = "GoalsViewModel"
        val instance: GoalsViewModel by lazy { GoalsViewModel() }
    }
}

