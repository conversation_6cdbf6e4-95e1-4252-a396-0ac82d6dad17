package com.gametaco.app_android_fd.manager.analytics

import com.gametaco.app_android_fd.data.entity.APIGoalRewardType
import com.gametaco.app_android_fd.data.entity.APIGoalType

fun APIGoalType.getRewardSelectionValue(clusterName: String?, isDailyReward: Boolean): String =
    if (isDailyReward) {
        "Daily Reward"
    } else {
        when (this) {
            APIGoalType.Chain,
            APIGoalType.Standard
                -> {
                eventPropertyPrefix
            }

            APIGoalType.Cluster -> {
                "$eventPropertyPrefix $clusterName"
            }
        }
    }

fun APIGoalRewardType.getRewardNameEvent(isDiscountedEnty: Boolean): String =
    if (isDiscountedEnty) {
        "faceoff_discounted_entries"
    } else {
        this.eventProperty
    }