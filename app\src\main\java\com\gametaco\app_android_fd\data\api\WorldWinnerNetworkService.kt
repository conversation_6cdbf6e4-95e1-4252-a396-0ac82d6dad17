
package com.gametaco.app_android_fd.data.api

import com.fanduel.coremodules.px.CorePx
import com.gametaco.app_android_fd.data.AppConstants
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.data.LoggerInitializerImpl
import com.gametaco.app_android_fd.data.isNonProd
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.DeviceManager
import com.gametaco.app_android_fd.manager.FDManager
import com.gametaco.app_android_fd.utils.log.HttpLoggingInterceptor
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import retrofit2.Invocation
import retrofit2.Retrofit
import retrofit2.converter.moshi.MoshiConverterFactory

class WorldWinnerNetworkService {

    private var retrofit: Retrofit? = null

    fun invalidRetrofit() {
        retrofit = null
    }

    fun getApiService(): ApiService = getRetrofit().create(ApiService::class.java)

    private fun getRetrofit(): Retrofit {
        return retrofit ?: buildRetrofit().also { retrofit = it }
    }

    private fun buildRetrofit(): Retrofit {
        //this is here to prime the result of DeviceManager.instance.getUserAgent() because it can't be called on the
        //headerInterceptor thread
        DeviceManager.instance.getUserAgent()

        return Retrofit.Builder()
            .baseUrl(AppEnv.current.api_endpoint)
            .client(buildHttpClient())
            .addConverterFactory(buildMoshiConverter())
            .build()
    }

    private fun buildHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(buildHeaderInterceptor())
            .apply {
                LoggerInitializerImpl.loggingEnabled
                    .takeIf { it }
                    ?.let { addInterceptor(HttpLoggingInterceptor().apply { level = HttpLoggingInterceptor.Level.BODY }) }
                addInterceptor(UnauthorizedInterceptor())
                addInterceptor(CorePx.instance.getPxInterceptor())
            }
            .build()
    }

    private fun buildHeaderInterceptor(): Interceptor = Interceptor { chain ->
        val request = chain.request()
        val builder = request.newBuilder()
            .header("Content-Type", "application/json")
            .header("x-application-id", AppConstants.API_APP_ID)
            .header("x-application-version-id", AppConstants.API_APP_VERSION)
            .header("x-device-native-id", DeviceManager.instance.getDeviceId() ?: "")
            .header("User-Agent", DeviceManager.instance.getUserAgent() ?: "Unknown User Agent")

        AppEnv.current.takeIf { it.env.isNonProd }?.envHost?.envHeader?.let {
            builder.header("x-fanduel-env", it)
        }

        val method = request.tag(Invocation::class.java)?.method()
        method?.annotations?.forEach { annotation ->
            if (annotation is WithAuthentication) {
                AuthenticationManager.instance.authToken?.let {
                    builder.header("Authorization", "Token $it")
                }
                FDManager.instance.sessionDataToken?.let {
                    builder.header("x-fanduel-auth-token", it)
                }
            }
        }

        chain.proceed(builder.build())
    }

    private fun buildMoshiConverter(): MoshiConverterFactory {
        val moshi = Moshi.Builder()
            .add(KotlinJsonAdapterFactory())
            .build()
        return MoshiConverterFactory.create(moshi)
    }
}
