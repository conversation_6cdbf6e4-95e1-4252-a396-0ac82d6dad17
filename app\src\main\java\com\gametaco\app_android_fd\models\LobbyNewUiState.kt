package com.gametaco.app_android_fd.models

import com.gametaco.app_android_fd.data.entity.APICatalog
import com.gametaco.app_android_fd.data.entity.APIGamesResponse
import com.gametaco.app_android_fd.data.entity.APIGamesResponseGame
import com.gametaco.app_android_fd.data.entity.APITournament
import com.gametaco.app_android_fd.data.entity.GCGameData
import com.gametaco.app_android_fd.data.entity.GameCatalogResponse
import com.gametaco.app_android_fd.manager.NavManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

sealed interface LobbyNewUiState {

    data object Loading : LobbyNewUiState

    data class Error(
        val errorMessage: String,
    ) : LobbyNewUiState

    data class Success(
        val catalog: APICatalog,
        val gamesData: APIGamesResponse,
        val currentTournamentData: StateFlow<APITournament?>,
        val selectedGameData: StateFlow<APIGamesResponseGame?>,
        val infoModalExpanded: MutableStateFlow<Boolean>,
        val gameModalExpanded: MutableStateFlow<Boolean>,
        val onInfoClick: (APITournament, GCGameData?) -> Unit,
        val onGameTileClicked: (navManager: NavManager, gameData: GCGameData, lobbySelection: String?) -> Unit,
    ) : LobbyNewUiState
}