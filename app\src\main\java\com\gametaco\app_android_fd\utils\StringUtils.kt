package com.gametaco.app_android_fd.utils

import android.text.Spanned
import androidx.compose.material3.LocalTextStyle
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.ParagraphStyle
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.core.text.HtmlCompat
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import kotlin.math.floor

fun String.toDollarString(): String {
    return "$$this"
}

fun Double.toDollarWithCommaString():String{
    return this.toString().toDollarWithCommaString()
}
fun Float.toDollarWithCommaString():String{
    return this.toString().toDollarWithCommaString()
}
fun String.toDollarWithCommaString(forceDecimal :Boolean = false) :String {
    return toNumberWithCommaString(includeDecimals = if(forceDecimal) true else getIncludeDecimals()).toDollarString()
}

fun Double.toNumberWithCommaString() :String {
    return this.toString().toNumberWithCommaString()
}
fun Float.toNumberWithCommaString() :String {
    return this.toString().toNumberWithCommaString()
}
fun Int.toNumberWithCommaString() :String {
    return this.toString().toNumberWithCommaString(includeDecimals = false)
}

fun String.getIncludeDecimals():Boolean {
    // Only show decimals if < 100. See KF-1086.
    return this.toDouble() < 100
}

fun String.toDollarWithFanduelFormatting(): String {
    // NOTES: bottom footer logic is rounding down
    // limit all bottom nav digits to 4 or less. this means:
    //
    //$0 - $99.99 : show cents (i.e. $9.99, $99.99)
    //
    //$100 - $999 : no cents, round down (i.e. $590)
    //
    //$1000 - $9999 : no cents, round down with comma (i.e. $1,032)
    //
    //>$10,000 - $99,999 : add K (i.e. $99K)

    return try {
        val amount = this.toDouble().let {
            if (it > 100.0) {
                it.toInt().toDouble()
            } else {
                it
            }
        }

        val formatter = if (amount < 100) {
            DecimalFormat("#,##0.00")
        } else if (amount >= 10000) {
            return this.toDollarKStringFormat()
        } else {
            DecimalFormat("#,##0")
        }
        formatter.format(amount).toDollarString()
    } catch (e: NumberFormatException) {
        return this.toDollarString()
    }
}

fun String.toNumberWithCommaString(includeDecimals: Boolean = getIncludeDecimals()): String {
    return try {
        val amount = this.toDouble()
        val formatter = if (includeDecimals) {
            DecimalFormat("#,##0.00")
        } else {
            DecimalFormat("#,##0")
        }
        formatter.format(amount)
    } catch (e: NumberFormatException) {
        return this.toDollarString()
    }
}
fun String.toDollarKStringFormat(): String{
    val amount = this.toDouble()
    return when {
        amount < 100 -> String.format("$%.2f", amount)
        amount < 10_000 -> String.format("$%,.0f", amount)
        amount < 1_000_000 -> String.format("$%.0fK", floor(amount / 1_000))
        else -> String.format("$%.2fM", floor(amount / 1_000_000))
    }
}

fun String.toDate(utc:Boolean = true): Date? {
    val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
    if(utc){
        dateFormat.timeZone = TimeZone.getTimeZone("UTC") // Set the timezone to UTC to handle the 'Z'
    }else{
        dateFormat.timeZone = TimeZone.getDefault()
    }

    return try {
        dateFormat.parse(this)
    } catch (e: Exception) {
        null
    }
}
@Composable
fun String.parseHtml(): AnnotatedString {
    var text = this

    val annotatedString = buildAnnotatedString {
        val regex = Regex("<(/?)(b|i|u|s|h[1-6]|p|br)>")
        var lastIndex = 0

        var style: TextStyle = LocalTextStyle.current

        regex.findAll(text).forEach { matchResult ->
            val index = matchResult.range.first

            val appendingText = text.substring(lastIndex, index)
            if (appendingText.length != 1 || !appendingText[0].isWhitespace()) {
                append(text.substring(lastIndex, index))
            }
            tagToStyle(this,matchResult.value,style)
            lastIndex = matchResult.range.last + 1
        }

        append(text.substring(lastIndex, text.length))
    }
    return annotatedString
}
private fun tagToStyle(builder:AnnotatedString.Builder,tag: String,style: TextStyle) {
    with(builder){
        when (tag) {
            "<b>" -> pushStyle(SpanStyle(fontWeight = FontWeight.Bold))
            "</b>" -> pop()
            "<i>" -> pushStyle(SpanStyle(fontStyle = FontStyle.Italic))
            "</i>" -> pop()
            "<u>" -> pushStyle(SpanStyle(textDecoration = TextDecoration.Underline))
            "</u>" -> pop()
            "<s>" -> pushStyle(SpanStyle(textDecoration = TextDecoration.LineThrough))
            "</s>" -> pop()
            "<p>" -> {
                append("\n")
                pushStyle(ParagraphStyle(lineHeight = style.fontSize * 1.5f))
            }

            "</p>" -> {
                pop()
                append("\n")
            }

            "<br>" -> append("\n")

            in listOf("<h1>", "<h2>", "<h3>", "<h4>", "<h5>", "<h6>") -> {
                val fontSize = when (tag) {
                    "<h1>" -> style.fontSize * 2f
                    "<h2>" -> style.fontSize * 1.5f
                    "<h3>" -> style.fontSize * 1.25f
                    "<h4>" -> style.fontSize * 1.15f
                    "<h5>" -> style.fontSize * 1.05f
                    else -> style.fontSize
                }
                pushStyle(SpanStyle(fontSize = fontSize, fontWeight = FontWeight.Bold))
            }

            in listOf("</h1>", "</h2>", "</h3>", "</h4>", "</h5>", "</h6>") -> pop()
            else -> {

            }
        }
    }
}

fun String.fromHtml(): Spanned = HtmlCompat.fromHtml(this, HtmlCompat.FROM_HTML_MODE_LEGACY)
