package com.gametaco.app_android_fd.ui.modifiers

import android.graphics.BlurMaskFilter
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.ClipOp
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.DefaultShadowColor
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.addOutline
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

fun Modifier.shadowWithClippingBlurMask(
    elevation: Dp,
    shape: Shape = RoundedCornerShape(8.dp),
    spotColor: Color = DefaultShadowColor,
    transparency: Float = 0.25f,              // adjust the transparency for more natural shadows
    elevationToBlurMultiplier: Float = 1.2f   // Match the blur radius to the elevation
): Modifier = this
    .drawWithCache {
        val transparentColor = spotColor.copy(alpha = transparency)
        val outline = shape.createOutline(size, layoutDirection, this)
        val path = Path().apply { addOutline(outline) }

        // Use a more restrained blur radius, closer to the default shadow
        val blurRadius = elevation.toPx() * elevationToBlurMultiplier
        //apply light direction
        val dxPx = 0f  // No horizontal offset for default Material shadow
        val dyPx = (elevation * 0.5f).toPx()  // Vertical offset

        val shadowPaint = Paint().apply {
            asFrameworkPaint().apply {
                isAntiAlias = false
                maskFilter = BlurMaskFilter(blurRadius, BlurMaskFilter.Blur.NORMAL)
                color = transparentColor.toArgb()
            }
        }

        onDrawWithContent {
            drawIntoCanvas { canvas ->
                canvas.save()

                clipPath(path, ClipOp.Difference) {
                    canvas.translate(dxPx, dyPx)  // Apply vertical shadow offset
                    canvas.drawPath(path, shadowPaint)
                }
                canvas.restore()
            }
            drawContent()
        }
    }
