package com.gametaco.app_android_fd.ui.components

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.ripple.LocalRippleTheme
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.gametaco.app_android_fd.data.navigation.NavigationData
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.HapticFeedbackType
import com.gametaco.app_android_fd.manager.HapticsManager
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.WalletManager
import com.gametaco.app_android_fd.manager.MockModeManager
import com.gametaco.app_android_fd.manager.OnSessionChangedEvent
import org.greenrobot.eventbus.EventBus
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.models.NavigationDataModel
import com.gametaco.app_android_fd.ui.modifiers.onAppear
import com.gametaco.app_android_fd.ui.theme.AppandroidwwTheme
import com.gametaco.app_android_fd.ui.theme.NoRippleTheme
import com.gametaco.app_android_fd.ui.utils.getNavigationBarHeight
import com.gametaco.app_android_fd.utils.toDollarWithFanduelFormatting
import com.gametaco.app_android_fd.viewmodel.GamesViewModel
import com.gametaco.utilities.STR
import resources.R

const val TAG_NAV_BAR = "NavBar"

@Composable
fun NavBar(navData: NavigationData, navManager: NavManager?)
{
    val TAG = TAG_NAV_BAR
    //var wasOnContestScreen by remember { mutableStateOf(false) }
    CompositionLocalProvider(LocalRippleTheme provides NoRippleTheme) {
        Column (
            modifier = Modifier.background(MaterialTheme.colorScheme.surface)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(color = MaterialTheme.colorScheme.outline)
                    .height(1.dp)
            )
            NavigationBar(
                containerColor = Color.Transparent,
                modifier = Modifier
                    .padding(start = 12.dp, end = 12.dp, top = 0.dp, bottom = 0.dp)
                    .onAppear {
                        WalletManager.instance.refreshWallet()
                    },
                windowInsets = WindowInsets(0.dp)
            ) {
                val wallet by WalletManager.instance.wallet.collectAsState()
                val isWalletValid by WalletManager.instance.isWalletValid.collectAsState()
                val navState by navData.stateData.collectAsState()

                val destContestScreen = navManager?.currentDestinationRoute == Routes.CONTEST_SCREEN

                navData.getNavElements().forEach { item ->
                    val navBarHeight = getNavigationBarHeight()
                    val isSelected = item.route == navManager?.currentDestinationRoute
                    val isPlay = item.route == Routes.GAMES_SCREEN

                    NavigationBarItem(
                        selected = isSelected,
                        onClick = {
                            selectNavItem(item, navManager)
                        },
                        colors = NavigationBarItemDefaults.colors(
                            indicatorColor = MaterialTheme.colorScheme.surface
                        ),
                        modifier = Modifier
                            .height(48.dp + navBarHeight)
                            .offset(0.dp, (-4).dp - (navBarHeight * 0.25f)),
                        label = {
                            Text(
                                text = item.title,
                                textAlign = TextAlign.Center,
                                color = getColor(isSelected || (isPlay && destContestScreen)),
                                style = MaterialTheme.typography.labelMedium,
                                modifier = Modifier.offset(0.dp, (-4).dp)
                            )
                        },
                        icon = {
                            BadgedBox(
                                modifier = Modifier
                                    .align(alignment = Alignment.Bottom),
                                badge = {
                                    val notificationCount: Int? = when(item.route){
                                        Routes.GAMES_SCREEN -> navState.notificationCountPlay
                                        Routes.GOALS_SCREEN -> navState.notificationCountGoals
                                        Routes.SCORES_SCREEN -> navState.notificationCountScores
                                        else -> null
                                    }
                                    if (notificationCount != null && notificationCount > 0) {
                                        Badge(
                                            containerColor = Color(0xFF005FC8),
                                            contentColor = MaterialTheme.colorScheme.onPrimary,
                                            modifier = Modifier
                                                .align(Alignment.Center)
                                                .size(16.dp)
                                                .offset(3.dp, (-5).dp)
                                        ) {
                                            Text(
                                                text = if (notificationCount >= 10) {
                                                    STR(R.string.ten_plus)
                                                } else {
                                                    notificationCount.toString()
                                                },
                                                modifier = Modifier
                                                    .align(Alignment.CenterVertically)
                                                    .requiredWidth(20.dp),
                                                softWrap = false,
                                                textAlign = TextAlign.Center,
                                                overflow = TextOverflow.Visible,
                                                style = MaterialTheme.typography.labelSmall,
                                            )
                                        }
                                    }
                                }
                            ) {
                                val icon = item.icon?: (if (item.route == Routes.ACCOUNT_SCREEN && navState.accountValueHidden) R.drawable.ic_account else null)
                                Box (modifier = Modifier.height(18.dp)) {
                                    if (icon != null) {
                                        //item.icon should be of format: R.drawable.icon_name
                                        Image(
                                            painter = painterResource(id = icon),
                                            contentDescription = item.title,
                                            modifier = Modifier
                                                .size(18.dp)
                                                .align(Alignment.Center),
                                            colorFilter = ColorFilter.tint(
                                                getColor(isSelected =
                                                    isSelected || (isPlay && destContestScreen)
                                                )
                                            )
                                        )
                                    } else if (item.route == Routes.ACCOUNT_SCREEN) {
                                        if(isWalletValid){
                                            Text(
                                                text = wallet.balance.toDollarWithFanduelFormatting(),
                                                color = getColor(isSelected),
                                                textAlign = TextAlign.Center,
                                                maxLines = 1,
                                                style = MaterialTheme.typography.bodySmall,
                                                modifier = Modifier
                                                    .align(Alignment.Center)
                                                    .fillMaxWidth()
                                            )
                                        }else{
                                            CircularProgressIndicator(
                                                modifier = Modifier.width(20.dp),
                                                color = Color.Gray,
                                                trackColor = MaterialTheme.colorScheme.surfaceVariant,
                                                strokeCap = StrokeCap.Round
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    )
                }
            }
        }
    }
}

fun selectNavItem(item: NavigationDataModel, navManager: NavManager?) {
    if (item == null || navManager == null)
        return

    // Check if mock user is trying to access Account screen
    if (item.route == Routes.ACCOUNT_SCREEN && MockModeManager.instance.isMockModeEnabled) {
        // Disable mock mode and logout
        MockModeManager.instance.disableMockMode()
        EventBus.getDefault().post(OnSessionChangedEvent(false, "NavBar/MockUserAccountClick"))
        HapticsManager.instance.playEffect(HapticFeedbackType.MEDIUM)
        return
    }

    val isContest = navManager.currentDestinationRoute == Routes.CONTEST_SCREEN
    val isSelected = item.route == navManager.currentDestinationRoute
    val isPlay = item.route == Routes.GAMES_SCREEN
    var route = item.route

    if (isPlay) {
        if (!isSelected && isContest) {
            navManager.setWasOnContestScreen(false)
        }
        else if (!isSelected && route == Routes.GAMES_SCREEN && navManager.getWasOnContestScreen()) {
            route = Routes.CONTEST_SCREEN
            navManager.setWasOnContestScreen(true)
        }
        else if (isSelected) {
            navManager.setWasOnContestScreen(false)
        }
    }

    //scroll lobby to top when pressing Play button on nav bar while already on play screen
    if(isPlay && isSelected) {
        GamesViewModel.instance.scrollLobbyToTop()
    }

    if (!isSelected) {
        if (route != null) {
            logNavigationTabVisited(route, navManager.currentDestinationRoute)
            navManager.navigate(route)
        }
        HapticsManager.instance.playEffect(HapticFeedbackType.MEDIUM)
    }
}

@Composable
fun getColor(isSelected: Boolean): Color
{
    return if (isSelected)
        Color(0xFF005FC8)
    else
        MaterialTheme.colorScheme.onSurface
}

@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter")
@Preview
@Composable
fun NavBarPreview(){
    AppandroidwwTheme {
        Scaffold(
            bottomBar = {
                NavBar(NavigationData(), navManager = null)
            }
        )
        {}
    }
}

fun logNavigationTabVisited(route: String, previousRoute: String?) {
    when (route) {
        Routes.GOALS_SCREEN -> {
            AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Rewards_Tab_Viewed.value,
                properties = mapOf("Source" to (previousRoute ?: "Unknown"))))
        }
        Routes.SCORES_SCREEN -> {
            AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Scores_Tab_Viewed.value,
                properties = mapOf("Source" to (previousRoute ?: "Unknown"))))
        }
        Routes.GAMES_SCREEN -> {
            AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Play_Tab_Viewed.value,
                properties = mapOf("Source" to (previousRoute ?: "Unknown"))))
        }
        Routes.ACCOUNT_SCREEN -> {
            AnalyticsManager.instance.logEvent(AnalyticsEvent(analyticsEvent = AnalyticsEventName.Account_Tab_Viewed.value,
                properties = mapOf("Source" to (previousRoute ?: "Unknown"))))
        }
    }
}
