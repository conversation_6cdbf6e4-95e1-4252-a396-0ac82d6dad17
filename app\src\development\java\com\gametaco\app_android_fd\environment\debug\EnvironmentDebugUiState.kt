package com.gametaco.app_android_fd.environment.debug

data class EnvironmentDebugUiState(
    val envName: String,
    val title: String,
    val environmentTitle: String,
    val textFieldLabel: String,
    val textFieldPlaceholder: String,
    val currentLabel: String,
    val applyLabel: String,
    val onApplyHostName: (String) -> Unit,
    
    // Hostnames section
    val hostnamesTitle: String,
    val endpointUrl: String,
    val endpointUrlLabel: String,
    val endpointUrlPlaceholder: String,
    val maintenanceUrl: String,
    val maintenanceUrlLabel: String,
    val maintenanceUrlPlaceholder: String,
    val resetButtonLabel: String,
    val onApplyEndpointUrl: (String) -> Unit,
    val onApplyMaintenanceUrl: (String) -> Unit,
    val onResetHostnames: () -> Unit,
)