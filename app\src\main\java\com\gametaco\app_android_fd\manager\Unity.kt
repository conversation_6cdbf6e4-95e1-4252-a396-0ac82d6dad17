package com.gametaco.app_android_fd.manager

import android.content.Context
import com.gametaco.app_android_fd.UnityWrapperActivity
import com.gametaco.app_android_fd.UnityWrapperInterface
import com.gametaco.app_android_fd.data.AppConstants
import com.gametaco.app_android_fd.data.AppEnv
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APITournamentRegisterReplayURLRequest
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.utils.CoroutineScopes
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.viewmodel.GameplayViewModel
import com.gametaco.app_android_fd.viewmodel.OnReLoadGoalsDataEvent
import com.gametaco.app_android_fd.viewmodel.PlayViewModel
import com.gametaco.utilities.ResourceState
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import java.io.File
import java.io.FileInputStream
import java.net.HttpURLConnection
import java.net.URL


class Unity(
    context: Context,
    private val activityManager: ActivityManager,
    private val tournamentManager: TournamentManager,
    private val brazeManager: BrazeManager,
    private val coroutineScopes: CoroutineScopes,
    private val worldWinnerAPI: WorldWinnerAPI,
) : UnityProviding {
    private var state: AppState = AppState.NOT_READY
    private var tournamentManagerDelegate: TournamentManagerDelegate? = null
    private var _isShowingUnity:Boolean = false

    companion object {
        const val TAG = "UnityProviding"
    }

    init {
        state = AppState.READY
    }

    override val containerInfo: String? by lazy {
            (activityManager.unityWrapper as UnityWrapperActivity).containerInfo
        }

    private val unityWrapper: UnityWrapperInterface
        get() = activityManager.unityWrapper
    private val uiManager: UIManager
        get() = UIManager.instance

    override fun loadGame(encodedString: String) {
        Logger.d(TAG, "loadGame: $encodedString")

        if (state != AppState.READY) {
            Logger.d(TAG, "Unity is not ready")
            return
        }

        brazeManager.disableInAppMessagePresenter()
        unityWrapper.resume()
        unityWrapper.UnityNativeAPI_loadGame(encodedString)
    }


    override fun startGame(o: String) {
        Logger.d(TAG, "startGame")

        unityWrapper.UnityNativeAPI_startGameReady(o)
    }

    override fun pauseGame(isPaused: Boolean) {
        Logger.d(TAG, "pauseGame")
        if(isPaused){
            unityWrapper.pause()
        } else {
            unityWrapper.resume()
        }
    }

    override fun resetUnity() {
        Logger.d(TAG, "resetUnity")
        unityWrapper.UnityNativeAPI_reset()
    }


    override fun returnToApp() {
        Logger.d(TAG, "returnToApp")

        brazeManager.enableInAppMessagePresenter()
        tournamentManager.onReturnToContainer()

        FDManager.instance.keepAlive()

        coroutineScopes.main.launch {
            val scoreValid = TournamentManager.instance.waitForScoreValid()
            if(scoreValid) {
                GameplayViewModel.instance.showLeaderboard(false)
            } else {
                NavManager.instance.navigate(Routes.GAMES_SCREEN)
            }
        }
    }

    override fun getReplayFilePath(tournamentInstanceEntryId : String) : String? {
        return unityWrapper.UnityNativeAPI_getReplayFilePath(tournamentInstanceEntryId)
    }

    override fun prepareForReplay(tournamentInstanceEntryID: String?) {
        Logger.i(TAG, "prepareForReplay: tournamentInstanceEntryID: $tournamentInstanceEntryID")
    }

    override fun uploadReplay(tournamentInstanceEntryID: String?, replayFilePath: String?, isUserViewable: Boolean) {
        Logger.i(TAG, "uploadReplay: tournamentInstanceEntryID: $tournamentInstanceEntryID, replayFilePath: $replayFilePath")

        coroutineScopes.io.launch {

            try{
                //get the s3 url from WW api
                val s3URLResponse = worldWinnerAPI.getReplayUploadURL(tournamentInstanceEntryID!!, isUserViewable)

                //upload if success
                if(s3URLResponse.isSuccess()) {
                    val responseData = (s3URLResponse as ResourceState.Success).data
                    val s3Path = responseData.presigned_url

                    if(s3Path != null) {
                        val success = uploadReplayToS3(replayFilePath, s3Path)

                        if (success) {
                            //register upload url
                            worldWinnerAPI.postRegisterReplayUploadURL(
                                APITournamentRegisterReplayURLRequest(tournamentInstanceEntryID!!)
                            )

                            AnalyticsManager.instance.logEvent(
                                AnalyticsEvent(
                                    analyticsEvent = AnalyticsEventName.Replay_Upload_Success.value,
                                )
                            )
                            
                        }
                    }
                } else if(s3URLResponse.isError()){

                    AnalyticsManager.instance.logEvent(
                        AnalyticsEvent(
                            analyticsEvent = AnalyticsEventName.Replay_Upload_Failure.value,
                            properties = mapOf(
                                "details" to (s3URLResponse as ResourceState.Error).error
                            )
                        )
                    )

                }



            } catch(exception: Exception)
            {
                Logger.i(TAG, "uploadReplay Exception: ${exception.message}")

                AnalyticsManager.instance.logEvent(
                    AnalyticsEvent(
                        analyticsEvent = AnalyticsEventName.Replay_Upload_Failure.value,
                        properties = mapOf(
                            "details" to "s3 upload error: " + exception.message
                        )
                    )
                )
            }
        }

    }

    override fun showUnityFromAction(actionType: String) {
        if (state != AppState.READY) {
            Logger.d(TAG, "Unity is not ready")
            return
        }
        val jsonString = createUnityActionJson(actionType)
        coroutineScopes.main.launch {
//            state = AppState.NOT_READY
            _isShowingUnity = true
            unityWrapper.hideLauncherView()
//            println("?>>>>>>>> ${jsonString}")
            unityWrapper.UnityNativeAPI_appAction(jsonString)
        }

    }
    fun createUnityActionJson(type:String?):String{
        val event = JSONObject()
        event.put("Type",type)
        event.put("ApplicationVersionId", AppConstants.API_APP_VERSION)
        event.put("DeviceNativeId",DeviceManager.instance.getDeviceId())
        event.put("AuthToken",AuthenticationManager.instance.authToken)
        event.put("FDAuthToken",FDManager.instance.sessionDataToken)
        event.put("ApiHostname",AppEnv.current.apiEndpointHostname)

        if(type == "daily"){
            val dailyRewardExperiment = ExperimentManager.instance.dailyRewardExperiment
            event.put("DailyRewardVariant",dailyRewardExperiment?.cachedVariant?.value ?:"")
            if(dailyRewardExperiment?.isAvailable() == true){
                dailyRewardExperiment.trackExposure()
            }
            PreferencesManager.instance.setDailyRewardSeen(true)
        }
        return event.toString()
    }

    override fun getDailyRewardFutureTime(): String? {
        return unityWrapper.UnityNativeAPI_getDailyRewardFutureTime()
    }

    override fun setTournamentManagerDelegate(delegate: TournamentManagerDelegate) {
        this.tournamentManagerDelegate = delegate
    }

    override fun hideUnity(appNavigationInfo: String?, error: String?) {
        _isShowingUnity = false
        unityWrapper.showLauncherView()

        if(appNavigationInfo != null){
            Logger.d("hide Unity with params: ${appNavigationInfo}")
            if(appNavigationInfo.isNotEmpty() && appNavigationInfo != "{}"){
                processHideUnityParams(appNavigationInfo)
            }
        }else{
            Logger.d("hide Unity with no params")
        }
        if(error != null){
            if(error.isNotEmpty()){
                Logger.d("hide Unity with error: ${error}")
                AlertDialogManager.instance.showDialog("Sorry!","Something went wrong on our end. Please try again or check back later.","OK",{})
            }
        }
    }

    override fun isShowingUnity(): Boolean {
        return _isShowingUnity
    }

    private fun processHideUnityParams(jsonString:String){
        try {
            val moshi = Moshi.Builder().add(KotlinJsonAdapterFactory()).build()
            val type = Types.newParameterizedType(Map::class.java, String::class.java, Any::class.java)
            val adapter = moshi.adapter<Map<String, Any>>(type)
            val jsonObject = adapter.fromJson(jsonString)
            if(jsonObject != null){
                val navigationType = jsonObject.get("navigationType") as String?
                if(navigationType == "rewards"){
                    EventBus.getDefault().post(OnReLoadGoalsDataEvent(true))
                }else if(navigationType == "game"){
                    //sample data:{"navigationType":"game","gameName":"puzzlepyramidskt3prime","tournamentID":"0197a98c-457f-761e-bf7a-0c8ead068920"}
                    EventBus.getDefault().post(OnReLoadGoalsDataEvent(true))
                    val gameName = jsonObject.get("gameName") as String?
                    val tournamentId = jsonObject.get("tournamentID") as String?
                    if(gameName != null){
                        PlayViewModel.instance.jumpToPlayerViewByName(gameName,tournamentId)
                    }
                }else{
                    Logger.d("Unknown navigation type: ${navigationType}")
                }
            }else{
                Logger.d("Unknown jsonObject")
            }

        }catch (e:Exception){
            Logger.e(TAG,"processHideUnityParams error: ${e.message}")
        }
    }

    suspend fun uploadReplayToS3(localPath : String?, s3Path : String?) : Boolean {

        if(s3Path == null || localPath == null) {
            Logger.i(TAG, "uploadReplayToS3: paths invalid s3Path:${s3Path}localPath:${localPath}")

            return false
        }

        try {
            val file = File(localPath)

            // Open a connection to the signed URL
            val url = URL(s3Path)
            val connection = url.openConnection() as HttpURLConnection

            // Configure the connection for a PUT request
            connection.requestMethod = "PUT"
            connection.doOutput = true
            connection.setRequestProperty("Content-Type", "video/mp4")
            connection.connectTimeout = 15000
            connection.readTimeout = 15000
            connection.setFixedLengthStreamingMode(file.length())
            connection.instanceFollowRedirects = false

            // Open streams to transfer the file
            FileInputStream(file).use { fileInputStream ->
                connection.outputStream.use { outputStream ->
                    fileInputStream.copyTo(outputStream)
                }
            }

            // Trigger the request and get the response
            val responseCode = connection.responseCode
            if (responseCode in 200..299) {
                Logger.i(TAG, "uploadReplayToS3: Upload succeeded with response code: $responseCode")
                return true
            } else {
                Logger.i(TAG, "uploadReplayToS3: Upload failed with response code: $responseCode")

                AnalyticsManager.instance.logEvent(
                    AnalyticsEvent(
                        analyticsEvent = AnalyticsEventName.Replay_Upload_Failure.value,
                        properties = mapOf(
                            "details" to "s3 upload error: " + connection.responseCode
                        )
                    )
                )

                return false
            }
        } catch(exception : Exception) {
            Logger.i(TAG, "uploadReplayToS3: Exception ${exception.message}")

            AnalyticsManager.instance.logEvent(
                AnalyticsEvent(
                    analyticsEvent = AnalyticsEventName.Replay_Upload_Failure.value,
                    properties = mapOf(
                        "details" to "s3 upload error: " + exception.message
                    )
                )
            )

            return false
        }
        return false
    }


    enum class AppState {
        NOT_READY, READY
    }
}

