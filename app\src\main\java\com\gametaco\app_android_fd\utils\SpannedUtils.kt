package com.gametaco.app_android_fd.utils

import android.graphics.Typeface
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.text.style.BackgroundColorSpan
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.text.style.URLSpan
import android.text.style.UnderlineSpan
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.sp


fun Spanned.toAnnotatedString(): AnnotatedString {
    val spanned = this
    val builder = AnnotatedString.Builder(spanned.toString())

    val spans = spanned.getSpans(0, spanned.length, Any::class.java)
    for (span in spans) {
        val start = spanned.getSpanStart(span)
        val end = spanned.getSpanEnd(span)

        if (start >= end) continue

        when (span) {
            is StyleSpan -> {
                when (span.style) {
                    Typeface.BOLD -> builder.addStyle(
                        SpanStyle(fontWeight = FontWeight.Bold),
                        start,
                        end
                    )
                    Typeface.ITALIC -> builder.addStyle(
                        SpanStyle(fontStyle = FontStyle.Italic),
                        start,
                        end
                    )
                }
            }

            is UnderlineSpan -> builder.addStyle(
                SpanStyle(textDecoration = TextDecoration.Underline),
                start,
                end
            )

            is StrikethroughSpan -> builder.addStyle(
                SpanStyle(textDecoration = TextDecoration.LineThrough),
                start,
                end
            )

            is ForegroundColorSpan -> builder.addStyle(
                SpanStyle(color = Color(span.foregroundColor)),
                start,
                end
            )

            is BackgroundColorSpan -> builder.addStyle(
                SpanStyle(background = Color(span.backgroundColor)),
                start,
                end
            )

            is AbsoluteSizeSpan -> builder.addStyle(
                SpanStyle(fontSize = span.size.sp),
                start,
                end
            )

            is RelativeSizeSpan -> {
                val text = spanned.subSequence(start, end).toString()
                val defaultSize = 16.sp // default size fallback
                builder.addStyle(
                    SpanStyle(fontSize = (defaultSize.value * span.sizeChange).sp),
                    start,
                    end
                )
            }

            is URLSpan -> {
                builder.addStyle(
                    SpanStyle(color = Color.Blue, textDecoration = TextDecoration.Underline),
                    start,
                    end
                )
                builder.addStringAnnotation(
                    tag = "URL",
                    annotation = span.url,
                    start = start,
                    end = end
                )
            }
        }
    }

    return builder.toAnnotatedString()
}