package com.gametaco.app_android_fd.data

import com.fanduel.coremodules.config.contract.AppDomain
import com.fanduel.coremodules.config.contract.Country
import com.fanduel.coremodules.config.contract.Environment
import com.gametaco.app_android_fd.data.AppConstants.API_MAINTENANCE_URL_PROD

enum class ENV{
    DEV,
    QA,
    PROD
}

val ENV.isProd: Boolean
    get() = this == ENV.PROD
val ENV.isNonProd: Boolean
    get() = !isProd

data class EnvConfig(
    val env: ENV,
    val envHost: EnvironmentSpec?,
    val api_endpoint:String,
    val api_app_id:String,
    val app_version:String,
    val braze_api_key:String,
    val braze_api_endpoint:String,
    val sift_account_id:String,
    val sift_beacon_key:String,
    val amplitude_api_key:String,
    val amplitude_experiments_key:String,
    val amplitude_app_id:String,
    val fanduel_account_url:String,
    val firebase_cloud_messaging_sender_id:String,
    val fanduel_environment:Environment,
    val fanduel_appdomain : AppDomain,
    val maintenanceURL:String,
    val fanduel_salesforce_orgId : String,
    val fanduel_salesforce_deploymentId : String,
    val fanduel_salesforce_buttonId : String,
    val fanduel_salesforce_liveAgentPod : String,
) {
    val apiEndpointHostname: String
        get() = api_endpoint.removePrefix("https://").removePrefix("http://").substringBefore("/")
}

object EnvConfigs{


    val PROD:EnvConfig = EnvConfig(
        env = ENV.PROD,
        envHost = null,
        api_endpoint = AppConstants.API_BASE_URL_PROD,
        api_app_id = AppConstants.API_APP_ID,
        app_version = AppConstants.API_APP_VERSION,
        braze_api_endpoint = AppConstants.BRAZE_API_ENDPOINT_PROD,
        braze_api_key = AppConstants.BRAZE_API_KEY_PROD,
        sift_account_id = AppConstants.SIFT_ACCOUNT_ID_PROD,
        sift_beacon_key = AppConstants.SIFT_BEACON_KEY_PROD,
        amplitude_api_key = AppConstants.AMPLITUDE_API_KEY_PROD,
        amplitude_app_id = AppConstants.AMPLITUDE_APP_ID_PROD,
        amplitude_experiments_key = AppConstants.AMPLITUDE_EXPERIMENTS_KEY_PROD,
        fanduel_account_url = AppConstants.FANDUEL_BASE_URL_PROD,
        firebase_cloud_messaging_sender_id = AppConstants.FIREBASE_CLOUD_MESSAGING_SENDER_ID,
        fanduel_environment = Environment.Prod(Country.US),
        fanduel_appdomain = AppDomain.SkilledGames("us"),
        maintenanceURL = API_MAINTENANCE_URL_PROD,
        fanduel_salesforce_orgId = "00D5Y000002UY4T",
        fanduel_salesforce_deploymentId = "5725Y000000QSWF",
        fanduel_salesforce_buttonId = "5735Y000000QSsW",
        fanduel_salesforce_liveAgentPod = "d.la3-c2-ia5.salesforceliveagent.com",
    )
}
class AppEnv {
    companion object {
        val current:EnvConfig
            get() = EnvConfigProviderImpl.envConfig
    }
}
