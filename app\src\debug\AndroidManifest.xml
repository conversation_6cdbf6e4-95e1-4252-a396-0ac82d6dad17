<?xml version="1.0" encoding="utf-8"?>
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permission for debugging SSL traffic -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" tools:node="remove" />

    <application
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="n">

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>


    </application>
</manifest>