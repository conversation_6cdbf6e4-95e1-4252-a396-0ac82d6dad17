<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Debug configuration to allow SSL inspection -->
    <debug-overrides>
        <trust-anchors>
            <!-- Trust preinstalled CAs -->
            <certificates src="system" />
            <!-- Trust user-installed CAs (for <PERSON>, <PERSON><PERSON><PERSON>, etc.) -->
            <certificates src="user" />
        </trust-anchors>
    </debug-overrides>
    
    <!-- Base configuration -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system" />
            <!-- Also trust user CAs in debug builds -->
            <certificates src="user" />
        </trust-anchors>
    </base-config>
    
    <!-- Domain-specific configurations to disable pinning for API endpoints -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- Add your API domains here -->
        <domain includeSubdomains="true">api.ww.prod.fanduel.com</domain>
        <domain includeSubdomains="true">api.ww.stage.fanduel.com</domain>
        <domain includeSubdomains="true">api.ww.dev.fanduel.com</domain>
        <domain includeSubdomains="true">fanduel.com</domain>
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">fdbox.net</domain>
        <trust-anchors>
            <certificates src="system" />
            <certificates src="user" />
        </trust-anchors>
        <!-- Disable certificate pinning -->
        <pin-set expiration="2099-12-31">
            <!-- Empty pin-set disables pinning -->
        </pin-set>
    </domain-config>
</network-security-config>