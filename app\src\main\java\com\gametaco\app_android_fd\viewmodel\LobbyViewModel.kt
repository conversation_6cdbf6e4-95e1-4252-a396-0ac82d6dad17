package com.gametaco.app_android_fd.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.gametaco.app_android_fd.data.api.WorldWinnerAPI
import com.gametaco.app_android_fd.data.entity.APICatalog
import com.gametaco.app_android_fd.data.entity.APICatalogGameItem
import com.gametaco.app_android_fd.data.entity.APICatalogSection
import com.gametaco.app_android_fd.data.entity.APICatalogSectionTypeEnum
import com.gametaco.app_android_fd.data.entity.APICatalogSwimlaneData
import com.gametaco.app_android_fd.data.entity.APICatalogSwimlaneGamesLayoutEnum
import com.gametaco.app_android_fd.data.entity.APICatalogVersion
import com.gametaco.app_android_fd.data.entity.APIGamesResponse
import com.gametaco.app_android_fd.data.entity.APIGamesResponseGame
import com.gametaco.app_android_fd.data.entity.APITournament
import com.gametaco.app_android_fd.data.entity.APITournamentsResponse
import com.gametaco.app_android_fd.data.entity.GCGameData
import com.gametaco.app_android_fd.data.entity.GameCatalogResponse
import com.gametaco.app_android_fd.data.navigation.Routes
import com.gametaco.app_android_fd.manager.AlertDialogManager
import com.gametaco.app_android_fd.manager.AuthenticationManager
import com.gametaco.app_android_fd.manager.ErrorManager
import com.gametaco.app_android_fd.manager.NavManager
import com.gametaco.app_android_fd.manager.PreferencesManager
import com.gametaco.app_android_fd.manager.TournamentManager
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEvent
import com.gametaco.app_android_fd.manager.analytics.AnalyticsEventName
import com.gametaco.app_android_fd.manager.analytics.AnalyticsManager
import com.gametaco.app_android_fd.models.LobbyNewUiState
import com.gametaco.app_android_fd.models.LobbyUiState
import com.gametaco.app_android_fd.utils.log.Logger
import com.gametaco.app_android_fd.viewmodel.GamesViewModel.Companion.TAG
import com.gametaco.app_android_fd.viewmodel.GamesViewModel.UiEvent
import com.gametaco.utilities.ResourceState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class LobbyViewModel: ViewModel() {
    init {
        Logger.d(TAG, "Init LobbyViewModel")
    }
    companion object {
        const val TAG = "LobbyViewModel"
        val instance: LobbyViewModel by lazy { LobbyViewModel() }
    }
    private val tournamentManager: TournamentManager by lazy {
        TournamentManager.instance
    }
    // Define UI events
    sealed class UiEvent {
        object ScrollToTop : UiEvent()
        // Add other events if needed
    }
    private val _uiEvent = Channel<com.gametaco.app_android_fd.viewmodel.GamesViewModel.UiEvent>(
        Channel.BUFFERED)
    val uiEvent = _uiEvent.receiveAsFlow()

    // Function to trigger scroll to top
    fun scrollLobbyToTop() {
        _uiEvent.trySend(com.gametaco.app_android_fd.viewmodel.GamesViewModel.UiEvent.ScrollToTop)
    }

    private val _firstTimeLoad = MutableStateFlow(true)
    private val dataReady = MutableStateFlow(false)

    fun loadData(){
        viewModelScope.launch (Dispatchers.IO) {
            getRecentGames()
            getGameCatalog()
            getGamesData()
            populateGamesDataDictionary()
            _firstTimeLoad.value = false
            dataReady.value = true
        }
    }
    private val _recentlyPlayedGames = MutableStateFlow<List<String>>(listOf())
    val recentlyPlayedGames = _recentlyPlayedGames.asStateFlow()

    private val _gameCatalog : MutableStateFlow<ResourceState<APICatalog>> = MutableStateFlow(ResourceState.Loading())
    private val gameCatalog : StateFlow<ResourceState<APICatalog>> = _gameCatalog

    private val _gameCatalogVersion: MutableStateFlow<APICatalogVersion?> = MutableStateFlow(null)
    private val gameCatalogVersion: APICatalogVersion?
        get() = _gameCatalogVersion.value


    private val _gamesData : MutableStateFlow<ResourceState<APIGamesResponse>> = MutableStateFlow(ResourceState.Loading())
    private val gamesData : StateFlow<ResourceState<APIGamesResponse>> = _gamesData

    private val gamesDataDictionary: MutableMap<String, APIGamesResponseGame> = mutableMapOf()

    private val _featuredTournaments:MutableStateFlow<MutableMap<String, APITournament>> = MutableStateFlow(mutableMapOf())
    val featuredTournaments:StateFlow<MutableMap<String, APITournament>> = _featuredTournaments.asStateFlow()

    private val isGuest get() = AuthenticationManager.instance.isGuest
    private val isFtue get() = AuthenticationManager.instance.showLobbyFtue

    private val currentTournamentData = MutableStateFlow<APITournament?>(null)
    private val infoModalExpanded = MutableStateFlow(false)
    private val gameModalExpanded = MutableStateFlow(false)

    private val selectedGameData: MutableStateFlow<APIGamesResponseGame?> = MutableStateFlow(null)
    private val selectedGameCatalogData: MutableStateFlow<GCGameData?> = MutableStateFlow(null)


    private suspend fun getRecentGames(){
        val res = WorldWinnerAPI.instance.getRecentPlayedGames()
        if(res is ResourceState.Success){
            _recentlyPlayedGames.value= res.data.results
        }
    }
    private suspend fun updateGameCatalogVersion():Boolean{
        val currentVersion: APICatalogVersion? = _gameCatalogVersion.value

        val res = WorldWinnerAPI.instance.getHomeCatalogVersion()
        if(res is ResourceState.Success){
            val data: APICatalogVersion = res.data
            _gameCatalogVersion.value = data

            if (currentVersion == null || currentVersion != data) {
                return true
            }
        }
        return false
    }
    private suspend fun getGameCatalog() {
        updateGameCatalogVersion()
        val gameCatalogVersion = gameCatalogVersion
        val cachedGameCatalogVersion = PreferencesManager.instance.getCachedNewGameCatalogVersion()
        val catalogCache = PreferencesManager.instance.getNewCatalogCache()
        val update = gameCatalogVersion == null
                || cachedGameCatalogVersion != gameCatalogVersion
                || catalogCache == null

        if (true) {
            Logger.d(TAG, "Fetching new catalog")
            WorldWinnerAPI.instance.getHomeCatalog()
                .collectLatest { gamesCatalogResponse ->
                    if (_firstTimeLoad.value) {
                        _gameCatalog.value = gamesCatalogResponse
                    }
                    when (gamesCatalogResponse) {
                        is ResourceState.Loading -> {
                        }
                        is ResourceState.Error -> {
                            if(!ErrorManager.instance.handleResourceStateError(gamesCatalogResponse)) {
                                AlertDialogManager.instance.showDialog("Error", gamesCatalogResponse.error, "Confirm", {} )
                            }

                            Logger.e(TAG, "getGameCatalog is not success, not calling UtilityManager.instance.updateGameData")
                        }
                        is ResourceState.Success -> {
                            if(!_firstTimeLoad.value){
                                _gameCatalog.value = gamesCatalogResponse
                            }
                            PreferencesManager.instance.setNewCatalogCache(
                                data = gamesCatalogResponse.data,
                                gameCatalogVersion = requireNotNull(this.gameCatalogVersion),
                            )
                            tournamentManager.updateGameData(gamesCatalogResponse.data.data.games)
                        }
                    }
                }
        } else {
            requireNotNull(catalogCache) { "Catalog cache should not be null" }
            _firstTimeLoad.value = false
            _gameCatalog.value = ResourceState.Success(catalogCache)
            tournamentManager.updateGameData(catalogCache.data.games)
            Logger.d(TAG, "Using cached catalog")
        }
    }
    private suspend fun getGamesData(){
        val gamesDataResponse = WorldWinnerAPI.instance.getGames()
        if(_firstTimeLoad.value){
            _gamesData.value = gamesDataResponse
        }
        when (gamesDataResponse) {
            is ResourceState.Loading -> {
            }
            is ResourceState.Error -> {
                if(!ErrorManager.instance.handleResourceStateError(gamesDataResponse)) {
                    AlertDialogManager.instance.showDialog(
                        "Error",
                        gamesDataResponse.error,
                        "Confirm",
                        {})
                }
            }
            is ResourceState.Success -> {
                if(!_firstTimeLoad.value){
                    _gamesData.value = gamesDataResponse
                }
                PreferencesManager.instance.setAvailableGamesCache(gamesDataResponse.data)
            }
        }
    }
    private suspend fun populateGamesDataDictionary(){
        gamesDataDictionary.clear()

        if(_gamesData.value is ResourceState.Success){
            val data = (_gamesData.value as ResourceState.Success).data
            for (gameData in data.results){
                gamesDataDictionary[gameData.id!!] = gameData
            }
        }


        if(_gameCatalog.value is ResourceState.Success){
            val catalog = (_gameCatalog.value as ResourceState.Success).data
            refreshFeaturedTournaments(catalog)
        }
    }
    fun generateRecentlyPlayedSection(raw:APICatalog):APICatalogSection{
        val games = mutableListOf<APICatalogGameItem>()
        _recentlyPlayedGames.value.forEachIndexed { index, id ->
            if(gamesDataDictionary.containsKey(id)){
//                val gameData = gamesDataDictionary[id]
                val cgData = raw.data.games.values.firstOrNull { it.game_id == id }
                if(cgData != null){
                    games.add(APICatalogGameItem(
                        game_meta_datum_id = cgData.id,
                        display_order = index,
                        banner = null
                    ))
                }
            }
        }
        val swimlaneData = APICatalogSwimlaneData(
            title =" Recently Played",
            show_title = false,
            display_order = 0,
            games_layout = APICatalogSwimlaneGamesLayoutEnum.HORIZONTAL,
            is_new = false,
            spotlight_item = null,
            filter_pill_title = null,
            tournament_items = listOf(),
            game_items = games
        )
        val newSection = APICatalogSection(
            title = "Recently Played",
            section_type = APICatalogSectionTypeEnum.RECENTLY_PLAYED,
            is_collapsible = true,
            display_order = 0,
            swimlanes = listOf(swimlaneData),
            protip_items = listOf()
        )
        return newSection
    }
    private suspend fun refreshFeaturedTournaments(catalog:APICatalog) {
        val tournamentIds = mutableSetOf<String>()
        catalog.sections.forEach { section ->
            section.swimlanes.forEach{ swimlane ->
                swimlane.tournament_items.forEach {
                    tournamentIds.add(it.tournament_id)
                }
            }
        }

        val ids = tournamentIds.joinToString(",")

        viewModelScope.launch (Dispatchers.IO){
            val tournamentsDataResponse = WorldWinnerAPI.instance.getTournamentsByTournamentIDs(ids)
            updateFeaturedTournaments(tournamentsDataResponse)
        }
    }
    private fun updateFeaturedTournaments(response : ResourceState<APITournamentsResponse>){
        if(response.isSuccess()){
            val responseData = (response as ResourceState.Success).data
            val dic = mutableMapOf<String, APITournament>()
            for (tournament in responseData.results){
                dic[tournament.id] = tournament
                if(tournament.game_id != null) {
                    tournamentManager.addTournamentsIdsForGameId(
                        tournament,
                        tournament.game_id
                    )
                }
            }
            _featuredTournaments.value = dic
        } else if (response.isError()) {
            if(!ErrorManager.instance.handleResourceStateError(response)) {
                AlertDialogManager.instance.showDialog(
                    "Error",
                    (response as ResourceState.Error).error,
                    "Confirm",
                    {})
            }
        }
    }
    fun getGameDataById(
        id: String
    ): APIGamesResponseGame? {
        if (gamesDataDictionary.containsKey(id)){
            return gamesDataDictionary[id]
        }

        return null
    }

    fun getGameDateByName(gameName:String):APIGamesResponseGame?{
        return gamesDataDictionary.values.firstOrNull { it.name == gameName }
    }
    private fun onLobbyUiInfoClick(tournamentData: APITournament, gameData: GCGameData?) {
        currentTournamentData.value = tournamentData
        infoModalExpanded.value = true
        selectedGameData.value = getGameDataById(
            gameData?.game_id ?: ""
        )
    }
    private fun onGameTileClicked(
        navManager: NavManager,
        gameData: GCGameData,
        lobbySelection: String?,
    ) {
        val apiGamesResponseGame = getGameDataById(gameData.game_id)
        selectedGameCatalogData.value = gameData
        selectedGameData.value = apiGamesResponseGame

        PlayViewModel.instance.setGameData(
            gameData = gameData,
            game = apiGamesResponseGame,
        )

        if (isFtue) {
            gameModalExpanded.value = true
        } else {
            navManager.navigate(Routes.CONTEST_SCREEN)

            AnalyticsManager.instance.logEvent(
                AnalyticsEvent(
                    analyticsEvent = AnalyticsEventName.Game_Selected.value,
                    properties = mapOf(
                        "Game Name" to (apiGamesResponseGame?.name ?: "Unknown"),
                        "Lobby Selection" to (lobbySelection ?: "Unknown"),
                    )
                )
            )
        }
    }
    fun resetLobbyUiState() {
        currentTournamentData.value = null
        infoModalExpanded.value = false
        selectedGameData.value = null
        selectedGameCatalogData.value = null
    }

    val lobbyUiState: StateFlow<LobbyNewUiState> = combine(
        gameCatalog,
        gamesData,
        dataReady,
    ) { catalogState, dataState, ready ->
        when {
            catalogState is ResourceState.Loading || dataState is ResourceState.Loading ->
                LobbyNewUiState.Loading
            catalogState is ResourceState.Error -> LobbyNewUiState.Error(catalogState.error)
            dataState is ResourceState.Error -> LobbyNewUiState.Error(dataState.error)
            !ready -> LobbyNewUiState.Loading
            catalogState is ResourceState.Success && dataState is ResourceState.Success ->
                LobbyNewUiState.Success(
                    catalogState.data,
                    dataState.data,
                    currentTournamentData = currentTournamentData,
                    selectedGameData = selectedGameData,
                    infoModalExpanded = infoModalExpanded,
                    gameModalExpanded = gameModalExpanded,
                    onInfoClick = ::onLobbyUiInfoClick,
                    onGameTileClicked = ::onGameTileClicked,
                )
            else -> LobbyNewUiState.Error("Unknown state")
        }
    }
        .stateIn(viewModelScope, SharingStarted.Eagerly, LobbyNewUiState.Loading)
}