name: Build and Upload (QA and Production)

on:
  workflow_dispatch:

jobs:
  build_both_and_upload:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger QA Build
        uses: the-actions-org/workflow-dispatch@v4
        with:
          workflow: Build and Upload to S3
          ref: ${{ github.ref_name }}
          token: ${{ secrets.GITHUB_TOKEN }}
          inputs: '{"buildType": "QA"}'
          wait-for-completion: true

      - name: Trigger Production Build
        uses: the-actions-org/workflow-dispatch@v4
        with:
          workflow: Build and Upload to S3
          ref: ${{ github.ref_name }}
          token: ${{ secrets.GITHUB_TOKEN }}
          inputs: '{"buildType": "Production"}'
          wait-for-completion: true
