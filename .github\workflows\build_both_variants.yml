name: Build and Upload Both Env Variants

on:
  workflow_dispatch:
    inputs:
      variant:
        description: 'App variant to build'
        required: true
        type: choice
        options:
          - Default
          - Games

jobs:
  build_both_variants:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger QA Build
        uses: the-actions-org/workflow-dispatch@v4
        with:
          workflow: Build and Upload Single Env Variants
          ref: ${{ github.ref_name }}
          token: ${{ secrets.GITHUB_TOKEN }}
          inputs: '{"buildType": "QA", "variant": "${{ github.event.inputs.variant }}"}'
          wait-for-completion: true

      - name: Trigger Production Build
        uses: the-actions-org/workflow-dispatch@v4
        with:
          workflow: Build and Upload Single Env Variants
          ref: ${{ github.ref_name }}
          token: ${{ secrets.GITHUB_TOKEN }}
          inputs: '{"buildType": "Production", "variant": "${{ github.event.inputs.variant }}"}'
          wait-for-completion: true